<?php
/**
 * Enhanced Notification Functions
 * Provides comprehensive notification management with support for multiple tables
 */

/**
 * Get all notifications from both tables for a user
 */
function getAllUserNotifications($conn, $user_id, $limit = 50) {
    $notifications = [];
    
    // Get from order_notifications table
    try {
        $stmt = $conn->prepare("
            SELECT 
                COALESCE(notification_id, id) as id,
                'order_notifications' as source_table,
                COALESCE(title, 'Order Update') as title,
                message,
                COALESCE(type, 'order') as type,
                order_id,
                COALESCE(status_logo, '📦') as icon,
                is_read,
                created_at,
                DATE_FORMAT(created_at, '%d %M %Y %H:%i') as formatted_date
            FROM order_notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC
        ");
        $stmt->execute([$user_id]);
        $order_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($order_notifications as $notif) {
            $notif['priority'] = 'high'; // Order notifications are high priority
            $notifications[] = $notif;
        }
    } catch (Exception $e) {
        error_log("Error fetching order notifications: " . $e->getMessage());
    }
    
    // Get from notifications table
    try {
        $stmt = $conn->prepare("
            SELECT 
                notification_id as id,
                'notifications' as source_table,
                title,
                message,
                COALESCE(type, 'general') as type,
                NULL as order_id,
                '🔔' as icon,
                is_read,
                created_at,
                DATE_FORMAT(created_at, '%d %M %Y %H:%i') as formatted_date
            FROM notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC
        ");
        $stmt->execute([$user_id]);
        $general_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($general_notifications as $notif) {
            $notif['priority'] = 'normal';
            $notifications[] = $notif;
        }
    } catch (Exception $e) {
        error_log("Error fetching general notifications: " . $e->getMessage());
    }
    
    // Sort by created_at descending
    usort($notifications, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    
    return array_slice($notifications, 0, $limit);
}

/**
 * Get total unread count from both tables
 */
function getTotalUnreadCount($conn, $user_id) {
    $total = 0;
    
    // Count from order_notifications
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM order_notifications WHERE user_id = ? AND is_read = FALSE");
        $stmt->execute([$user_id]);
        $total += $stmt->fetchColumn();
    } catch (Exception $e) {
        error_log("Error counting order notifications: " . $e->getMessage());
    }
    
    // Count from notifications
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
        $stmt->execute([$user_id]);
        $total += $stmt->fetchColumn();
    } catch (Exception $e) {
        error_log("Error counting general notifications: " . $e->getMessage());
    }
    
    return $total;
}

/**
 * Mark a single notification as read
 */
function markSingleNotificationRead($conn, $notification_id, $user_id, $table = 'order_notifications') {
    try {
        $id_column = ($table === 'order_notifications') ? 
            (tableHasColumn($conn, $table, 'notification_id') ? 'notification_id' : 'id') : 
            'notification_id';
            
        $stmt = $conn->prepare("
            UPDATE {$table} 
            SET is_read = TRUE 
            WHERE {$id_column} = ? AND user_id = ?
        ");
        
        return $stmt->execute([$notification_id, $user_id]);
    } catch (Exception $e) {
        error_log("Error marking notification as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark all notifications as read for a user
 */
function markAllNotificationsRead($conn, $user_id) {
    $success = true;
    
    // Mark order_notifications as read
    try {
        $stmt = $conn->prepare("UPDATE order_notifications SET is_read = TRUE WHERE user_id = ?");
        $stmt->execute([$user_id]);
    } catch (Exception $e) {
        error_log("Error marking order notifications as read: " . $e->getMessage());
        $success = false;
    }
    
    // Mark notifications as read
    try {
        $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE user_id = ?");
        $stmt->execute([$user_id]);
    } catch (Exception $e) {
        error_log("Error marking general notifications as read: " . $e->getMessage());
        $success = false;
    }
    
    return $success;
}

/**
 * Delete a single notification
 */
function deleteNotification($conn, $notification_id, $user_id, $table = 'order_notifications') {
    try {
        $id_column = ($table === 'order_notifications') ? 
            (tableHasColumn($conn, $table, 'notification_id') ? 'notification_id' : 'id') : 
            'notification_id';
            
        $stmt = $conn->prepare("
            DELETE FROM {$table} 
            WHERE {$id_column} = ? AND user_id = ?
        ");
        
        return $stmt->execute([$notification_id, $user_id]);
    } catch (Exception $e) {
        error_log("Error deleting notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Clear all notifications for a user
 */
function clearAllNotifications($conn, $user_id) {
    $success = true;
    
    // Clear order_notifications
    try {
        $stmt = $conn->prepare("DELETE FROM order_notifications WHERE user_id = ?");
        $stmt->execute([$user_id]);
    } catch (Exception $e) {
        error_log("Error clearing order notifications: " . $e->getMessage());
        $success = false;
    }
    
    // Clear notifications
    try {
        $stmt = $conn->prepare("DELETE FROM notifications WHERE user_id = ?");
        $stmt->execute([$user_id]);
    } catch (Exception $e) {
        error_log("Error clearing general notifications: " . $e->getMessage());
        $success = false;
    }
    
    return $success;
}

/**
 * Check if a table has a specific column
 */
function tableHasColumn($conn, $table, $column) {
    try {
        $stmt = $conn->prepare("SHOW COLUMNS FROM {$table} LIKE ?");
        $stmt->execute([$column]);
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Get notification statistics
 */
function getNotificationStats($conn, $user_id) {
    $stats = [
        'total' => 0,
        'unread' => 0,
        'order_notifications' => 0,
        'general_notifications' => 0,
        'today' => 0,
        'this_week' => 0
    ];
    
    $today = date('Y-m-d');
    $week_ago = date('Y-m-d', strtotime('-7 days'));
    
    // Order notifications stats
    try {
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread,
                SUM(CASE WHEN DATE(created_at) = ? THEN 1 ELSE 0 END) as today,
                SUM(CASE WHEN DATE(created_at) >= ? THEN 1 ELSE 0 END) as this_week
            FROM order_notifications 
            WHERE user_id = ?
        ");
        $stmt->execute([$today, $week_ago, $user_id]);
        $order_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stats['order_notifications'] = $order_stats['total'];
        $stats['total'] += $order_stats['total'];
        $stats['unread'] += $order_stats['unread'];
        $stats['today'] += $order_stats['today'];
        $stats['this_week'] += $order_stats['this_week'];
    } catch (Exception $e) {
        error_log("Error getting order notification stats: " . $e->getMessage());
    }
    
    // General notifications stats
    try {
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                SUM(CASE WHEN DATE(created_at) = ? THEN 1 ELSE 0 END) as today,
                SUM(CASE WHEN DATE(created_at) >= ? THEN 1 ELSE 0 END) as this_week
            FROM notifications 
            WHERE user_id = ?
        ");
        $stmt->execute([$today, $week_ago, $user_id]);
        $general_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stats['general_notifications'] = $general_stats['total'];
        $stats['total'] += $general_stats['total'];
        $stats['unread'] += $general_stats['unread'];
        $stats['today'] += $general_stats['today'];
        $stats['this_week'] += $general_stats['this_week'];
    } catch (Exception $e) {
        error_log("Error getting general notification stats: " . $e->getMessage());
    }
    
    return $stats;
}

/**
 * Create a welcome notification for new users
 */
function createWelcomeNotification($conn, $user_id) {
    try {
        // Check if welcome notification already exists
        $stmt = $conn->prepare("
            SELECT COUNT(*) FROM order_notifications 
            WHERE user_id = ? AND type = 'welcome'
        ");
        $stmt->execute([$user_id]);
        
        if ($stmt->fetchColumn() == 0) {
            $stmt = $conn->prepare("
                INSERT INTO order_notifications (
                    user_id, title, message, type, status_logo, is_read, created_at
                ) VALUES (?, ?, ?, ?, ?, FALSE, NOW())
            ");
            
            return $stmt->execute([
                $user_id,
                'Welcome to TeWuNeed! 🎉',
                'Thank you for joining TeWuNeed! We\'re excited to have you as part of our community. Start exploring our products and enjoy shopping with us!',
                'welcome',
                '🎉'
            ]);
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Error creating welcome notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Clean up old notifications (older than 30 days)
 */
function cleanupOldNotifications($conn, $days = 30) {
    $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
    $cleaned = 0;
    
    try {
        // Clean order_notifications
        $stmt = $conn->prepare("
            DELETE FROM order_notifications 
            WHERE created_at < ? AND is_read = TRUE
        ");
        $stmt->execute([$cutoff_date]);
        $cleaned += $stmt->rowCount();
        
        // Clean notifications
        $stmt = $conn->prepare("
            DELETE FROM notifications 
            WHERE created_at < ? AND is_read = 1
        ");
        $stmt->execute([$cutoff_date]);
        $cleaned += $stmt->rowCount();
        
        return $cleaned;
    } catch (Exception $e) {
        error_log("Error cleaning up old notifications: " . $e->getMessage());
        return 0;
    }
}
?>
