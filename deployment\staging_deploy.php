<?php
/**
 * Staging Environment Deployment Script
 * Automated deployment and testing for TeWuNeed features
 */

class StagingDeployment {
    private $conn;
    private $deployment_log = [];
    private $test_results = [];
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * Main deployment process
     */
    public function deploy() {
        $this->log("Starting staging deployment...");
        
        try {
            // Step 1: Database setup
            $this->setupDatabase();
            
            // Step 2: Create test data
            $this->createTestData();
            
            // Step 3: Test all features
            $this->runFeatureTests();
            
            // Step 4: Performance tests
            $this->runPerformanceTests();
            
            // Step 5: Generate deployment report
            $this->generateDeploymentReport();
            
            $this->log("Staging deployment completed successfully!");
            
        } catch (Exception $e) {
            $this->log("Deployment failed: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }
    
    /**
     * Setup database with all required tables
     */
    private function setupDatabase() {
        $this->log("Setting up database...");
        
        $sql_files = [
            '../database/shipping_tables.sql',
            '../database/chat_tables.sql',
            '../database/analytics_tables.sql',
            '../database/notification_queue.sql',
            '../database/payment_methods_expansion.sql'
        ];
        
        foreach ($sql_files as $file) {
            if (file_exists($file)) {
                $sql = file_get_contents($file);
                $this->executeSQLFile($sql);
                $this->log("Executed: " . basename($file));
            } else {
                $this->log("SQL file not found: " . $file, 'WARNING');
            }
        }
        
        $this->test_results['database_setup'] = 'PASSED';
    }
    
    /**
     * Create comprehensive test data
     */
    private function createTestData() {
        $this->log("Creating test data...");
        
        // Create test users
        $this->createTestUsers();
        
        // Create test products
        $this->createTestProducts();
        
        // Create test orders
        $this->createTestOrders();
        
        // Create test analytics data
        $this->createTestAnalytics();
        
        // Create test chat sessions
        $this->createTestChatSessions();
        
        // Create test promotions
        $this->createTestPromotions();
        
        $this->test_results['test_data_creation'] = 'PASSED';
    }
    
    /**
     * Create test users
     */
    private function createTestUsers() {
        $test_users = [
            ['name' => 'Test Customer 1', 'email' => '<EMAIL>', 'password' => password_hash('test123', PASSWORD_DEFAULT)],
            ['name' => 'Test Customer 2', 'email' => '<EMAIL>', 'password' => password_hash('test123', PASSWORD_DEFAULT)],
            ['name' => 'Test Customer 3', 'email' => '<EMAIL>', 'password' => password_hash('test123', PASSWORD_DEFAULT)]
        ];
        
        foreach ($test_users as $user) {
            try {
                $stmt = $this->conn->prepare("
                    INSERT INTO users (name, email, password, created_at) 
                    VALUES (?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE name = VALUES(name)
                ");
                $stmt->execute([$user['name'], $user['email'], $user['password']]);
            } catch (Exception $e) {
                $this->log("Error creating test user: " . $e->getMessage(), 'WARNING');
            }
        }
    }
    
    /**
     * Create test products
     */
    private function createTestProducts() {
        $test_products = [
            ['name' => 'Test Smartphone', 'price' => 2500000, 'stock' => 50, 'category_id' => 1],
            ['name' => 'Test Laptop', 'price' => 8500000, 'stock' => 25, 'category_id' => 1],
            ['name' => 'Test Headphones', 'price' => 750000, 'stock' => 100, 'category_id' => 1],
            ['name' => 'Test T-Shirt', 'price' => 150000, 'stock' => 200, 'category_id' => 2],
            ['name' => 'Test Jeans', 'price' => 350000, 'stock' => 75, 'category_id' => 2]
        ];
        
        foreach ($test_products as $product) {
            try {
                $stmt = $this->conn->prepare("
                    INSERT INTO products (name, price, stock, category_id, description, is_active, created_at) 
                    VALUES (?, ?, ?, ?, 'Test product for staging environment', 1, NOW())
                    ON DUPLICATE KEY UPDATE price = VALUES(price), stock = VALUES(stock)
                ");
                $stmt->execute([$product['name'], $product['price'], $product['stock'], $product['category_id']]);
            } catch (Exception $e) {
                $this->log("Error creating test product: " . $e->getMessage(), 'WARNING');
            }
        }
    }
    
    /**
     * Create test orders
     */
    private function createTestOrders() {
        for ($i = 1; $i <= 10; $i++) {
            $order_id = 'TEST_' . date('Ymd') . '_' . str_pad($i, 4, '0', STR_PAD_LEFT);
            $user_id = rand(1, 3);
            $total_amount = rand(100000, 1000000);
            $status = ['pending', 'confirmed', 'processing', 'shipped', 'delivered'][rand(0, 4)];
            
            try {
                $stmt = $this->conn->prepare("
                    INSERT INTO orders (order_id, user_id, total_amount, status, created_at) 
                    VALUES (?, ?, ?, ?, NOW() - INTERVAL ? DAY)
                    ON DUPLICATE KEY UPDATE total_amount = VALUES(total_amount)
                ");
                $stmt->execute([$order_id, $user_id, $total_amount, $status, rand(0, 30)]);
            } catch (Exception $e) {
                $this->log("Error creating test order: " . $e->getMessage(), 'WARNING');
            }
        }
    }
    
    /**
     * Create test analytics data
     */
    private function createTestAnalytics() {
        // Create test sessions
        for ($i = 1; $i <= 50; $i++) {
            $session_id = 'test_session_' . $i;
            $device_types = ['desktop', 'mobile', 'tablet'];
            $referrers = ['google.com', 'facebook.com', 'instagram.com', 'direct', 'twitter.com'];
            
            try {
                $stmt = $this->conn->prepare("
                    INSERT INTO analytics_sessions (session_id, user_id, referrer_domain, device_type, browser, created_at) 
                    VALUES (?, ?, ?, ?, 'Chrome', NOW() - INTERVAL ? DAY)
                    ON DUPLICATE KEY UPDATE updated_at = NOW()
                ");
                $stmt->execute([
                    $session_id, 
                    rand(1, 3), 
                    $referrers[rand(0, 4)], 
                    $device_types[rand(0, 2)], 
                    rand(0, 7)
                ]);
            } catch (Exception $e) {
                $this->log("Error creating test analytics: " . $e->getMessage(), 'WARNING');
            }
        }
    }
    
    /**
     * Create test chat sessions
     */
    private function createTestChatSessions() {
        for ($i = 1; $i <= 5; $i++) {
            $session_id = 'CHAT_TEST_' . $i;
            $statuses = ['waiting', 'active', 'closed'];
            
            try {
                $stmt = $this->conn->prepare("
                    INSERT INTO chat_sessions (session_id, user_id, subject, status, created_at) 
                    VALUES (?, ?, ?, ?, NOW() - INTERVAL ? HOUR)
                    ON DUPLICATE KEY UPDATE status = VALUES(status)
                ");
                $stmt->execute([
                    $session_id, 
                    rand(1, 3), 
                    'Test support inquiry ' . $i, 
                    $statuses[rand(0, 2)], 
                    rand(1, 24)
                ]);
            } catch (Exception $e) {
                $this->log("Error creating test chat: " . $e->getMessage(), 'WARNING');
            }
        }
    }
    
    /**
     * Create test promotions
     */
    private function createTestPromotions() {
        $test_promotions = [
            [
                'name' => 'Test Welcome Discount',
                'code' => 'TESTWELCOME10',
                'type' => 'coupon',
                'discount_type' => 'percentage',
                'discount_value' => 10.00,
                'min_order_amount' => 100000
            ],
            [
                'name' => 'Test Free Shipping',
                'code' => 'TESTFREESHIP',
                'type' => 'voucher',
                'discount_type' => 'free_shipping',
                'discount_value' => 0,
                'min_order_amount' => 200000
            ]
        ];
        
        foreach ($test_promotions as $promo) {
            try {
                $stmt = $this->conn->prepare("
                    INSERT INTO promotions (name, code, type, discount_type, discount_value, min_order_amount, start_date, end_date, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 1)
                    ON DUPLICATE KEY UPDATE discount_value = VALUES(discount_value)
                ");
                $stmt->execute([
                    $promo['name'], $promo['code'], $promo['type'], 
                    $promo['discount_type'], $promo['discount_value'], $promo['min_order_amount']
                ]);
            } catch (Exception $e) {
                $this->log("Error creating test promotion: " . $e->getMessage(), 'WARNING');
            }
        }
    }
    
    /**
     * Run comprehensive feature tests
     */
    private function runFeatureTests() {
        $this->log("Running feature tests...");
        
        // Test shipping functionality
        $this->testShippingFeatures();
        
        // Test promotion functionality
        $this->testPromotionFeatures();
        
        // Test chat functionality
        $this->testChatFeatures();
        
        // Test analytics functionality
        $this->testAnalyticsFeatures();
        
        // Test notification functionality
        $this->testNotificationFeatures();
        
        // Test wishlist functionality
        $this->testWishlistFeatures();
    }
    
    /**
     * Test shipping features
     */
    private function testShippingFeatures() {
        try {
            require_once '../includes/ShippingManager.php';
            $shippingManager = new ShippingManager();
            
            // Test getting shipping options
            $options = $shippingManager->getShippingOptions('Jakarta', 'Bandung', 1.5, 500000);
            
            if (!empty($options)) {
                $this->test_results['shipping_options'] = 'PASSED';
                $this->log("Shipping options test: PASSED (" . count($options) . " options found)");
            } else {
                $this->test_results['shipping_options'] = 'FAILED';
                $this->log("Shipping options test: FAILED (no options returned)", 'ERROR');
            }
            
            // Test creating shipping record
            $shipping_result = $shippingManager->createShipping(
                'TEST_ORDER_001',
                'jne',
                'REG',
                15000,
                [
                    'name' => 'Test Customer',
                    'phone' => '081234567890',
                    'address' => 'Test Address',
                    'city' => 'Jakarta',
                    'postal_code' => '12345'
                ]
            );
            
            if ($shipping_result['success']) {
                $this->test_results['shipping_creation'] = 'PASSED';
                $this->log("Shipping creation test: PASSED");
            } else {
                $this->test_results['shipping_creation'] = 'FAILED';
                $this->log("Shipping creation test: FAILED", 'ERROR');
            }
            
        } catch (Exception $e) {
            $this->test_results['shipping_features'] = 'FAILED';
            $this->log("Shipping features test failed: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * Test promotion features
     */
    private function testPromotionFeatures() {
        try {
            require_once '../includes/PromotionManager.php';
            $promotionManager = new PromotionManager();
            
            // Test getting active promotions
            $promotions = $promotionManager->getActivePromotions();
            
            if (!empty($promotions)) {
                $this->test_results['promotion_listing'] = 'PASSED';
                $this->log("Promotion listing test: PASSED (" . count($promotions) . " promotions found)");
            } else {
                $this->test_results['promotion_listing'] = 'FAILED';
                $this->log("Promotion listing test: FAILED (no promotions found)", 'WARNING');
            }
            
            // Test applying promotion
            $cart_items = [
                ['product_id' => 1, 'quantity' => 2, 'price' => 100000, 'total_price' => 200000, 'category_id' => 1]
            ];
            
            $promo_result = $promotionManager->applyPromotion('TESTWELCOME10', $cart_items, 1);
            
            if ($promo_result['success']) {
                $this->test_results['promotion_application'] = 'PASSED';
                $this->log("Promotion application test: PASSED (discount: " . $promo_result['discount_amount'] . ")");
            } else {
                $this->test_results['promotion_application'] = 'FAILED';
                $this->log("Promotion application test: FAILED - " . $promo_result['message'], 'ERROR');
            }
            
        } catch (Exception $e) {
            $this->test_results['promotion_features'] = 'FAILED';
            $this->log("Promotion features test failed: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * Test chat features
     */
    private function testChatFeatures() {
        try {
            require_once '../includes/ChatManager.php';
            $chatManager = new ChatManager();
            
            // Test starting chat session
            $chat_result = $chatManager->startChatSession(1, 'Test Support', 'This is a test message');
            
            if ($chat_result['success']) {
                $this->test_results['chat_session_creation'] = 'PASSED';
                $this->log("Chat session creation test: PASSED");
                
                // Test sending message
                $message_result = $chatManager->sendMessage(
                    $chat_result['session_id'], 
                    1, 
                    'Test response message', 
                    'customer'
                );
                
                if ($message_result['success']) {
                    $this->test_results['chat_messaging'] = 'PASSED';
                    $this->log("Chat messaging test: PASSED");
                } else {
                    $this->test_results['chat_messaging'] = 'FAILED';
                    $this->log("Chat messaging test: FAILED", 'ERROR');
                }
            } else {
                $this->test_results['chat_session_creation'] = 'FAILED';
                $this->log("Chat session creation test: FAILED", 'ERROR');
            }
            
        } catch (Exception $e) {
            $this->test_results['chat_features'] = 'FAILED';
            $this->log("Chat features test failed: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * Test analytics features
     */
    private function testAnalyticsFeatures() {
        try {
            require_once '../includes/AnalyticsManager.php';
            $analyticsManager = new AnalyticsManager();
            
            // Test dashboard overview
            $dashboard = $analyticsManager->getDashboardOverview();
            
            if (!empty($dashboard)) {
                $this->test_results['analytics_dashboard'] = 'PASSED';
                $this->log("Analytics dashboard test: PASSED");
            } else {
                $this->test_results['analytics_dashboard'] = 'FAILED';
                $this->log("Analytics dashboard test: FAILED", 'ERROR');
            }
            
            // Test conversion funnel
            $funnel = $analyticsManager->getConversionFunnel(date('Y-m-01'), date('Y-m-d'));
            
            if (!empty($funnel)) {
                $this->test_results['analytics_funnel'] = 'PASSED';
                $this->log("Analytics funnel test: PASSED");
            } else {
                $this->test_results['analytics_funnel'] = 'FAILED';
                $this->log("Analytics funnel test: FAILED", 'ERROR');
            }
            
        } catch (Exception $e) {
            $this->test_results['analytics_features'] = 'FAILED';
            $this->log("Analytics features test failed: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * Test notification features
     */
    private function testNotificationFeatures() {
        try {
            require_once '../includes/NotificationManager.php';
            $notificationManager = new NotificationManager();
            
            // Test creating notification
            $notification_id = $notificationManager->createNotification(
                1, 
                'test', 
                'Test Notification', 
                'This is a test notification for staging deployment'
            );
            
            if ($notification_id) {
                $this->test_results['notification_creation'] = 'PASSED';
                $this->log("Notification creation test: PASSED");
            } else {
                $this->test_results['notification_creation'] = 'FAILED';
                $this->log("Notification creation test: FAILED", 'ERROR');
            }
            
        } catch (Exception $e) {
            $this->test_results['notification_features'] = 'FAILED';
            $this->log("Notification features test failed: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * Test wishlist features
     */
    private function testWishlistFeatures() {
        try {
            require_once '../includes/WishlistManager.php';
            $wishlistManager = new WishlistManager();
            
            // Test adding to wishlist
            $wishlist_result = $wishlistManager->addToWishlist(1, 1, 'Test wishlist item');
            
            if ($wishlist_result['success']) {
                $this->test_results['wishlist_add'] = 'PASSED';
                $this->log("Wishlist add test: PASSED");
                
                // Test getting wishlist
                $wishlist_items = $wishlistManager->getUserWishlist(1);
                
                if (!empty($wishlist_items)) {
                    $this->test_results['wishlist_retrieval'] = 'PASSED';
                    $this->log("Wishlist retrieval test: PASSED");
                } else {
                    $this->test_results['wishlist_retrieval'] = 'FAILED';
                    $this->log("Wishlist retrieval test: FAILED", 'ERROR');
                }
            } else {
                $this->test_results['wishlist_add'] = 'FAILED';
                $this->log("Wishlist add test: FAILED", 'ERROR');
            }
            
        } catch (Exception $e) {
            $this->test_results['wishlist_features'] = 'FAILED';
            $this->log("Wishlist features test failed: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * Run performance tests
     */
    private function runPerformanceTests() {
        $this->log("Running performance tests...");
        
        // Test database query performance
        $start_time = microtime(true);
        
        try {
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) FROM orders o 
                JOIN users u ON o.user_id = u.user_id 
                WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ");
            $stmt->execute();
            $result = $stmt->fetchColumn();
            
            $execution_time = microtime(true) - $start_time;
            
            if ($execution_time < 1.0) {
                $this->test_results['database_performance'] = 'PASSED';
                $this->log("Database performance test: PASSED (" . round($execution_time * 1000, 2) . "ms)");
            } else {
                $this->test_results['database_performance'] = 'WARNING';
                $this->log("Database performance test: WARNING (slow query: " . round($execution_time * 1000, 2) . "ms)", 'WARNING');
            }
            
        } catch (Exception $e) {
            $this->test_results['database_performance'] = 'FAILED';
            $this->log("Database performance test failed: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * Execute SQL file
     */
    private function executeSQLFile($sql) {
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(--|\/\*)/', $statement)) {
                try {
                    $this->conn->exec($statement);
                } catch (PDOException $e) {
                    // Log but don't fail for non-critical errors
                    $this->log("SQL execution warning: " . $e->getMessage(), 'WARNING');
                }
            }
        }
    }
    
    /**
     * Generate deployment report
     */
    private function generateDeploymentReport() {
        $this->log("Generating deployment report...");
        
        $report = [
            'deployment_time' => date('Y-m-d H:i:s'),
            'total_tests' => count($this->test_results),
            'passed_tests' => count(array_filter($this->test_results, function($result) { return $result === 'PASSED'; })),
            'failed_tests' => count(array_filter($this->test_results, function($result) { return $result === 'FAILED'; })),
            'warning_tests' => count(array_filter($this->test_results, function($result) { return $result === 'WARNING'; })),
            'test_results' => $this->test_results,
            'deployment_log' => $this->deployment_log
        ];
        
        // Save report to file
        $report_file = 'staging_deployment_report_' . date('Y-m-d_H-i-s') . '.json';
        file_put_contents($report_file, json_encode($report, JSON_PRETTY_PRINT));
        
        $this->log("Deployment report saved: " . $report_file);
        
        // Display summary
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "STAGING DEPLOYMENT SUMMARY\n";
        echo str_repeat("=", 60) . "\n";
        echo "Total Tests: " . $report['total_tests'] . "\n";
        echo "Passed: " . $report['passed_tests'] . "\n";
        echo "Failed: " . $report['failed_tests'] . "\n";
        echo "Warnings: " . $report['warning_tests'] . "\n";
        echo "Success Rate: " . round(($report['passed_tests'] / $report['total_tests']) * 100, 2) . "%\n";
        echo str_repeat("=", 60) . "\n";
        
        foreach ($this->test_results as $test => $result) {
            $status_icon = $result === 'PASSED' ? '✅' : ($result === 'FAILED' ? '❌' : '⚠️');
            echo $status_icon . " " . ucwords(str_replace('_', ' ', $test)) . ": " . $result . "\n";
        }
        
        echo str_repeat("=", 60) . "\n";
    }
    
    /**
     * Log deployment activities
     */
    private function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] [$level] $message";
        
        $this->deployment_log[] = $log_entry;
        echo $log_entry . "\n";
    }
}

// Run deployment if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    require_once '../includes/db_connect.php';
    
    try {
        $deployment = new StagingDeployment();
        $deployment->deploy();
    } catch (Exception $e) {
        echo "Deployment failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
