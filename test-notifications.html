
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notifikasi Keranjang - <PERSON>wuneed</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    
    <!-- Custom Notification CSS -->
    <link rel="stylesheet" href="css/notifications.css">
    
    <style>
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            transition: transform 0.2s;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .btn-add-cart {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s;
        }
        
        .btn-add-cart:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .test-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">Test Notifikasi Keranjang Belanja</h1>
        
        <!-- Test Controls -->
        <div class="test-controls">
            <h3>Test Notifikasi</h3>
            <div class="row">
                <div class="col-md-3">
                    <button class="btn btn-success w-100 mb-2" onclick="testSingleNotification()">
                        Test 1 Notifikasi
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning w-100 mb-2" onclick="testMultipleNotifications()">
                        Test Multiple Notifikasi
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info w-100 mb-2" onclick="testStaggeredNotifications()">
                        Test Staggered Timing
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-danger w-100 mb-2" onclick="clearAllNotifications()">
                        Clear All
                    </button>
                </div>
            </div>
            
            <h3 class="mt-3">Test Filter Error & Enable/Disable</h3>
            <div class="row">
                <div class="col-md-3">
                    <button class="btn btn-danger w-100 mb-2" onclick="testErrorNotification()">
                        Test Error Notification
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success w-100 mb-2" onclick="testSuccessNotification()">
                        Test Success Notification
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-primary w-100 mb-2" onclick="enableNotifications()">
                        Enable Notifications
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-secondary w-100 mb-2" onclick="disableNotifications()">
                        Disable Notifications
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Sample Products -->
        <div class="row">
            <div class="col-md-4">
                <div class="product-card">
                    <img src="https://via.placeholder.com/300x200/28a745/ffffff?text=Produk+1" alt="Produk 1" class="product-image">
                    <h5>Smartphone Samsung Galaxy</h5>
                    <p class="text-muted">Rp 5.000.000</p>
                    <div class="mb-3">
                        <label>Quantity:</label>
                        <input type="number" id="qty1" value="1" min="1" max="10" class="form-control d-inline-block" style="width: 80px;">
                    </div>
                    <button class="btn btn-add-cart" onclick="addToCart(1, 'Smartphone Samsung Galaxy', document.getElementById('qty1').value)">
                        🛒 Tambah ke Keranjang
                    </button>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="product-card">
                    <img src="https://via.placeholder.com/300x200/dc3545/ffffff?text=Produk+2" alt="Produk 2" class="product-image">
                    <h5>Laptop ASUS ROG</h5>
                    <p class="text-muted">Rp 15.000.000</p>
                    <div class="mb-3">
                        <label>Quantity:</label>
                        <input type="number" id="qty2" value="1" min="1" max="10" class="form-control d-inline-block" style="width: 80px;">
                    </div>
                    <button class="btn btn-add-cart" onclick="addToCart(2, 'Laptop ASUS ROG', document.getElementById('qty2').value)">
                        🛒 Tambah ke Keranjang
                    </button>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="product-card">
                    <img src="https://via.placeholder.com/300x200/ffc107/000000?text=Produk+3" alt="Produk 3" class="product-image">
                    <h5>Headphone Sony WH-1000XM4</h5>
                    <p class="text-muted">Rp 4.500.000</p>
                    <div class="mb-3">
                        <label>Quantity:</label>
                        <input type="number" id="qty3" value="1" min="1" max="10" class="form-control d-inline-block" style="width: 80px;">
                    </div>
                    <button class="btn btn-add-cart" onclick="addToCart(3, 'Headphone Sony WH-1000XM4', document.getElementById('qty3').value)">
                        🛒 Tambah ke Keranjang
                    </button>
                </div>
            </div>
        </div>
        
        <!-- More Products -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="product-card">
                    <img src="https://via.placeholder.com/300x200/17a2b8/ffffff?text=Produk+4" alt="Produk 4" class="product-image">
                    <h5>Kamera Canon EOS R5</h5>
                    <p class="text-muted">Rp 25.000.000</p>
                    <div class="mb-3">
                        <label>Quantity:</label>
                        <input type="number" id="qty4" value="1" min="1" max="10" class="form-control d-inline-block" style="width: 80px;">
                    </div>
                    <button class="btn btn-add-cart" onclick="addToCart(4, 'Kamera Canon EOS R5', document.getElementById('qty4').value)">
                        🛒 Tambah ke Keranjang
                    </button>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="product-card">
                    <img src="https://via.placeholder.com/300x200/6f42c1/ffffff?text=Produk+5" alt="Produk 5" class="product-image">
                    <h5>Apple Watch Series 8</h5>
                    <p class="text-muted">Rp 8.000.000</p>
                    <div class="mb-3">
                        <label>Quantity:</label>
                        <input type="number" id="qty5" value="1" min="1" max="10" class="form-control d-inline-block" style="width: 80px;">
                    </div>
                    <button class="btn btn-add-cart" onclick="addToCart(5, 'Apple Watch Series 8', document.getElementById('qty5').value)">
                        🛒 Tambah ke Keranjang
                    </button>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="product-card">
                    <img src="https://via.placeholder.com/300x200/fd7e14/ffffff?text=Produk+6" alt="Produk 6" class="product-image">
                    <h5>iPad Pro 12.9 inch</h5>
                    <p class="text-muted">Rp 18.000.000</p>
                    <div class="mb-3">
                        <label>Quantity:</label>
                        <input type="number" id="qty6" value="1" min="1" max="10" class="form-control d-inline-block" style="width: 80px;">
                    </div>
                    <button class="btn btn-add-cart" onclick="addToCart(6, 'iPad Pro 12.9 inch', document.getElementById('qty6').value)">
                        🛒 Tambah ke Keranjang
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="alert alert-info mt-4">
            <h5>Cara Test:</h5>
            <ul>
                <li>Klik tombol "Tambah ke Keranjang" pada produk untuk melihat notifikasi dengan nama produk dan quantity</li>
                <li>Ubah quantity sebelum menambahkan untuk melihat notifikasi dengan jumlah yang berbeda</li>
                <li>Gunakan tombol test di atas untuk menguji berbagai skenario notifikasi</li>
                <li>Notifikasi akan muncul dengan staggered timing untuk menghindari spam</li>
                <li><strong>Filter Error:</strong> Klik tombol "Test Error Notification" untuk menguji filter notifikasi error. Pesan error umum seperti "Error adding to cart" tidak akan ditampilkan</li>
                <li><strong>Enable/Disable:</strong> Gunakan tombol Enable/Disable untuk mengaktifkan atau menonaktifkan semua notifikasi</li>
            </ul>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script src="js/notification-manager.js"></script>
    <script src="js/notification-integration.js"></script>
    
    <script>
        // Simulate cart functionality
        let cart = [];
        
        function addToCart(productId, productName, quantity) {
            quantity = parseInt(quantity) || 1;
            
            // Simulate adding to cart
            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({
                    id: productId,
                    name: productName,
                    quantity: quantity
                });
            }
            
            // Show notification with product name and quantity
            if (window.notificationManager) {
                window.notificationManager.addCartNotification(productName, quantity);
            } else {
                showToast(`${quantity} ${productName} telah ditambahkan ke keranjang`, 'success');
            }
            
            console.log('Cart updated:', cart);
        }
        
        // Test functions
        function testSingleNotification() {
            addToCart(999, 'Test Product', 1);
        }
        
        function testMultipleNotifications() {
            const products = [
                'Smartphone Test',
                'Laptop Test', 
                'Headphone Test',
                'Camera Test',
                'Watch Test'
            ];
            
            products.forEach((product, index) => {
                setTimeout(() => {
                    addToCart(1000 + index, product, Math.floor(Math.random() * 3) + 1);
                }, index * 200); // Quick succession to test staggering
            });
        }
        
        function testStaggeredNotifications() {
            const products = [
                'Produk Stagger 1',
                'Produk Stagger 2',
                'Produk Stagger 3',
                'Produk Stagger 4'
            ];
            
            products.forEach((product, index) => {
                addToCart(2000 + index, product, index + 1);
            });
        }
        
        function clearAllNotifications() {
            if (window.notificationManager) {
                window.notificationManager.clearAll();
            }
        }
        
        // Test error notification filtering
        function testErrorNotification() {
            if (window.notificationManager) {
                // Test dengan pesan error yang seharusnya difilter
                window.notificationManager.addNotification(
                    'Error adding to cart', 
                    'danger'
                );
                
                // Test dengan pesan error database
                window.notificationManager.addNotification(
                    'Database error occurred', 
                    'danger'
                );
                
                // Test dengan pesan error dalam bahasa Indonesia
                window.notificationManager.addNotification(
                    'Gagal menambahkan produk ke keranjang', 
                    'danger'
                );
            }
        }
        
        // Test success notification (tidak difilter)
        function testSuccessNotification() {
            if (window.notificationManager) {
                window.notificationManager.addNotification(
                    'Ini adalah notifikasi sukses yang tidak difilter', 
                    'success'
                );
            }
        }
        
        // Enable notifications
        function enableNotifications() {
            if (window.toggleNotifications) {
                window.toggleNotifications(true);
                alert('Notifikasi diaktifkan');
            } else {
                alert('toggleNotifications function tidak tersedia');
            }
        }
        
        // Disable notifications
        function disableNotifications() {
            if (window.toggleNotifications) {
                window.toggleNotifications(false);
                alert('Notifikasi dinonaktifkan');
            } else {
                alert('toggleNotifications function tidak tersedia');
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Notification test page loaded');
            console.log('NotificationManager available:', !!window.notificationManager);
            console.log('toggleNotifications available:', !!window.toggleNotifications);
        });
    </script>
</body>
</html>

