<?php
session_start();
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';
require_once 'includes/firebase_auth.php';
require_once 'includes/WishlistManager.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'] ?? $_SESSION['local_user_id'] ?? null;

if (!$user_id) {
    header('Location: login.php');
    exit;
}

// Set page variables
$page = 'wishlist';
$page_title = 'My Wishlist';

$wishlistManager = new WishlistManager();

// Get wishlist items
$collection_id = $_GET['collection'] ?? null;
$current_page = (int)($_GET['page'] ?? 1);
$items_per_page = 12;
$offset = ($current_page - 1) * $items_per_page;

$wishlist_items = $wishlistManager->getUserWishlist($user_id, $collection_id, $items_per_page, $offset);
$total_count = $wishlistManager->getWishlistCount($user_id);
$total_pages = ceil($total_count / $items_per_page);

// Get collections
$collections = $wishlistManager->getUserCollections($user_id);

require_once 'includes/header.php';
?>

<style>
.wishlist-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin-bottom: 40px;
}

.wishlist-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    position: relative;
}

.wishlist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.wishlist-item-img {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.wishlist-card:hover .wishlist-item-img {
    transform: scale(1.05);
}

.priority-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2;
}

.remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.1);
}

.empty-wishlist {
    text-align: center;
    padding: 80px 20px;
}

.collection-tabs .nav-link {
    border-radius: 25px;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.collection-tabs .nav-link.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-color: transparent;
}
</style>

<!-- Hero Section -->
<div class="wishlist-hero">
    <div class="container text-center">
        <h1><i class="fas fa-heart me-3"></i>My Wishlist</h1>
        <p class="lead">Your favorite products saved for later</p>
        <div class="mt-3">
            <span class="badge bg-light text-dark fs-6 px-3 py-2">
                <i class="fas fa-items-alt me-2"></i><?php echo $total_count; ?> Items
            </span>
        </div>
    </div>
</div>

<div class="container mb-5">
    <!-- Collections Tabs -->
    <?php if (count($collections) > 1): ?>
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills collection-tabs justify-content-center">
                <li class="nav-item">
                    <a class="nav-link <?php echo !$collection_id ? 'active' : ''; ?>" href="wishlist.php">
                        <i class="fas fa-heart me-2"></i>All Items
                    </a>
                </li>
                <?php foreach ($collections as $collection): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo $collection_id == $collection['collection_id'] ? 'active' : ''; ?>" 
                       href="wishlist.php?collection=<?php echo $collection['collection_id']; ?>">
                        <i class="fas fa-folder me-2"></i><?php echo htmlspecialchars($collection['collection_name']); ?>
                        <span class="badge bg-secondary ms-2"><?php echo $collection['item_count']; ?></span>
                    </a>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>

    <!-- Wishlist Actions -->
    <?php if (!empty($wishlist_items)): ?>
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="d-flex align-items-center">
                <input type="checkbox" id="selectAll" class="form-check-input me-2">
                <label for="selectAll" class="form-check-label me-3">Select All</label>
                <button class="btn btn-outline-danger btn-sm me-2" id="removeSelected" disabled>
                    <i class="fas fa-trash me-1"></i>Remove Selected
                </button>
                <button class="btn btn-outline-primary btn-sm" id="moveToCart" disabled>
                    <i class="fas fa-shopping-cart me-1"></i>Move to Cart
                </button>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-sort me-1"></i>Sort By
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="?sort=newest">Recently Added</a></li>
                    <li><a class="dropdown-item" href="?sort=oldest">Oldest First</a></li>
                    <li><a class="dropdown-item" href="?sort=price_low">Price: Low to High</a></li>
                    <li><a class="dropdown-item" href="?sort=price_high">Price: High to Low</a></li>
                    <li><a class="dropdown-item" href="?sort=priority">Priority</a></li>
                </ul>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Wishlist Items -->
    <?php if (empty($wishlist_items)): ?>
    <div class="empty-wishlist">
        <i class="fas fa-heart-broken text-muted" style="font-size: 4rem;"></i>
        <h3 class="mt-3 text-muted">Your wishlist is empty</h3>
        <p class="text-muted">Start adding products you love to your wishlist!</p>
        <a href="Products.php" class="btn btn-primary btn-lg mt-3">
            <i class="fas fa-shopping-bag me-2"></i>Browse Products
        </a>
    </div>
    <?php else: ?>
    <div class="row" id="wishlistItems">
        <?php foreach ($wishlist_items as $item): ?>
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card wishlist-card h-100" data-product-id="<?php echo $item['product_id']; ?>">
                <!-- Priority Badge -->
                <?php if ($item['priority'] !== 'medium'): ?>
                <span class="badge priority-badge <?php echo $item['priority'] === 'high' ? 'bg-danger' : 'bg-warning'; ?>">
                    <?php echo ucfirst($item['priority']); ?>
                </span>
                <?php endif; ?>
                
                <!-- Remove Button -->
                <button class="remove-btn" onclick="removeFromWishlist(<?php echo $item['product_id']; ?>)">
                    <i class="fas fa-times"></i>
                </button>
                
                <!-- Selection Checkbox -->
                <div class="position-absolute" style="top: 10px; left: 50px; z-index: 2;">
                    <input type="checkbox" class="form-check-input item-checkbox" 
                           value="<?php echo $item['product_id']; ?>">
                </div>
                
                <!-- Product Image -->
                <div class="position-relative overflow-hidden">
                    <img src="<?php echo !empty($item['image']) ? 'uploads/' . htmlspecialchars($item['image']) : 'assets/img/product-default.jpg'; ?>"
                         class="card-img-top wishlist-item-img"
                         alt="<?php echo htmlspecialchars($item['product_name']); ?>"
                         onerror="this.src='assets/img/product-default.jpg'">
                </div>
                
                <!-- Product Info -->
                <div class="card-body">
                    <h6 class="card-title"><?php echo htmlspecialchars($item['product_name']); ?></h6>
                    <p class="text-muted small mb-2">
                        <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($item['category_name']); ?>
                    </p>
                    
                    <!-- Rating -->
                    <?php if ($item['average_rating'] > 0): ?>
                    <div class="mb-2">
                        <span class="text-warning">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star<?php echo $i <= $item['average_rating'] ? '' : '-o'; ?>"></i>
                            <?php endfor; ?>
                        </span>
                        <small class="text-muted">(<?php echo $item['total_reviews']; ?>)</small>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Price -->
                    <div class="mb-3">
                        <span class="h5 text-primary fw-bold">Rp <?php echo number_format($item['price']); ?></span>
                    </div>
                    
                    <!-- Stock Status -->
                    <div class="mb-3">
                        <?php if ($item['stock'] > 0): ?>
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>In Stock (<?php echo $item['stock']; ?>)
                        </span>
                        <?php else: ?>
                        <span class="badge bg-danger">
                            <i class="fas fa-times me-1"></i>Out of Stock
                        </span>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Notes -->
                    <?php if (!empty($item['notes'])): ?>
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-sticky-note me-1"></i>
                            <?php echo htmlspecialchars($item['notes']); ?>
                        </small>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Added Date -->
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Added <?php echo date('M j, Y', strtotime($item['added_at'])); ?>
                        </small>
                    </div>
                    
                    <!-- Actions -->
                    <div class="d-grid gap-2">
                        <a href="product-detail.php?id=<?php echo $item['product_id']; ?>" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>View Details
                        </a>
                        <?php if ($item['stock'] > 0): ?>
                        <button class="btn btn-success btn-sm add-to-cart-btn" 
                                data-product-id="<?php echo $item['product_id']; ?>"
                                data-product-name="<?php echo htmlspecialchars($item['product_name']); ?>">
                            <i class="fas fa-cart-plus me-1"></i>Add to Cart
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
</div>

<!-- Wishlist JavaScript -->
<script src="js/wishlist.js"></script>

<?php require_once 'includes/footer.php'; ?>
