<?php
/**
 * Live Chat Manager
 * Handles real-time customer support chat functionality
 */

class ChatManager {
    private $conn;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * Start a new chat session
     */
    public function startChatSession($user_id, $subject = null, $initial_message = null) {
        try {
            $session_id = $this->generateSessionId();
            
            $stmt = $this->conn->prepare("
                INSERT INTO chat_sessions (session_id, user_id, subject, status, created_at)
                VALUES (?, ?, ?, 'waiting', NOW())
            ");
            $stmt->execute([$session_id, $user_id, $subject]);
            
            $chat_session_id = $this->conn->lastInsertId();
            
            // Send initial message if provided
            if ($initial_message) {
                $this->sendMessage($session_id, $user_id, $initial_message, 'customer');
            }
            
            // Send automated welcome message
            $this->sendAutomatedMessage($session_id, 
                "Hello! Thank you for contacting TeWuNeed support. An agent will be with you shortly. How can we help you today?"
            );
            
            return [
                'success' => true,
                'session_id' => $session_id,
                'chat_session_id' => $chat_session_id
            ];
            
        } catch (PDOException $e) {
            error_log("Error starting chat session: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to start chat session'];
        }
    }
    
    /**
     * Send a message in chat
     */
    public function sendMessage($session_id, $sender_id, $message, $sender_type = 'customer', $message_type = 'text') {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO chat_messages (session_id, sender_id, sender_type, message, message_type, sent_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$session_id, $sender_id, $sender_type, $message, $message_type]);
            
            $message_id = $this->conn->lastInsertId();
            
            // Update session last activity
            $this->updateSessionActivity($session_id);
            
            // Trigger real-time notification
            $this->triggerChatNotification($session_id, $sender_id, $sender_type, $message);
            
            return [
                'success' => true,
                'message_id' => $message_id,
                'sent_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (PDOException $e) {
            error_log("Error sending chat message: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to send message'];
        }
    }
    
    /**
     * Get chat messages for a session
     */
    public function getChatMessages($session_id, $limit = 50, $offset = 0) {
        try {
            $stmt = $this->conn->prepare("
                SELECT cm.*, 
                       CASE 
                           WHEN cm.sender_type = 'customer' THEN u.name
                           WHEN cm.sender_type = 'agent' THEN a.name
                           ELSE 'System'
                       END as sender_name,
                       CASE 
                           WHEN cm.sender_type = 'customer' THEN u.profile_picture
                           WHEN cm.sender_type = 'agent' THEN a.profile_picture
                           ELSE NULL
                       END as sender_avatar
                FROM chat_messages cm
                LEFT JOIN users u ON cm.sender_id = u.user_id AND cm.sender_type = 'customer'
                LEFT JOIN admin_users a ON cm.sender_id = a.admin_id AND cm.sender_type = 'agent'
                WHERE cm.session_id = ?
                ORDER BY cm.sent_at ASC
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$session_id, $limit, $offset]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting chat messages: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get user's chat sessions
     */
    public function getUserChatSessions($user_id, $limit = 10) {
        try {
            $stmt = $this->conn->prepare("
                SELECT cs.*, 
                       (SELECT message FROM chat_messages 
                        WHERE session_id = cs.session_id 
                        ORDER BY sent_at DESC LIMIT 1) as last_message,
                       (SELECT sent_at FROM chat_messages 
                        WHERE session_id = cs.session_id 
                        ORDER BY sent_at DESC LIMIT 1) as last_message_time,
                       (SELECT COUNT(*) FROM chat_messages 
                        WHERE session_id = cs.session_id 
                        AND sender_type != 'customer' 
                        AND is_read = 0) as unread_count
                FROM chat_sessions cs
                WHERE cs.user_id = ?
                ORDER BY cs.updated_at DESC
                LIMIT ?
            ");
            $stmt->execute([$user_id, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting user chat sessions: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Assign agent to chat session
     */
    public function assignAgent($session_id, $agent_id) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE chat_sessions 
                SET agent_id = ?, status = 'active', updated_at = NOW()
                WHERE session_id = ?
            ");
            $result = $stmt->execute([$agent_id, $session_id]);
            
            if ($result) {
                // Send agent introduction message
                $agent_info = $this->getAgentInfo($agent_id);
                $intro_message = "Hi! I'm {$agent_info['name']} and I'll be assisting you today. How can I help you?";
                $this->sendMessage($session_id, $agent_id, $intro_message, 'agent');
            }
            
            return $result;
            
        } catch (PDOException $e) {
            error_log("Error assigning agent: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Close chat session
     */
    public function closeChatSession($session_id, $closed_by_id, $closed_by_type = 'customer', $reason = null) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE chat_sessions 
                SET status = 'closed', closed_by = ?, closed_by_type = ?, close_reason = ?, closed_at = NOW(), updated_at = NOW()
                WHERE session_id = ?
            ");
            $result = $stmt->execute([$closed_by_id, $closed_by_type, $reason, $session_id]);
            
            if ($result) {
                // Send closing message
                $closing_message = "This chat session has been closed. Thank you for contacting TeWuNeed support!";
                $this->sendAutomatedMessage($session_id, $closing_message);
                
                // Send satisfaction survey
                $this->sendSatisfactionSurvey($session_id);
            }
            
            return $result;
            
        } catch (PDOException $e) {
            error_log("Error closing chat session: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mark messages as read
     */
    public function markMessagesAsRead($session_id, $reader_id, $reader_type = 'customer') {
        try {
            $stmt = $this->conn->prepare("
                UPDATE chat_messages 
                SET is_read = 1, read_at = NOW()
                WHERE session_id = ? AND sender_type != ? AND is_read = 0
            ");
            return $stmt->execute([$session_id, $reader_type]);
            
        } catch (PDOException $e) {
            error_log("Error marking messages as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get chat session info
     */
    public function getChatSession($session_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT cs.*, u.name as customer_name, u.email as customer_email,
                       a.name as agent_name, a.email as agent_email
                FROM chat_sessions cs
                LEFT JOIN users u ON cs.user_id = u.user_id
                LEFT JOIN admin_users a ON cs.agent_id = a.admin_id
                WHERE cs.session_id = ?
            ");
            $stmt->execute([$session_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting chat session: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get available agents
     */
    public function getAvailableAgents() {
        try {
            $stmt = $this->conn->prepare("
                SELECT admin_id, name, email, profile_picture, status,
                       (SELECT COUNT(*) FROM chat_sessions 
                        WHERE agent_id = admin_users.admin_id AND status = 'active') as active_chats
                FROM admin_users 
                WHERE role IN ('admin', 'support') AND is_active = 1 AND status = 'online'
                ORDER BY active_chats ASC, name ASC
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting available agents: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Send automated message
     */
    private function sendAutomatedMessage($session_id, $message) {
        return $this->sendMessage($session_id, 0, $message, 'system');
    }
    
    /**
     * Generate unique session ID
     */
    private function generateSessionId() {
        return 'CHAT_' . date('Ymd') . '_' . strtoupper(substr(uniqid(), -8));
    }
    
    /**
     * Update session last activity
     */
    private function updateSessionActivity($session_id) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE chat_sessions SET updated_at = NOW() WHERE session_id = ?
            ");
            $stmt->execute([$session_id]);
            
        } catch (PDOException $e) {
            error_log("Error updating session activity: " . $e->getMessage());
        }
    }
    
    /**
     * Trigger real-time chat notification
     */
    private function triggerChatNotification($session_id, $sender_id, $sender_type, $message) {
        // This would integrate with the real-time notification system
        try {
            // Get session info to determine who to notify
            $session = $this->getChatSession($session_id);
            
            if ($session) {
                require_once 'NotificationManager.php';
                $notificationManager = new NotificationManager();
                
                if ($sender_type === 'customer' && $session['agent_id']) {
                    // Notify agent
                    $notificationManager->createNotification(
                        $session['agent_id'],
                        'chat',
                        "New message from {$session['customer_name']}",
                        substr($message, 0, 100) . (strlen($message) > 100 ? '...' : ''),
                        [
                            'session_id' => $session_id,
                            'sender_type' => $sender_type,
                            'action_url' => "admin/chat.php?session={$session_id}"
                        ],
                        'high'
                    );
                } elseif ($sender_type === 'agent' && $session['user_id']) {
                    // Notify customer
                    $notificationManager->createNotification(
                        $session['user_id'],
                        'chat',
                        "New message from support",
                        substr($message, 0, 100) . (strlen($message) > 100 ? '...' : ''),
                        [
                            'session_id' => $session_id,
                            'sender_type' => $sender_type,
                            'action_url' => "chat.php?session={$session_id}"
                        ],
                        'high'
                    );
                }
            }
            
        } catch (Exception $e) {
            error_log("Error triggering chat notification: " . $e->getMessage());
        }
    }
    
    /**
     * Get agent info
     */
    private function getAgentInfo($agent_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT name, email, profile_picture FROM admin_users WHERE admin_id = ?
            ");
            $stmt->execute([$agent_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting agent info: " . $e->getMessage());
            return ['name' => 'Support Agent'];
        }
    }
    
    /**
     * Send satisfaction survey
     */
    private function sendSatisfactionSurvey($session_id) {
        $survey_message = "How was your experience with our support today? Please rate us: ⭐⭐⭐⭐⭐";
        $this->sendAutomatedMessage($session_id, $survey_message);
    }
    
    /**
     * Get chat statistics
     */
    public function getChatStatistics($date_from = null, $date_to = null) {
        try {
            $where_clause = "WHERE 1=1";
            $params = [];
            
            if ($date_from) {
                $where_clause .= " AND created_at >= ?";
                $params[] = $date_from;
            }
            
            if ($date_to) {
                $where_clause .= " AND created_at <= ?";
                $params[] = $date_to;
            }
            
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_sessions,
                    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_sessions,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_sessions,
                    SUM(CASE WHEN status = 'waiting' THEN 1 ELSE 0 END) as waiting_sessions,
                    AVG(CASE WHEN closed_at IS NOT NULL THEN 
                        TIMESTAMPDIFF(MINUTE, created_at, closed_at) 
                        ELSE NULL END) as avg_session_duration
                FROM chat_sessions 
                $where_clause
            ");
            $stmt->execute($params);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting chat statistics: " . $e->getMessage());
            return [];
        }
    }
}
?>
