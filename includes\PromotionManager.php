<?php
/**
 * Promotion Manager
 * Handles discounts, coupons, flash sales, and promotional campaigns
 */

class PromotionManager {
    private $conn;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * Get active promotions
     */
    public function getActivePromotions($type = null, $limit = null) {
        try {
            $sql = "
                SELECT * FROM promotions 
                WHERE is_active = 1 
                AND start_date <= NOW() 
                AND (end_date IS NULL OR end_date >= NOW())
            ";
            
            if ($type) {
                $sql .= " AND type = ?";
            }
            
            $sql .= " ORDER BY priority DESC, created_at DESC";
            
            if ($limit) {
                $sql .= " LIMIT ?";
            }
            
            $stmt = $this->conn->prepare($sql);
            $params = [];
            
            if ($type) {
                $params[] = $type;
            }
            if ($limit) {
                $params[] = $limit;
            }
            
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting active promotions: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Apply promotion to cart
     */
    public function applyPromotion($promotion_code, $cart_items, $user_id = null) {
        try {
            // Get promotion details
            $promotion = $this->getPromotionByCode($promotion_code);
            
            if (!$promotion) {
                return ['success' => false, 'message' => 'Invalid promotion code'];
            }
            
            // Validate promotion
            $validation = $this->validatePromotion($promotion, $cart_items, $user_id);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // Calculate discount
            $discount_result = $this->calculateDiscount($promotion, $cart_items);
            
            // Record promotion usage
            if ($user_id) {
                $this->recordPromotionUsage($promotion['promotion_id'], $user_id, $discount_result['discount_amount']);
            }
            
            return [
                'success' => true,
                'promotion' => $promotion,
                'discount_amount' => $discount_result['discount_amount'],
                'discount_details' => $discount_result['details'],
                'message' => "Promotion '{$promotion['name']}' applied successfully!"
            ];
            
        } catch (Exception $e) {
            error_log("Error applying promotion: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to apply promotion'];
        }
    }
    
    /**
     * Get promotion by code
     */
    private function getPromotionByCode($code) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM promotions 
                WHERE code = ? AND is_active = 1 
                AND start_date <= NOW() 
                AND (end_date IS NULL OR end_date >= NOW())
            ");
            $stmt->execute([$code]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting promotion by code: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Validate promotion
     */
    private function validatePromotion($promotion, $cart_items, $user_id = null) {
        // Check usage limit
        if ($promotion['usage_limit'] > 0) {
            $current_usage = $this->getPromotionUsageCount($promotion['promotion_id']);
            if ($current_usage >= $promotion['usage_limit']) {
                return ['valid' => false, 'message' => 'Promotion usage limit exceeded'];
            }
        }
        
        // Check user usage limit
        if ($user_id && $promotion['user_usage_limit'] > 0) {
            $user_usage = $this->getUserPromotionUsageCount($promotion['promotion_id'], $user_id);
            if ($user_usage >= $promotion['user_usage_limit']) {
                return ['valid' => false, 'message' => 'You have reached the usage limit for this promotion'];
            }
        }
        
        // Check minimum order amount
        if ($promotion['min_order_amount'] > 0) {
            $cart_total = array_sum(array_column($cart_items, 'total_price'));
            if ($cart_total < $promotion['min_order_amount']) {
                return [
                    'valid' => false, 
                    'message' => 'Minimum order amount is Rp ' . number_format($promotion['min_order_amount'])
                ];
            }
        }
        
        // Check applicable products
        if (!empty($promotion['applicable_products'])) {
            $applicable_products = json_decode($promotion['applicable_products'], true);
            $has_applicable_product = false;
            
            foreach ($cart_items as $item) {
                if (in_array($item['product_id'], $applicable_products)) {
                    $has_applicable_product = true;
                    break;
                }
            }
            
            if (!$has_applicable_product) {
                return ['valid' => false, 'message' => 'This promotion is not applicable to items in your cart'];
            }
        }
        
        // Check applicable categories
        if (!empty($promotion['applicable_categories'])) {
            $applicable_categories = json_decode($promotion['applicable_categories'], true);
            $has_applicable_category = false;
            
            foreach ($cart_items as $item) {
                if (in_array($item['category_id'], $applicable_categories)) {
                    $has_applicable_category = true;
                    break;
                }
            }
            
            if (!$has_applicable_category) {
                return ['valid' => false, 'message' => 'This promotion is not applicable to items in your cart'];
            }
        }
        
        return ['valid' => true];
    }
    
    /**
     * Calculate discount amount
     */
    private function calculateDiscount($promotion, $cart_items) {
        $discount_amount = 0;
        $details = [];
        
        switch ($promotion['discount_type']) {
            case 'percentage':
                $applicable_total = $this->getApplicableTotal($promotion, $cart_items);
                $discount_amount = $applicable_total * ($promotion['discount_value'] / 100);
                
                // Apply maximum discount limit
                if ($promotion['max_discount_amount'] > 0) {
                    $discount_amount = min($discount_amount, $promotion['max_discount_amount']);
                }
                
                $details = [
                    'type' => 'percentage',
                    'percentage' => $promotion['discount_value'],
                    'applicable_total' => $applicable_total,
                    'max_discount' => $promotion['max_discount_amount']
                ];
                break;
                
            case 'fixed':
                $discount_amount = $promotion['discount_value'];
                $details = [
                    'type' => 'fixed',
                    'amount' => $promotion['discount_value']
                ];
                break;
                
            case 'buy_x_get_y':
                $discount_result = $this->calculateBuyXGetYDiscount($promotion, $cart_items);
                $discount_amount = $discount_result['discount'];
                $details = $discount_result['details'];
                break;
                
            case 'free_shipping':
                // Free shipping discount will be handled in shipping calculation
                $discount_amount = 0;
                $details = [
                    'type' => 'free_shipping',
                    'message' => 'Free shipping applied'
                ];
                break;
        }
        
        return [
            'discount_amount' => $discount_amount,
            'details' => $details
        ];
    }
    
    /**
     * Get applicable total for percentage discounts
     */
    private function getApplicableTotal($promotion, $cart_items) {
        $total = 0;
        
        // If specific products are defined
        if (!empty($promotion['applicable_products'])) {
            $applicable_products = json_decode($promotion['applicable_products'], true);
            foreach ($cart_items as $item) {
                if (in_array($item['product_id'], $applicable_products)) {
                    $total += $item['total_price'];
                }
            }
        }
        // If specific categories are defined
        elseif (!empty($promotion['applicable_categories'])) {
            $applicable_categories = json_decode($promotion['applicable_categories'], true);
            foreach ($cart_items as $item) {
                if (in_array($item['category_id'], $applicable_categories)) {
                    $total += $item['total_price'];
                }
            }
        }
        // Apply to all items
        else {
            $total = array_sum(array_column($cart_items, 'total_price'));
        }
        
        return $total;
    }
    
    /**
     * Calculate Buy X Get Y discount
     */
    private function calculateBuyXGetYDiscount($promotion, $cart_items) {
        $conditions = json_decode($promotion['conditions'], true);
        $buy_quantity = $conditions['buy_quantity'] ?? 2;
        $get_quantity = $conditions['get_quantity'] ?? 1;
        $discount_percentage = $conditions['discount_percentage'] ?? 100; // 100% = free
        
        $discount = 0;
        $free_items = [];
        
        // Sort items by price (cheapest first for free items)
        usort($cart_items, function($a, $b) {
            return $a['price'] <=> $b['price'];
        });
        
        foreach ($cart_items as $item) {
            $eligible_sets = floor($item['quantity'] / $buy_quantity);
            $free_items_count = $eligible_sets * $get_quantity;
            
            if ($free_items_count > 0) {
                $item_discount = $item['price'] * $free_items_count * ($discount_percentage / 100);
                $discount += $item_discount;
                
                $free_items[] = [
                    'product_name' => $item['product_name'],
                    'quantity' => $free_items_count,
                    'discount_amount' => $item_discount
                ];
            }
        }
        
        return [
            'discount' => $discount,
            'details' => [
                'type' => 'buy_x_get_y',
                'buy_quantity' => $buy_quantity,
                'get_quantity' => $get_quantity,
                'discount_percentage' => $discount_percentage,
                'free_items' => $free_items
            ]
        ];
    }
    
    /**
     * Get flash sales
     */
    public function getFlashSales($limit = 10) {
        try {
            $stmt = $this->conn->prepare("
                SELECT fs.*, p.name as product_name, p.price as original_price, p.image
                FROM flash_sales fs
                JOIN products p ON fs.product_id = p.product_id
                WHERE fs.is_active = 1 
                AND fs.start_time <= NOW() 
                AND fs.end_time > NOW()
                AND fs.sold_quantity < fs.stock_quantity
                ORDER BY fs.discount_percentage DESC, fs.end_time ASC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            $flash_sales = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Calculate discounted price and time remaining
            foreach ($flash_sales as &$sale) {
                $sale['discounted_price'] = $sale['original_price'] * (1 - $sale['discount_percentage'] / 100);
                $sale['savings'] = $sale['original_price'] - $sale['discounted_price'];
                $sale['time_remaining'] = strtotime($sale['end_time']) - time();
                $sale['progress_percentage'] = ($sale['sold_quantity'] / $sale['stock_quantity']) * 100;
            }
            
            return $flash_sales;
            
        } catch (PDOException $e) {
            error_log("Error getting flash sales: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Record promotion usage
     */
    private function recordPromotionUsage($promotion_id, $user_id, $discount_amount) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO promotion_usage (promotion_id, user_id, discount_amount, used_at)
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$promotion_id, $user_id, $discount_amount]);
            
        } catch (PDOException $e) {
            error_log("Error recording promotion usage: " . $e->getMessage());
        }
    }
    
    /**
     * Get promotion usage count
     */
    private function getPromotionUsageCount($promotion_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) FROM promotion_usage WHERE promotion_id = ?
            ");
            $stmt->execute([$promotion_id]);
            return $stmt->fetchColumn();
            
        } catch (PDOException $e) {
            error_log("Error getting promotion usage count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get user promotion usage count
     */
    private function getUserPromotionUsageCount($promotion_id, $user_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) FROM promotion_usage 
                WHERE promotion_id = ? AND user_id = ?
            ");
            $stmt->execute([$promotion_id, $user_id]);
            return $stmt->fetchColumn();
            
        } catch (PDOException $e) {
            error_log("Error getting user promotion usage count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Create new promotion
     */
    public function createPromotion($data) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO promotions (
                    name, description, code, type, discount_type, discount_value,
                    max_discount_amount, min_order_amount, usage_limit, user_usage_limit,
                    applicable_products, applicable_categories, conditions,
                    start_date, end_date, priority, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['name'],
                $data['description'],
                $data['code'],
                $data['type'],
                $data['discount_type'],
                $data['discount_value'],
                $data['max_discount_amount'] ?? null,
                $data['min_order_amount'] ?? 0,
                $data['usage_limit'] ?? 0,
                $data['user_usage_limit'] ?? 0,
                $data['applicable_products'] ?? null,
                $data['applicable_categories'] ?? null,
                $data['conditions'] ?? null,
                $data['start_date'],
                $data['end_date'] ?? null,
                $data['priority'] ?? 1,
                $data['is_active'] ?? 1
            ]);
            
            return [
                'success' => true,
                'promotion_id' => $this->conn->lastInsertId(),
                'message' => 'Promotion created successfully'
            ];
            
        } catch (PDOException $e) {
            error_log("Error creating promotion: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to create promotion'];
        }
    }
    
    /**
     * Get user's available coupons
     */
    public function getUserCoupons($user_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT p.*, 
                       COALESCE(pu.usage_count, 0) as user_usage_count,
                       CASE 
                           WHEN p.user_usage_limit > 0 AND COALESCE(pu.usage_count, 0) >= p.user_usage_limit THEN 0
                           WHEN p.usage_limit > 0 AND p.total_usage >= p.usage_limit THEN 0
                           ELSE 1
                       END as can_use
                FROM promotions p
                LEFT JOIN (
                    SELECT promotion_id, COUNT(*) as usage_count
                    FROM promotion_usage 
                    WHERE user_id = ?
                    GROUP BY promotion_id
                ) pu ON p.promotion_id = pu.promotion_id
                WHERE p.is_active = 1 
                AND p.start_date <= NOW() 
                AND (p.end_date IS NULL OR p.end_date >= NOW())
                AND p.type IN ('coupon', 'voucher')
                ORDER BY p.priority DESC, p.created_at DESC
            ");
            $stmt->execute([$user_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting user coupons: " . $e->getMessage());
            return [];
        }
    }
}
?>
