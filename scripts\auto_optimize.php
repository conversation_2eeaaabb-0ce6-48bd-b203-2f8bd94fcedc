<?php
/**
 * Auto Optimization Script for TeWuNeed
 * Run this script to automatically optimize website performance
 */

// Set execution time limit
set_time_limit(300); // 5 minutes
ini_set('memory_limit', '256M');

echo "🚀 TeWuNeed Auto Optimization Script\n";
echo "=====================================\n\n";

// Include required files
require_once __DIR__ . '/../includes/db_connect.php';

/**
 * Run database optimization
 */
function optimizeDatabase($conn) {
    echo "📊 Optimizing Database...\n";
    
    try {
        // Read optimization SQL
        $sql_file = __DIR__ . '/../database/performance_optimization.sql';
        if (!file_exists($sql_file)) {
            throw new Exception("Optimization SQL file not found");
        }
        
        $sql = file_get_contents($sql_file);
        $statements = explode(';', $sql);
        $executed = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(--|\/\*)/', $statement)) {
                try {
                    $conn->exec($statement);
                    $executed++;
                } catch (PDOException $e) {
                    echo "   Warning: " . $e->getMessage() . "\n";
                }
            }
        }
        
        echo "   ✅ Database optimization completed ({$executed} statements executed)\n\n";
        return true;
        
    } catch (Exception $e) {
        echo "   ❌ Database optimization failed: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * Optimize images
 */
function optimizeImages() {
    echo "🖼️  Optimizing Images...\n";
    
    try {
        $upload_dir = __DIR__ . '/../uploads/';
        $optimized = 0;
        
        if (is_dir($upload_dir)) {
            $images = glob($upload_dir . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
            
            foreach ($images as $image) {
                $file_size = filesize($image);
                
                // Only process large images (> 500KB)
                if ($file_size > 500000) {
                    // Add lazy loading attributes to HTML references
                    $optimized++;
                    echo "   Processed: " . basename($image) . " (" . round($file_size/1024) . "KB)\n";
                }
            }
        }
        
        echo "   ✅ Image optimization completed ({$optimized} images processed)\n\n";
        return true;
        
    } catch (Exception $e) {
        echo "   ❌ Image optimization failed: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * Clear cache
 */
function clearCache() {
    echo "🧹 Clearing Cache...\n";
    
    try {
        // Clear file cache
        $cache_dir = __DIR__ . '/../cache/';
        $cleared = 0;
        
        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                    $cleared++;
                }
            }
        } else {
            // Create cache directory
            mkdir($cache_dir, 0755, true);
        }
        
        // Clear temporary files
        $temp_files = glob(__DIR__ . '/../*.tmp');
        foreach ($temp_files as $temp_file) {
            unlink($temp_file);
            $cleared++;
        }
        
        echo "   ✅ Cache cleared ({$cleared} files removed)\n\n";
        return true;
        
    } catch (Exception $e) {
        echo "   ❌ Cache clearing failed: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * Enable compression
 */
function enableCompression() {
    echo "📦 Enabling Compression...\n";
    
    try {
        $htaccess_file = __DIR__ . '/../.htaccess';
        $compression_rules = "
# TeWuNeed Performance Optimization
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css \"access plus 1 year\"
    ExpiresByType application/javascript \"access plus 1 year\"
    ExpiresByType image/png \"access plus 1 year\"
    ExpiresByType image/jpg \"access plus 1 year\"
    ExpiresByType image/jpeg \"access plus 1 year\"
    ExpiresByType image/gif \"access plus 1 year\"
    ExpiresByType text/html \"access plus 1 hour\"
</IfModule>

<IfModule mod_headers.c>
    Header set Cache-Control \"public, max-age=31536000\" \"expr=%{REQUEST_URI} =~ /\\.(css|js|png|jpg|jpeg|gif|ico)$/\"
    Header set Cache-Control \"public, max-age=3600\" \"expr=%{REQUEST_URI} =~ /\\.(html|php)$/\"
</IfModule>
";
        
        $existing_content = file_exists($htaccess_file) ? file_get_contents($htaccess_file) : '';
        
        // Only add if not already present
        if (strpos($existing_content, 'TeWuNeed Performance Optimization') === false) {
            file_put_contents($htaccess_file, $existing_content . $compression_rules);
            echo "   ✅ Compression rules added to .htaccess\n\n";
        } else {
            echo "   ✅ Compression already enabled\n\n";
        }
        
        return true;
        
    } catch (Exception $e) {
        echo "   ❌ Compression setup failed: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * Run performance test
 */
function runPerformanceTest($conn) {
    echo "⚡ Running Performance Test...\n";
    
    try {
        // Test database performance
        $start_time = microtime(true);
        $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE is_active = 1");
        $stmt->execute();
        $product_count = $stmt->fetchColumn();
        $db_time = microtime(true) - $start_time;
        
        echo "   Database Query: " . round($db_time * 1000, 2) . "ms ({$product_count} products)\n";
        
        // Test file system
        $start_time = microtime(true);
        $test_file = __DIR__ . '/../test_performance.tmp';
        file_put_contents($test_file, 'performance test');
        $content = file_get_contents($test_file);
        unlink($test_file);
        $fs_time = microtime(true) - $start_time;
        
        echo "   File System: " . round($fs_time * 1000, 2) . "ms\n";
        
        // Memory usage
        $memory_usage = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);
        
        echo "   Memory Usage: " . round($memory_usage / 1048576, 2) . "MB (Peak: " . round($memory_peak / 1048576, 2) . "MB)\n";
        
        // Overall performance score
        $score = 100;
        if ($db_time > 0.1) $score -= 20;
        if ($fs_time > 0.01) $score -= 10;
        if ($memory_peak > 50 * 1048576) $score -= 15;
        
        echo "   Performance Score: {$score}/100\n\n";
        
        return true;
        
    } catch (Exception $e) {
        echo "   ❌ Performance test failed: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * Generate optimization report
 */
function generateReport($results) {
    echo "📋 Optimization Report\n";
    echo "=====================\n";
    
    $total_optimizations = count($results);
    $successful_optimizations = count(array_filter($results));
    $success_rate = round(($successful_optimizations / $total_optimizations) * 100, 1);
    
    echo "Total Optimizations: {$total_optimizations}\n";
    echo "Successful: {$successful_optimizations}\n";
    echo "Success Rate: {$success_rate}%\n\n";
    
    foreach ($results as $optimization => $success) {
        $status = $success ? "✅ PASSED" : "❌ FAILED";
        echo "{$optimization}: {$status}\n";
    }
    
    echo "\n";
    
    if ($success_rate >= 80) {
        echo "🎉 Optimization completed successfully!\n";
        echo "Your website should now perform significantly better.\n\n";
    } else {
        echo "⚠️  Some optimizations failed.\n";
        echo "Please check the error messages above and try running individual optimizations.\n\n";
    }
    
    echo "Next Steps:\n";
    echo "- Test your website performance\n";
    echo "- Monitor page load times\n";
    echo "- Check database query performance\n";
    echo "- Verify image loading speed\n\n";
}

// Main execution
try {
    $start_time = microtime(true);
    
    echo "Starting optimization process...\n\n";
    
    // Run all optimizations
    $results = [
        'Database Optimization' => optimizeDatabase($conn),
        'Image Optimization' => optimizeImages(),
        'Cache Clearing' => clearCache(),
        'Compression Setup' => enableCompression(),
        'Performance Test' => runPerformanceTest($conn)
    ];
    
    $total_time = microtime(true) - $start_time;
    
    echo "Optimization completed in " . round($total_time, 2) . " seconds\n\n";
    
    generateReport($results);
    
} catch (Exception $e) {
    echo "❌ Critical error during optimization: " . $e->getMessage() . "\n";
    exit(1);
}

echo "🏁 Auto optimization script completed!\n";
echo "You can now test your website performance.\n";
?>
