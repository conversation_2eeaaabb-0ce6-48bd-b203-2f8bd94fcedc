<?php
// File untuk mengambil detail produk via AJAX
ob_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Cek ID produk dari GET atau POST
$productId = null;
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $productId = (int)$_GET['id'];
} elseif (isset($_POST['product_id']) && is_numeric($_POST['product_id'])) {
    $productId = (int)$_POST['product_id'];
}

if (!$productId) {
    ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'ID produk tidak valid']);
    exit;
}

// Ambil data produk dari database
try {
    global $conn;

    $stmt = $conn->prepare("
        SELECT p.*, c.NAME as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        WHERE p.product_id = ?
    ");

    $stmt->execute([$productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Produk tidak ditemukan']);
        exit;
    }

    // Format data untuk response
    $response = [
        'success' => true,
        'id' => $product['product_id'],
        'name' => $product['NAME'], // Use NAME field from database
        'description' => $product['description'],
        'price' => $product['price'],
        'price_formatted' => number_format($product['price'], 0, ',', '.'),
        'stock' => $product['stock'],
        'category' => $product['category_name'],
        'image' => !empty($product['image']) ? $product['image'] : 'product-default.jpg'
    ];

    ob_end_clean();
    echo json_encode($response);

} catch (Exception $e) {
    ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
