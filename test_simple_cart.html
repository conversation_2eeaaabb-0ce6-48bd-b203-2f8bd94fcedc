<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Cart Test - TeWuNeed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">🛒 Simple Cart Test</h1>
                
                <!-- Cart Count Display -->
                <div class="alert alert-info">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Current Cart Count: <span id="cart-count" class="fw-bold">0</span> items
                </div>

                <!-- Test Product Cards -->
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <img src="https://via.placeholder.com/300x200?text=Product+1" class="card-img-top" alt="Test Product 1">
                            <div class="card-body">
                                <h5 class="card-title">Test Product 1</h5>
                                <p class="card-text">This is a test product for simple cart functionality.</p>
                                <p class="h5 text-primary">Rp 50,000</p>
                                
                                <button class="btn btn-primary add-to-cart-btn" data-product-id="1">
                                    <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <img src="https://via.placeholder.com/300x200?text=Product+2" class="card-img-top" alt="Test Product 2">
                            <div class="card-body">
                                <h5 class="card-title">Test Product 2</h5>
                                <p class="card-text">Another test product for simple cart functionality.</p>
                                <p class="h5 text-primary">Rp 75,000</p>
                                
                                <button class="btn btn-primary add-to-cart-btn" data-product-id="2">
                                    <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <img src="https://via.placeholder.com/300x200?text=Product+3" class="card-img-top" alt="Test Product 3">
                            <div class="card-body">
                                <h5 class="card-title">Test Product 3</h5>
                                <p class="card-text">Third test product for simple cart functionality.</p>
                                <p class="h5 text-primary">Rp 100,000</p>
                                
                                <button class="btn btn-primary add-to-cart-btn" data-product-id="3">
                                    <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Test Buttons -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>Manual Tests</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info me-2 mb-2" onclick="loadCartCount()">
                            <i class="fas fa-refresh me-1"></i>Refresh Cart Count
                        </button>
                        <button class="btn btn-success me-2 mb-2" onclick="testDirectAdd()">
                            <i class="fas fa-plus me-1"></i>Direct Add Test
                        </button>
                        <button class="btn btn-warning me-2 mb-2" onclick="showTestNotification()">
                            <i class="fas fa-bell me-1"></i>Test Notification
                        </button>
                    </div>
                </div>

                <!-- Debug Console -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-terminal me-2"></i>Debug Console</h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-console" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace;">
                            <div class="text-success">Simple Cart Debug Console Ready...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load Simple Cart JavaScript -->
    <script src="assets/js/simple-cart.js"></script>
    
    <script>
        // Override console.log to show in debug console
        const originalLog = console.log;
        const debugConsole = document.getElementById('debug-console');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
            
            debugConsole.appendChild(logEntry);
            debugConsole.scrollTop = debugConsole.scrollHeight;
        };
        
        // Test functions
        function testDirectAdd() {
            console.log('Testing direct add to cart...');
            simpleAddToCart(1, document.querySelector('[data-product-id="1"]'));
        }
        
        function showTestNotification() {
            console.log('Testing notification system...');
            showSimpleNotification('This is a test notification!', 'info');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Simple cart test page loaded');
            console.log('Click the "Add to Cart" buttons to test functionality');
        });
    </script>
</body>
</html>
