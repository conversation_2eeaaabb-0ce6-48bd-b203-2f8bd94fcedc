<?php
/**
 * Fix Cart Database Schema
 * Run this script to fix database issues with cart functionality
 */

// Set execution time and memory limits
set_time_limit(300);
ini_set('memory_limit', '256M');

echo "🔧 Cart Database Fix Script\n";
echo "===========================\n\n";

// Include database connection
require_once 'includes/db_connect.php';

if (!$conn) {
    die("❌ Database connection failed!\n");
}

echo "✅ Database connected successfully\n\n";

/**
 * Execute SQL file
 */
function executeSQLFile($conn, $filename) {
    echo "📄 Executing SQL file: {$filename}\n";
    
    if (!file_exists($filename)) {
        echo "❌ SQL file not found: {$filename}\n";
        return false;
    }
    
    $sql = file_get_contents($filename);
    $statements = explode(';', $sql);
    $executed = 0;
    $errors = [];
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^(--|\/\*)/', $statement)) {
            try {
                $conn->exec($statement);
                $executed++;
                echo "   ✅ Statement executed\n";
            } catch (PDOException $e) {
                $errors[] = $e->getMessage();
                echo "   ⚠️  Warning: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "   📊 Executed {$executed} statements\n";
    if (!empty($errors)) {
        echo "   ⚠️  " . count($errors) . " warnings/errors\n";
    }
    echo "\n";
    
    return true;
}

/**
 * Check table structure
 */
function checkTableStructure($conn, $tableName) {
    echo "🔍 Checking table structure: {$tableName}\n";
    
    try {
        $stmt = $conn->prepare("DESCRIBE {$tableName}");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "   📋 Columns found:\n";
        foreach ($columns as $column) {
            echo "      - {$column['Field']} ({$column['Type']})\n";
        }
        echo "\n";
        
        return $columns;
    } catch (PDOException $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * Test cart functionality
 */
function testCartFunctionality($conn) {
    echo "🧪 Testing cart functionality\n";
    
    try {
        // Test 1: Create test cart
        echo "   Test 1: Creating test cart...\n";
        $stmt = $conn->prepare("INSERT INTO carts (user_id) VALUES (999)");
        $stmt->execute();
        $cart_id = $conn->lastInsertId();
        echo "      ✅ Cart created with ID: {$cart_id}\n";
        
        // Test 2: Add item to cart
        echo "   Test 2: Adding item to cart...\n";
        $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity) VALUES (?, 1, 2)");
        $stmt->execute([$cart_id]);
        echo "      ✅ Item added to cart\n";
        
        // Test 3: Get cart count
        echo "   Test 3: Getting cart count...\n";
        $stmt = $conn->prepare("SELECT SUM(quantity) FROM cart_items WHERE cart_id = ?");
        $stmt->execute([$cart_id]);
        $count = $stmt->fetchColumn();
        echo "      ✅ Cart count: {$count}\n";
        
        // Test 4: Update cart item
        echo "   Test 4: Updating cart item...\n";
        $stmt = $conn->prepare("UPDATE cart_items SET quantity = 3 WHERE cart_id = ? AND product_id = 1");
        $stmt->execute([$cart_id]);
        echo "      ✅ Cart item updated\n";
        
        // Cleanup
        echo "   Cleanup: Removing test data...\n";
        $stmt = $conn->prepare("DELETE FROM cart_items WHERE cart_id = ?");
        $stmt->execute([$cart_id]);
        $stmt = $conn->prepare("DELETE FROM carts WHERE cart_id = ?");
        $stmt->execute([$cart_id]);
        echo "      ✅ Test data cleaned up\n";
        
        echo "   🎉 All cart functionality tests passed!\n\n";
        return true;
        
    } catch (PDOException $e) {
        echo "   ❌ Test failed: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * Fix missing columns manually
 */
function fixMissingColumns($conn) {
    echo "🔧 Fixing missing columns manually\n";
    
    $fixes = [
        "ALTER TABLE cart_items ADD COLUMN IF NOT EXISTS added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        "ALTER TABLE cart_items ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "ALTER TABLE carts ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        "ALTER TABLE carts ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "ALTER TABLE products ADD COLUMN IF NOT EXISTS is_active TINYINT(1) DEFAULT 1",
        "ALTER TABLE products ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
    ];
    
    foreach ($fixes as $sql) {
        try {
            $conn->exec($sql);
            echo "   ✅ " . substr($sql, 0, 50) . "...\n";
        } catch (PDOException $e) {
            // Try alternative syntax for older MySQL versions
            $table = '';
            $column = '';
            if (preg_match('/ALTER TABLE (\w+) ADD COLUMN IF NOT EXISTS (\w+)/', $sql, $matches)) {
                $table = $matches[1];
                $column = $matches[2];
                
                // Check if column exists
                $checkSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? AND COLUMN_NAME = ?";
                $stmt = $conn->prepare($checkSql);
                $stmt->execute([$table, $column]);
                $exists = $stmt->fetchColumn();
                
                if (!$exists) {
                    // Remove IF NOT EXISTS and try again
                    $simpleSql = str_replace(' IF NOT EXISTS', '', $sql);
                    try {
                        $conn->exec($simpleSql);
                        echo "   ✅ " . substr($simpleSql, 0, 50) . "...\n";
                    } catch (PDOException $e2) {
                        echo "   ⚠️  Could not add column {$column} to {$table}: " . $e2->getMessage() . "\n";
                    }
                } else {
                    echo "   ✅ Column {$column} already exists in {$table}\n";
                }
            }
        }
    }
    echo "\n";
}

// Main execution
try {
    echo "🚀 Starting cart database fix process...\n\n";
    
    // Step 1: Check current table structures
    echo "Step 1: Checking current table structures\n";
    echo "==========================================\n";
    checkTableStructure($conn, 'carts');
    checkTableStructure($conn, 'cart_items');
    checkTableStructure($conn, 'products');
    
    // Step 2: Fix missing columns manually
    echo "Step 2: Fixing missing columns\n";
    echo "===============================\n";
    fixMissingColumns($conn);
    
    // Step 3: Execute SQL fix file if exists
    echo "Step 3: Executing SQL fix file\n";
    echo "===============================\n";
    if (file_exists('database/fix_cart_tables.sql')) {
        executeSQLFile($conn, 'database/fix_cart_tables.sql');
    } else {
        echo "⚠️  SQL fix file not found, skipping...\n\n";
    }
    
    // Step 4: Test cart functionality
    echo "Step 4: Testing cart functionality\n";
    echo "===================================\n";
    $testResult = testCartFunctionality($conn);
    
    // Step 5: Final verification
    echo "Step 5: Final verification\n";
    echo "==========================\n";
    echo "🔍 Checking final table structures:\n\n";
    checkTableStructure($conn, 'carts');
    checkTableStructure($conn, 'cart_items');
    
    // Summary
    echo "📋 SUMMARY\n";
    echo "==========\n";
    if ($testResult) {
        echo "🎉 Cart database fix completed successfully!\n";
        echo "✅ All cart functionality should now work properly.\n\n";
        
        echo "Next steps:\n";
        echo "1. Test add to cart functionality on your website\n";
        echo "2. Check the test page: test_cart_functionality.php\n";
        echo "3. Monitor error logs for any remaining issues\n\n";
    } else {
        echo "⚠️  Some issues may remain. Please check the errors above.\n";
        echo "You may need to manually fix remaining database issues.\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Critical error during fix process: " . $e->getMessage() . "\n";
    exit(1);
}

echo "🏁 Cart database fix script completed!\n";
?>
