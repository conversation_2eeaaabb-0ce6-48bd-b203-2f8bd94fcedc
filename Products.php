
<?php
// Start session first
session_start();

// Include required files
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';
require_once 'includes/firebase_auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Get user ID from session
$user_id = $_SESSION['user_id'] ?? $_SESSION['local_user_id'] ?? null;

// Set page variables for header
$page = 'products';
$page_title = 'Products';

// Get all categories
$categoryQuery = "SELECT * FROM categories ORDER BY NAME";
$categoryStmt = $conn->prepare($categoryQuery);
$categoryStmt->execute();
$categories = $categoryStmt->fetchAll();

// Get products with filters
$whereClause = "WHERE p.is_active = 1";
$params = [];

// Category filter
if (isset($_GET['category']) && !empty($_GET['category'])) {
    $whereClause .= " AND c.slug = ?";
    $params[] = $_GET['category'];
}

// Search filter
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $whereClause .= " AND (p.NAME LIKE ? OR p.description LIKE ? OR c.NAME LIKE ?)";
    $searchTerm = '%' . $_GET['search'] . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

// Price range filter
if (isset($_GET['min_price']) && !empty($_GET['min_price'])) {
    $whereClause .= " AND p.price >= ?";
    $params[] = (float)$_GET['min_price'];
}

if (isset($_GET['max_price']) && !empty($_GET['max_price'])) {
    $whereClause .= " AND p.price <= ?";
    $params[] = (float)$_GET['max_price'];
}

// Stock filter
if (isset($_GET['in_stock']) && $_GET['in_stock'] == '1') {
    $whereClause .= " AND p.stock > 0";
}

// Rating filter (if rating system exists)
if (isset($_GET['min_rating']) && !empty($_GET['min_rating'])) {
    $whereClause .= " AND COALESCE(prs.average_rating, 0) >= ?";
    $params[] = (float)$_GET['min_rating'];
}

// Sorting options
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'name_asc';
$order_clause = "";

switch($sort_by) {
    case 'name_asc':
        $order_clause = "ORDER BY p.NAME ASC";
        break;
    case 'name_desc':
        $order_clause = "ORDER BY p.NAME DESC";
        break;
    case 'price_low':
        $order_clause = "ORDER BY p.price ASC";
        break;
    case 'price_high':
        $order_clause = "ORDER BY p.price DESC";
        break;
    case 'newest':
        $order_clause = "ORDER BY p.created_at DESC";
        break;
    case 'oldest':
        $order_clause = "ORDER BY p.created_at ASC";
        break;
    case 'popularity':
        $order_clause = "ORDER BY COALESCE(prs.total_reviews, 0) DESC, COALESCE(prs.average_rating, 0) DESC";
        break;
    case 'rating':
        $order_clause = "ORDER BY COALESCE(prs.average_rating, 0) DESC, COALESCE(prs.total_reviews, 0) DESC";
        break;
    case 'stock':
        $order_clause = "ORDER BY p.stock DESC";
        break;
    default:
        $order_clause = "ORDER BY p.NAME ASC";
}

// Pagination setup
$page_number = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 9; // 3 rows of 3 products
$offset = ($page_number - 1) * $items_per_page;

// Count total products for pagination
$count_query = "SELECT COUNT(*) as total FROM products p
                LEFT JOIN categories c ON p.category_id = c.category_id
                LEFT JOIN product_rating_summary prs ON p.product_id = prs.product_id
                $whereClause";
$count_stmt = $conn->prepare($count_query);
$count_stmt->execute($params);
$total_products = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
$total_pages = ceil($total_products / $items_per_page);

// Get products with pagination
$query = "SELECT p.*, c.NAME as category_name, c.slug as category_slug,
                 COALESCE(prs.average_rating, 0) as average_rating,
                 COALESCE(prs.total_reviews, 0) as total_reviews
          FROM products p
          LEFT JOIN categories c ON p.category_id = c.category_id
          LEFT JOIN product_rating_summary prs ON p.product_id = prs.product_id
          $whereClause
          $order_clause
          LIMIT $offset, $items_per_page";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$products = $stmt->fetchAll();

// Preserve existing query parameters for pagination links
$query_params = [];
if (isset($_GET['category']) && !empty($_GET['category'])) {
    $query_params['category'] = $_GET['category'];
}
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $query_params['search'] = $_GET['search'];
}
if (isset($_GET['sort']) && !empty($_GET['sort'])) {
    $query_params['sort'] = $_GET['sort'];
}
if (isset($_GET['min_price']) && !empty($_GET['min_price'])) {
    $query_params['min_price'] = $_GET['min_price'];
}
if (isset($_GET['max_price']) && !empty($_GET['max_price'])) {
    $query_params['max_price'] = $_GET['max_price'];
}
if (isset($_GET['in_stock']) && $_GET['in_stock'] == '1') {
    $query_params['in_stock'] = $_GET['in_stock'];
}
if (isset($_GET['min_rating']) && !empty($_GET['min_rating'])) {
    $query_params['min_rating'] = $_GET['min_rating'];
}
$query_string = http_build_query($query_params);
$pagination_url = 'Products.php?' . ($query_string ? $query_string . '&' : '');

// Include header after all processing
include('includes/header.php');
?>

<!-- Include Toastify CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
<!-- Include Custom Notification CSS -->
<link rel="stylesheet" href="css/notifications.css">

<!-- Custom Styles -->
<style>
.products-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
}

.products-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.products-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.products-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.filter-section {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 30px;
    margin: -30px 20px 40px 20px;
    position: relative;
    z-index: 10;
}

.search-box {
    border-radius: 50px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.search-box:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-search {
    border-radius: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.btn-search:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.dropdown-toggle {
    border-radius: 50px;
    padding: 12px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-outline-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border: none;
}

.btn-outline-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-outline-primary {
    background: linear-gradient(45deg, #007bff, #6610f2);
    color: white;
    border: none;
}

.btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

.results-info {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    border-left: 5px solid #667eea;
}

.product-card {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    background: white;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.product-img {
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.product-card:hover .product-img {
    transform: scale(1.05);
}

.card-body {
    padding: 25px;
}

.card-title {
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.price-tag {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8px 15px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 1.1rem;
    display: inline-block;
    margin-bottom: 15px;
}

.btn-detail {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    border: none;
    border-radius: 25px;
    padding: 8px 15px;
    color: white;
    transition: all 0.3s ease;
}

.btn-detail:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
    color: white;
}

.btn-cart {
    background: linear-gradient(45deg, #00b894, #00cec9);
    border: none;
    border-radius: 25px;
    padding: 8px 15px;
    color: white;
    transition: all 0.3s ease;
}

.btn-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 184, 148, 0.4);
    color: white;
}

.stock-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.limited-stock {
    background: linear-gradient(45deg, #fdcb6e, #e17055);
    color: white;
}

.out-of-stock {
    background: linear-gradient(45deg, #d63031, #74b9ff);
    color: white;
}

.pagination .page-link {
    border-radius: 50px;
    margin: 0 5px;
    border: none;
    color: #667eea;
    font-weight: 600;
    transition: all 0.3s ease;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
}

.pagination .page-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    color: white;
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.dropdown-menu {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    padding: 15px 0;
}

.dropdown-item {
    padding: 10px 20px;
    transition: all 0.3s ease;
    border-radius: 10px;
    margin: 2px 10px;
}

.dropdown-item:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: translateX(5px);
}

.dropdown-item.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-elements::before,
.floating-elements::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
    animation: float 6s ease-in-out infinite;
}

.floating-elements::before {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-elements::after {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.category-badge {
    background: linear-gradient(45deg, #a29bfe, #6c5ce7);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 10px;
}

@media (max-width: 768px) {
    .products-hero h1 {
        font-size: 2rem;
    }

    .filter-section {
        margin: -20px 10px 30px 10px;
        padding: 20px;
    }

    .product-card {
        margin-bottom: 20px;
    }
}
</style>

<!-- Hero Section -->
<div class="products-hero">
    <div class="floating-elements"></div>
    <div class="container text-center position-relative" style="z-index: 2;">
        <h1>🛍️ Jelajahi Produk Kami</h1>
        <p>Temukan ribuan produk berkualitas dengan harga terbaik</p>
    </div>
</div>

<!-- Page Content -->
<div class="container mb-5">

    <!-- Advanced Filter Section -->
    <div class="filter-section">
        <!-- Search Bar -->
        <div class="row mb-4">
            <div class="col-md-8">
                <form action="Products.php" method="GET" class="d-flex">
                    <!-- Preserve existing filters -->
                    <?php foreach(['category', 'sort', 'min_price', 'max_price', 'in_stock', 'min_rating'] as $param): ?>
                        <?php if(isset($_GET[$param]) && !empty($_GET[$param])): ?>
                        <input type="hidden" name="<?php echo $param; ?>" value="<?php echo htmlspecialchars($_GET[$param]); ?>">
                        <?php endif; ?>
                    <?php endforeach; ?>

                    <div class="input-group">
                        <input type="text" name="search" class="form-control search-box"
                               placeholder="🔍 Cari produk, kategori, atau deskripsi..."
                               value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                        <button type="submit" class="btn btn-search">
                            <i class="fas fa-search"></i> Cari
                        </button>
                        <?php if(isset($_GET['search']) && !empty($_GET['search'])): ?>
                        <a href="<?php
                            $clear_params = $_GET;
                            unset($clear_params['search']);
                            echo 'Products.php' . (!empty($clear_params) ? '?' . http_build_query($clear_params) : '');
                        ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilters" aria-expanded="false">
                    <i class="fas fa-sliders-h me-1"></i> Filter Lanjutan
                </button>
            </div>
        </div>

        <!-- Advanced Filters (Collapsible) -->
        <div class="collapse" id="advancedFilters">
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <form action="Products.php" method="GET" id="advancedFilterForm">
                        <!-- Preserve search term -->
                        <?php if(isset($_GET['search']) && !empty($_GET['search'])): ?>
                        <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search']); ?>">
                        <?php endif; ?>

                        <div class="row">
                            <!-- Price Range -->
                            <div class="col-md-3 mb-3">
                                <label class="form-label fw-bold">💰 Rentang Harga</label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" name="min_price" class="form-control"
                                               placeholder="Min" min="0" step="1000"
                                               value="<?php echo isset($_GET['min_price']) ? htmlspecialchars($_GET['min_price']) : ''; ?>">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" name="max_price" class="form-control"
                                               placeholder="Max" min="0" step="1000"
                                               value="<?php echo isset($_GET['max_price']) ? htmlspecialchars($_GET['max_price']) : ''; ?>">
                                    </div>
                                </div>
                            </div>

                            <!-- Category Filter -->
                            <div class="col-md-3 mb-3">
                                <label class="form-label fw-bold">📂 Kategori</label>
                                <select name="category" class="form-select">
                                    <option value="">Semua Kategori</option>
                                    <?php foreach($categories as $category): ?>
                                    <option value="<?php echo $category['slug']; ?>"
                                            <?php echo (isset($_GET['category']) && $_GET['category'] == $category['slug']) ? 'selected' : ''; ?>>
                                        <?php echo $category['NAME']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Rating Filter -->
                            <div class="col-md-2 mb-3">
                                <label class="form-label fw-bold">⭐ Rating Min</label>
                                <select name="min_rating" class="form-select">
                                    <option value="">Semua Rating</option>
                                    <option value="4" <?php echo (isset($_GET['min_rating']) && $_GET['min_rating'] == '4') ? 'selected' : ''; ?>>4+ ⭐</option>
                                    <option value="3" <?php echo (isset($_GET['min_rating']) && $_GET['min_rating'] == '3') ? 'selected' : ''; ?>>3+ ⭐</option>
                                    <option value="2" <?php echo (isset($_GET['min_rating']) && $_GET['min_rating'] == '2') ? 'selected' : ''; ?>>2+ ⭐</option>
                                    <option value="1" <?php echo (isset($_GET['min_rating']) && $_GET['min_rating'] == '1') ? 'selected' : ''; ?>>1+ ⭐</option>
                                </select>
                            </div>

                            <!-- Stock Filter -->
                            <div class="col-md-2 mb-3">
                                <label class="form-label fw-bold">📦 Ketersediaan</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="in_stock" value="1" id="inStockCheck"
                                           <?php echo (isset($_GET['in_stock']) && $_GET['in_stock'] == '1') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="inStockCheck">
                                        Hanya yang tersedia
                                    </label>
                                </div>
                            </div>

                            <!-- Filter Actions -->
                            <div class="col-md-2 mb-3">
                                <label class="form-label fw-bold">&nbsp;</label>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-filter"></i> Terapkan
                                    </button>
                                    <a href="Products.php" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-undo"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sort and Quick Filters -->
        <div class="row align-items-center mt-3">
            <div class="col-md-6">
                <div class="dropdown">
                    <button class="btn btn-outline-success dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-sort me-1"></i>
                        <?php
                        $sort_labels = [
                            'name_asc' => 'A-Z',
                            'name_desc' => 'Z-A',
                            'price_low' => 'Harga Terendah',
                            'price_high' => 'Harga Tertinggi',
                            'newest' => 'Terbaru',
                            'oldest' => 'Terlama',
                            'popularity' => 'Terpopuler',
                            'rating' => 'Rating Tertinggi',
                            'stock' => 'Stok Terbanyak'
                        ];
                        echo isset($sort_labels[$sort_by]) ? $sort_labels[$sort_by] : 'A-Z';
                        ?>
                    </button>
                <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                    <li><h6 class="dropdown-header"><i class="fas fa-sort-alpha-down me-1"></i>Urut Nama</h6></li>
                    <li><a class="dropdown-item<?php echo $sort_by == 'name_asc' ? ' active' : ''; ?>" href="<?php
                        $sort_params = $_GET;
                        $sort_params['sort'] = 'name_asc';
                        echo 'Products.php?' . http_build_query($sort_params);
                    ?>">A-Z</a></li>
                    <li><a class="dropdown-item<?php echo $sort_by == 'name_desc' ? ' active' : ''; ?>" href="<?php
                        $sort_params = $_GET;
                        $sort_params['sort'] = 'name_desc';
                        echo 'Products.php?' . http_build_query($sort_params);
                    ?>">Z-A</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header"><i class="fas fa-dollar-sign me-1"></i>Urut Harga</h6></li>
                    <li><a class="dropdown-item<?php echo $sort_by == 'price_low' ? ' active' : ''; ?>" href="<?php
                        $sort_params = $_GET;
                        $sort_params['sort'] = 'price_low';
                        echo 'Products.php?' . http_build_query($sort_params);
                    ?>">Harga Terendah</a></li>
                    <li><a class="dropdown-item<?php echo $sort_by == 'price_high' ? ' active' : ''; ?>" href="<?php
                        $sort_params = $_GET;
                        $sort_params['sort'] = 'price_high';
                        echo 'Products.php?' . http_build_query($sort_params);
                    ?>">Harga Tertinggi</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header"><i class="fas fa-clock me-1"></i>Urut Waktu</h6></li>
                    <li><a class="dropdown-item<?php echo $sort_by == 'newest' ? ' active' : ''; ?>" href="<?php
                        $sort_params = $_GET;
                        $sort_params['sort'] = 'newest';
                        echo 'Products.php?' . http_build_query($sort_params);
                    ?>">Terbaru</a></li>
                    <li><a class="dropdown-item<?php echo $sort_by == 'oldest' ? ' active' : ''; ?>" href="<?php
                        $sort_params = $_GET;
                        $sort_params['sort'] = 'oldest';
                        echo 'Products.php?' . http_build_query($sort_params);
                    ?>">Terlama</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header"><i class="fas fa-star me-1"></i>Urut Popularitas</h6></li>
                    <li><a class="dropdown-item<?php echo $sort_by == 'popularity' ? ' active' : ''; ?>" href="<?php
                        $sort_params = $_GET;
                        $sort_params['sort'] = 'popularity';
                        echo 'Products.php?' . http_build_query($sort_params);
                    ?>">Terpopuler</a></li>
                    <li><a class="dropdown-item<?php echo $sort_by == 'rating' ? ' active' : ''; ?>" href="<?php
                        $sort_params = $_GET;
                        $sort_params['sort'] = 'rating';
                        echo 'Products.php?' . http_build_query($sort_params);
                    ?>">Rating Tertinggi</a></li>
                    <li><a class="dropdown-item<?php echo $sort_by == 'stock' ? ' active' : ''; ?>" href="<?php
                        $sort_params = $_GET;
                        $sort_params['sort'] = 'stock';
                        echo 'Products.php?' . http_build_query($sort_params);
                    ?>">Stok Terbanyak</a></li>
                </ul>
            </div>

            <!-- Quick Filter Buttons -->
            <div class="btn-group ms-2" role="group">
                <a href="<?php
                    $quick_params = $_GET;
                    $quick_params['in_stock'] = '1';
                    echo 'Products.php?' . http_build_query($quick_params);
                ?>" class="btn btn-outline-success btn-sm <?php echo (isset($_GET['in_stock']) && $_GET['in_stock'] == '1') ? 'active' : ''; ?>">
                    <i class="fas fa-check-circle me-1"></i>Tersedia
                </a>
                <a href="<?php
                    $quick_params = $_GET;
                    $quick_params['min_rating'] = '4';
                    echo 'Products.php?' . http_build_query($quick_params);
                ?>" class="btn btn-outline-warning btn-sm <?php echo (isset($_GET['min_rating']) && $_GET['min_rating'] == '4') ? 'active' : ''; ?>">
                    <i class="fas fa-star me-1"></i>4+ Rating
                </a>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <!-- Active Filters Display -->
            <?php
            $active_filters = [];
            if (isset($_GET['search']) && !empty($_GET['search'])) {
                $active_filters[] = 'Pencarian: "' . htmlspecialchars($_GET['search']) . '"';
            }
            if (isset($_GET['category']) && !empty($_GET['category'])) {
                foreach($categories as $cat) {
                    if($cat['slug'] == $_GET['category']) {
                        $active_filters[] = 'Kategori: ' . $cat['NAME'];
                        break;
                    }
                }
            }
            if (isset($_GET['min_price']) && !empty($_GET['min_price'])) {
                $active_filters[] = 'Min: Rp ' . number_format($_GET['min_price']);
            }
            if (isset($_GET['max_price']) && !empty($_GET['max_price'])) {
                $active_filters[] = 'Max: Rp ' . number_format($_GET['max_price']);
            }
            if (isset($_GET['min_rating']) && !empty($_GET['min_rating'])) {
                $active_filters[] = 'Rating: ' . $_GET['min_rating'] . '+⭐';
            }
            if (isset($_GET['in_stock']) && $_GET['in_stock'] == '1') {
                $active_filters[] = 'Tersedia';
            }

            if (!empty($active_filters)): ?>
            <div class="mb-2">
                <small class="text-muted">Filter aktif:</small>
                <?php foreach($active_filters as $filter): ?>
                <span class="badge bg-primary me-1"><?php echo $filter; ?></span>
                <?php endforeach; ?>
                <a href="Products.php" class="btn btn-outline-secondary btn-sm ms-2">
                    <i class="fas fa-times"></i> Hapus Semua
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Results Info -->
    <div class="results-info">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-2" style="color: #2c3e50; font-weight: 700;">
                    <?php if(isset($_GET['search']) && !empty($_GET['search'])): ?>
                        🔍 Hasil pencarian: "<span style="color: #667eea;"><?php echo htmlspecialchars($_GET['search']); ?></span>"
                    <?php elseif(isset($_GET['category']) && !empty($_GET['category'])): ?>
                        <?php
                        $current_category = '';
                        foreach($categories as $cat) {
                            if($cat['slug'] == $_GET['category']) {
                                $current_category = $cat['NAME'];
                                break;
                            }
                        }
                        ?>
                        📂 Kategori: <span style="color: #667eea;"><?php echo $current_category; ?></span>
                    <?php else: ?>
                        🛍️ Semua Produk
                    <?php endif; ?>
                </h4>
                <p class="mb-0" style="color: #6c757d; font-weight: 500;">
                    📊 Menampilkan <strong><?php echo count($products); ?></strong> dari <strong><?php echo $total_products; ?></strong> produk
                    <?php if($sort_by != 'name_asc'): ?>
                        • 🔄 Diurutkan: <strong><?php echo $sort_labels[$sort_by]; ?></strong>
                    <?php endif; ?>
                </p>
            </div>
            <?php if($total_pages > 1): ?>
            <div class="text-end">
                <span class="badge" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 8px 15px; border-radius: 20px; font-size: 0.9rem;">
                    📄 Halaman <?php echo $page_number; ?> dari <?php echo $total_pages; ?>
                </span>
            </div>
            <?php endif; ?>
        </div>
    </div>

            <div class="row">
                <?php if(count($products) > 0): ?>
                    <?php foreach($products as $product): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card product-card h-100">
                            <div class="position-relative">
                                <img src="<?= !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'assets/img/product-default.jpg' ?>"
                                     class="card-img-top product-img"
                                     alt="<?= htmlspecialchars($product['NAME'] ?? $product['name']) ?>"
                                     onerror="this.src='assets/img/product-default.jpg'">

                                <!-- Stock Badge -->
                                <?php if($product['stock'] <= 3 && $product['stock'] > 0): ?>
                                <span class="stock-badge limited-stock">⚠️ Stok Terbatas</span>
                                <?php elseif($product['stock'] <= 0): ?>
                                <span class="stock-badge out-of-stock">❌ Habis</span>
                                <?php else: ?>
                                <span class="stock-badge" style="background: linear-gradient(45deg, #00b894, #00cec9); color: white;">✅ Tersedia</span>
                                <?php endif; ?>

                                <!-- Wishlist Button -->
                                <div class="position-absolute top-0 start-0 m-3">
                                    <button class="btn btn-sm wishlist-btn"
                                            data-product-id="<?php echo $product['product_id']; ?>"
                                            onclick="toggleWishlist(<?php echo $product['product_id']; ?>)"
                                            style="background: rgba(255,255,255,0.9); border-radius: 50%; width: 40px; height: 40px; border: none; transition: all 0.3s ease;"
                                            title="Add to wishlist">
                                        <i class="far fa-heart" style="color: #ff6b6b;"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="card-body d-flex flex-column">
                                <!-- Category Badge -->
                                <span class="category-badge"><?php echo $product['category_name']; ?></span>

                                <!-- Product Title -->
                                <h5 class="card-title"><?php echo htmlspecialchars($product['NAME'] ?? $product['name']); ?></h5>

                                <!-- Description -->
                                <p class="card-text flex-grow-1" style="color: #6c757d; line-height: 1.5;">
                                    <?php echo substr($product['description'], 0, 80); ?>...
                                </p>

                                <!-- Price -->
                                <div class="mb-3">
                                    <span class="price-tag">
                                        💰 Rp<?= number_format($product['price'], 0, ',', '.') ?>
                                    </span>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-flex gap-2 mt-auto">
                                    <button class="btn btn-detail flex-fill detail-link"
                                            data-product-id="<?php echo $product['product_id']; ?>">
                                        <i class="fas fa-eye me-1"></i> Lihat
                                    </button>
                                    <?php if($product['stock'] > 0): ?>
                                    <button class="btn btn-cart flex-fill add-to-cart-btn"
                                            data-product-id="<?php echo $product['product_id']; ?>"
                                            data-product-name="<?php echo htmlspecialchars($product['NAME'] ?? $product['name']); ?>"
                                            data-product-price="<?php echo $product['price']; ?>"
                                            data-product-image="<?php echo !empty($product['image']) ? $product['image'] : 'product-default.jpg'; ?>">
                                        <i class="fas fa-cart-plus me-1"></i> Beli
                                    </button>
                                    <?php else: ?>
                                    <button class="btn flex-fill" disabled style="background: #ddd; color: #999; border-radius: 25px;">
                                        <i class="fas fa-times-circle me-1"></i> Habis
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <!-- Pagination -->
                    <?php if($total_pages > 1): ?>
                    <div class="col-12">
                        <nav aria-label="Product pagination">
                            <ul class="pagination justify-content-center mt-4">
                                <?php if($page_number > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo $pagination_url; ?>page=<?php echo $page_number - 1; ?>" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <?php endif; ?>

                                <?php
                                // Determine start and end page numbers for pagination display
                                $start_page = max(1, $page_number - 2);
                                $end_page = min($total_pages, $start_page + 4);

                                if ($end_page - $start_page < 4) {
                                    $start_page = max(1, $end_page - 4);
                                }

                                for($i = $start_page; $i <= $end_page; $i++):
                                ?>
                                <li class="page-item <?php echo $i == $page_number ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo $pagination_url; ?>page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                </li>
                                <?php endfor; ?>

                                <?php if($page_number < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo $pagination_url; ?>page=<?php echo $page_number + 1; ?>" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                    <?php endif; ?>

                <?php else: ?>
                    <div class="col-12">
                        <div class="alert alert-info">
                            No products found. Please try another search.
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Product Detail Modal -->
<div class="modal fade" id="productDetailModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalProductTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <img src="" alt="Product Image" id="modalProductImage" class="img-fluid rounded">
                    </div>
                    <div class="col-md-6">
                        <h3 id="modalProductPrice" class="text-primary mb-3"></h3>
                        <p><strong>Category:</strong> <span id="modalProductCategory"></span></p>
                        <p id="modalProductDescription"></p>
                        <div class="d-flex align-items-center mb-3">
                            <span class="me-3">Quantity:</span>
                            <div class="input-group" style="width: 130px;">
                                <button class="btn btn-outline-secondary" type="button" id="decreaseQuantity">-</button>
                                <input type="number" class="form-control text-center" id="modalQuantity" value="1" min="1">
                                <button class="btn btn-outline-secondary" type="button" id="increaseQuantity">+</button>
                            </div>
                        </div>
                        <p id="modalProductStock"></p>
                        <button type="button" class="btn btn-primary" id="modalAddToCartBtn">
                            <i class="fas fa-cart-plus me-2"></i> Add to Cart
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<!-- Advanced Search JavaScript -->
<script src="js/advanced-search.js"></script>

<!-- Wishlist JavaScript -->
<script src="js/wishlist.js"></script>

<script>
// Toggle wishlist function for product cards
async function toggleWishlist(productId) {
    const btn = document.querySelector(`[data-product-id="${productId}"].wishlist-btn`);
    if (!btn) return;

    const isInWishlist = btn.classList.contains('active');

    // Show loading state
    const originalContent = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin" style="color: #ff6b6b;"></i>';
    btn.disabled = true;

    try {
        let result;
        if (isInWishlist) {
            result = await window.wishlistManager.removeFromWishlist(productId);
        } else {
            result = await window.wishlistManager.addToWishlist(productId);
        }

        if (result.success) {
            // Update button state
            if (isInWishlist) {
                btn.classList.remove('active');
                btn.innerHTML = '<i class="far fa-heart" style="color: #ff6b6b;"></i>';
                btn.title = 'Add to wishlist';
            } else {
                btn.classList.add('active');
                btn.innerHTML = '<i class="fas fa-heart" style="color: #ff6b6b;"></i>';
                btn.title = 'Remove from wishlist';
            }
        } else {
            // Restore original state on error
            btn.innerHTML = originalContent;
        }
    } catch (error) {
        console.error('Error toggling wishlist:', error);
        btn.innerHTML = originalContent;
    } finally {
        btn.disabled = false;
    }
}

// Check wishlist status for all products on page load
document.addEventListener('DOMContentLoaded', async function() {
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');

    for (const btn of wishlistBtns) {
        const productId = btn.dataset.productId;

        try {
            const response = await fetch(`ajax/wishlist_actions.php?action=check&product_id=${productId}`);
            const data = await response.json();

            if (data.success && data.in_wishlist) {
                btn.classList.add('active');
                btn.innerHTML = '<i class="fas fa-heart" style="color: #ff6b6b;"></i>';
                btn.title = 'Remove from wishlist';
            }
        } catch (error) {
            console.error('Error checking wishlist status:', error);
        }
    }
});
</script>

<?php include('includes/footer.php'); ?>

<!-- Product-specific JavaScript -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function showProductDetails(productId) {
    // Show loading
    $('#modalProductTitle').text('Loading...');
    $('#modalProductPrice').text('');
    $('#modalProductCategory').text('');
    $('#modalProductDescription').text('');

    // Reset quantity input
    $('#modalQuantity').val(1);

    // Get product details via AJAX
    $.ajax({
        url: 'ajax/get_product.php',
        type: 'GET',
        data: {id: productId},
        dataType: 'json',
        success: function(response) {
            // Populate modal with product details
            $('#modalProductTitle').text(response.name);
            $('#modalProductImage').attr('src', response.image ? 'uploads/' + response.image : 'assets/img/product-default.jpg');

            // Add error handling for image loading
            $('#modalProductImage').on('error', function() {
                $(this).attr('src', 'assets/img/product-default.jpg');
            });
            $('#modalProductPrice').text('Rp ' + response.price_formatted);
            $('#modalProductCategory').text(response.category);

            // Store product data for validation
            $('#modalAddToCartBtn').data({
                'product-id': response.id,
                'product-name': response.name,
                'product-price': response.price,
                'product-image': response.image,
                'stock': response.stock
            });

            // Update quantity input limits
            $('#modalQuantity').attr('max', response.stock);

            // Set stock status with appropriate color
            if (response.stock > 0) {
                $('#modalProductStock').html('<span class="text-success"><i class="fas fa-check-circle"></i> Tersedia (' + response.stock + ' unit)</span>');
                $('#modalAddToCartBtn').prop('disabled', false);
                $('#modalQuantity').prop('disabled', false);
            } else {
                $('#modalProductStock').html('<span class="text-danger"><i class="fas fa-times-circle"></i> Stok Habis</span>');
                $('#modalAddToCartBtn').prop('disabled', true);
                $('#modalQuantity').prop('disabled', true);
            }

            $('#modalProductDescription').text(response.description);

            // Show modal
            $('#productDetailModal').modal('show');
        },
        error: function(xhr, status, error) {
            showAlert('danger', 'Gagal memuat detail produk. Silakan coba lagi.');
            console.error(xhr.responseText);
        }
    });
}

// Function to add product to cart
function addToCart(productId, quantity, button) {
    // Store original content of button if provided
    var originalContent = button ? button.html() : '';

    // Update button state if provided
    if (button && button.length) {
        button.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');
        button.prop('disabled', true);
    }

    // Add to cart via AJAX
    $.ajax({
        url: 'ajax/working_add_to_cart.php',
        method: 'POST',
        data: {product_id: productId, quantity: quantity},
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Update cart count in UI
                $.ajax({
                    url: 'ajax/get_cart_count.php',
                    method: 'GET',
                    dataType: 'json',
                    success: function(countResponse) {
                        const cartCountBadge = $('.cart-count');
                        if (countResponse.count > 0) {
                            cartCountBadge.text(countResponse.count);
                            cartCountBadge.show();
                        } else {
                            cartCountBadge.hide();
                        }
                    }
                });

                // Show success message with quantity using enhanced notification
                const productName = $(this).closest('.product-card').find('.product-name').text() || 'Produk';
                addToCartWithNotification(productId, productName, quantity);

                // Close modal if opened
                if ($('#productDetailModal').hasClass('show')) {
                    $('#productDetailModal').modal('hide');
                }
            } else {
                // Don't show error message - just log it
                console.log('Add to cart failed:', response.message);
            }

            // Restore button if it exists
            if (button && button.length) {
                setTimeout(function() {
                    button.html(originalContent);
                    button.prop('disabled', false);
                }, 600);
            }
        },
        error: function(xhr, status, error) {
            console.log("Add to cart request failed (silently handled):", xhr.responseText);
            // Don't show error alert - just restore button

            // Restore button if it exists
            if (button && button.length) {
                setTimeout(function() {
                    button.html(originalContent);
                    button.prop('disabled', false);
                }, 600);
            }
        }
    });
}

// Show alert function - Updated to use Toastify
function showAlert(type, message) {
    console.log('showAlert called:', type, message);

    // Use Toastify if available
    if (typeof Toastify !== 'undefined') {
        let background = '';
        switch(type) {
            case 'success':
                background = 'linear-gradient(to right, #00b09b, #96c93d)';
                break;
            case 'danger':
                background = 'linear-gradient(to right, #ff416c, #ff4b2b)';
                break;
            case 'warning':
                background = 'linear-gradient(to right, #f46b45, #eea849)';
                break;
            default:
                background = 'linear-gradient(to right, #00b09b, #96c93d)';
        }

        Toastify({
            text: message,
            duration: 3000,
            close: true,
            gravity: "top",
            position: "right",
            backgroundColor: background,
            stopOnFocus: true
        }).showToast();
    } else {
        // Fallback to original Bootstrap alert
        $('.alert-notification').remove();

        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show alert-notification position-fixed shadow-lg"
                 style="top: 20px; right: 20px; z-index: 9999; max-width: 400px; min-width: 300px;">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        const alertElement = $(alertHtml);
        $('body').append(alertElement);

        setTimeout(function() {
            alertElement.fadeOut(300, function() {
                $(this).remove();
            });
        }, 4000);
    }
}

// Initialize on document ready
$(document).ready(function() {
    // Product detail popup for detail-link buttons
    $(document).on('click', '.detail-link', function(e) {
        e.preventDefault();
        const productId = $(this).data('product-id');
        showProductDetails(productId);
    });

    // Add to cart functionality for add-to-cart-btn buttons
    $(document).on('click', '.add-to-cart-btn', function() {
        var productId = $(this).data('product-id');
        var productName = $(this).data('product-name');
        var quantity = 1;

        // Store original button content
        var originalContent = $(this).html();

        // Show loading state
        $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');
        $(this).prop('disabled', true);

        // Add to cart via AJAX
        $.ajax({
            url: 'ajax/working_add_to_cart.php',
            method: 'POST',
            data: {
                product_id: productId,
                quantity: quantity
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update cart count in UI
                    $.ajax({
                        url: 'ajax/get_cart_count.php',
                        method: 'GET',
                        dataType: 'json',
                        success: function(countResponse) {
                            const cartCountBadge = $('.cart-count');
                            if (countResponse.count > 0) {
                                cartCountBadge.text(countResponse.count);
                                cartCountBadge.show();
                            } else {
                                cartCountBadge.hide();
                            }
                        }
                    });

                    // Show success message with quantity using enhanced notification
                    const serverProductName = response.data.product_name || productName || 'Produk';
                    const serverQuantity = response.data.quantity || quantity;
                    addToCartWithNotification(productId, serverProductName, serverQuantity);
                } else {
                    // Don't show error message - just log it
                    console.log('Add to cart failed:', response.message);
                }

                // Restore button
                setTimeout(function() {
                    $('.add-to-cart-btn[data-product-id="' + productId + '"]').html(originalContent);
                    $('.add-to-cart-btn[data-product-id="' + productId + '"]').prop('disabled', false);
                }, 600);
            },
            error: function(xhr, status, error) {
                console.log("Add to cart request failed (silently handled):", xhr.responseText);
                // Don't show error alert - just restore button

                // Restore button
                setTimeout(function() {
                    $('.add-to-cart-btn[data-product-id="' + productId + '"]').html(originalContent);
                    $('.add-to-cart-btn[data-product-id="' + productId + '"]').prop('disabled', false);
                }, 600);
            }
        });
    });

    // Handle quantity buttons in the modal
    $('#decreaseQuantity').click(function() {
        var value = parseInt($('#modalQuantity').val());
        if (value > 1) {
            $('#modalQuantity').val(value - 1);
        }
    });

    $('#increaseQuantity').click(function() {
        var value = parseInt($('#modalQuantity').val());
        var max = parseInt($('#modalQuantity').attr('max'));
        if (value < max) {
            $('#modalQuantity').val(value + 1);
        }
    });

    // Handle modal add to cart button
    $('#modalAddToCartBtn').on('click', function() {
        const productId = $(this).data('product-id');
        const productName = $(this).data('product-name');
        const quantity = parseInt($('#modalQuantity').val());

        $(this).prop('disabled', true);
        $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Adding...');

        // Add to cart via AJAX
        $.ajax({
            url: 'ajax/working_add_to_cart.php',
            method: 'POST',
            data: {
                product_id: productId,
                quantity: quantity
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update cart count
                    $.ajax({
                        url: 'ajax/get_cart_count.php',
                        method: 'GET',
                        dataType: 'json',
                        success: function(countResponse) {
                            const cartCountBadge = $('.cart-count');
                            if (countResponse.count > 0) {
                                cartCountBadge.text(countResponse.count);
                                cartCountBadge.show();
                            } else {
                                cartCountBadge.hide();
                            }
                        }
                    });

                    // Close modal and show success message with enhanced notification
                    $('#productDetailModal').modal('hide');
                    const serverProductName = response.data.product_name || productName || 'Produk';
                    const serverQuantity = response.data.quantity || quantity;
                    addToCartWithNotification(productId, serverProductName, serverQuantity);
                } else {
                    // Don't show error message - just log it
                    console.log('Add to cart failed:', response.message);
                }

                // Reset button
                $('#modalAddToCartBtn').prop('disabled', false);
                $('#modalAddToCartBtn').html('<i class="fas fa-cart-plus me-2"></i> Add to Cart');
            },
            error: function(xhr, status, error) {
                console.log("Add to cart request failed (silently handled):", xhr.responseText);
                // Don't show error alert - just restore button

                // Reset button
                $('#modalAddToCartBtn').prop('disabled', false);
                $('#modalAddToCartBtn').html('<i class="fas fa-cart-plus me-2"></i> Add to Cart');
            }
        });
    });
});
</script>

<!-- Include Toastify JS -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

<!-- Wait for Toastify to load -->
<script>
function waitForToastify(callback) {
    if (typeof Toastify !== 'undefined') {
        callback();
    } else {
        setTimeout(() => waitForToastify(callback), 100);
    }
}

// Initialize when Toastify is ready
waitForToastify(function() {
    console.log('Toastify is ready!');
});
</script>

<script>
// Simple notification function using Toastify
function showToastNotification(message, type = 'success') {
    let background = '';
    switch(type) {
        case 'success':
            background = 'linear-gradient(to right, #00b09b, #96c93d)';
            break;
        case 'danger':
        case 'error':
            background = 'linear-gradient(to right, #ff416c, #ff4b2b)';
            break;
        case 'warning':
            background = 'linear-gradient(to right, #f46b45, #eea849)';
            break;
        default:
            background = 'linear-gradient(to right, #00b09b, #96c93d)';
    }

    if (typeof Toastify !== 'undefined') {
        Toastify({
            text: message,
            duration: 3000,
            close: true,
            gravity: "top",
            position: "right",
            backgroundColor: background,
            stopOnFocus: true
        }).showToast();
    } else {
        console.log('Toastify not available, message:', message);
        alert(message); // Fallback
    }
}

// Enhanced addToCart function with better notification
function addToCartWithNotification(productId, productName, quantity) {
    console.log('addToCartWithNotification called:', productName, quantity);
    const message = `${quantity} ${productName} telah ditambahkan ke keranjang`;
    showAlert('success', message);
}
</script>

<script>
// Debug: Check if required libraries are loaded
console.log('Toastify available:', typeof Toastify !== 'undefined');
console.log('NotificationManager available:', typeof window.notificationManager !== 'undefined');
console.log('jQuery available:', typeof $ !== 'undefined');




</script>
