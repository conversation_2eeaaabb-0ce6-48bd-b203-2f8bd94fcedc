document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Product detail page loaded');

    // Get elements
    const quantityInput = document.getElementById('quantity');
    const decreaseBtn = document.getElementById('decrease-quantity');
    const increaseBtn = document.getElementById('increase-quantity');
    const alertContainer = document.getElementById('alert-container');
    const spinnerElement = document.getElementById('spinner');

    console.log('Elements found:', {
        quantityInput: !!quantityInput,
        decreaseBtn: !!decreaseBtn,
        increaseBtn: !!increaseBtn,
        alertContainer: !!alertContainer,
        spinnerElement: !!spinnerElement
    });

    // Helper functions
    function showSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'block';
        }
    }

    function hideSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'none';
        }
    }

    function showAlert(message, type = 'success') {
        if (!alertContainer) return;

        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        alertContainer.innerHTML = alertHtml;

        // Automatically dismiss alert after 5 seconds
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    if (quantityInput && decreaseBtn && increaseBtn) {
        console.log('Setting up quantity controls');

        // Update quantity
        decreaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            console.log('Decrease clicked, current value:', value);
            if (value > 1) {
                quantityInput.value = value - 1;
                console.log('New value:', quantityInput.value);
                // Add visual feedback
                quantityInput.classList.add('quantity-changed');
                setTimeout(() => quantityInput.classList.remove('quantity-changed'), 300);
            }
        });

        increaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            console.log('Increase clicked, current value:', value, 'max:', max);
            if (value < max) {
                quantityInput.value = value + 1;
                console.log('New value:', quantityInput.value);
                // Add visual feedback
                quantityInput.classList.add('quantity-changed');
                setTimeout(() => quantityInput.classList.remove('quantity-changed'), 300);
            } else {
                // Show max stock message
                showAlert(`Maximum stock available: ${max}`, 'warning');
            }
        });

        // Validate quantity input
        quantityInput.addEventListener('change', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            let min = parseInt(quantityInput.getAttribute('min')) || 1;

            if (isNaN(value) || value < min) {
                quantityInput.value = min;
                showAlert(`Minimum quantity is ${min}`, 'warning');
            } else if (value > max) {
                quantityInput.value = max;
                showAlert(`Maximum stock available: ${max}`, 'warning');
            }
        });

        // Add input validation on keyup for real-time feedback
        quantityInput.addEventListener('input', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            let min = parseInt(quantityInput.getAttribute('min')) || 1;

            // Remove non-numeric characters
            quantityInput.value = quantityInput.value.replace(/[^0-9]/g, '');

            // Update button states
            decreaseBtn.disabled = value <= min;
            increaseBtn.disabled = value >= max;
        });
    }

    // Handle add to cart button for product detail page
    const addToCartBtn = document.querySelector('.add-to-cart-btn');
    console.log('Add to cart button found:', !!addToCartBtn);

    if (addToCartBtn) {
        console.log('Setting up add to cart event listener');
        addToCartBtn.addEventListener('click', function(e) {
            e.preventDefault();

            console.log('Product detail add to cart clicked');

            const productId = this.dataset.productId;
            let quantity = 1;

            if (quantityInput) {
                quantity = parseInt(quantityInput.value) || 1;
                const maxStock = parseInt(quantityInput.getAttribute('max'));

                // Validate quantity against stock
                if (quantity > maxStock) {
                    showAlert(`Only ${maxStock} items available in stock`, 'warning');
                    quantityInput.value = maxStock;
                    quantity = maxStock;
                }

                if (quantity < 1) {
                    showAlert('Quantity must be at least 1', 'warning');
                    quantityInput.value = 1;
                    quantity = 1;
                }
            } else {
                console.warn('Quantity input not found, using default quantity of 1');
            }

            console.log('Product ID:', productId, 'Quantity:', quantity);

            // Prevent double clicks
            if (this.disabled) {
                console.log('Button already disabled, ignoring click');
                return;
            }

            // Set loading state
            const originalText = this.innerHTML;
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';

            showSpinner();

            // Create form data
            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('quantity', quantity);

            // Send request to add to cart
            fetch('ajax/simple_add_to_cart.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Add to cart response:', data);
                hideSpinner();

                if (data.success) {
                    // Success
                    this.innerHTML = '<i class="fas fa-check"></i> Added!';
                    this.classList.remove('btn-primary');
                    this.classList.add('btn-success');

                    // Update cart count
                    const cartCount = parseInt(data.cart_count) || 0;
                    if (typeof updateCartCount === 'function') {
                        updateCartCount(cartCount);
                    } else {
                        // Fallback cart count update
                        const cartElements = document.querySelectorAll('.cart-count, .cart-badge, #cart-count');
                        cartElements.forEach(element => {
                            element.textContent = cartCount;
                            if (cartCount > 0) {
                                element.style.display = 'inline';
                            }
                        });
                    }

                    // Show success notification with product details and view cart option
                    const successMsg = `${data.product_name || 'Product'} (${data.quantity || quantity}) added to cart!`;
                    showAlert(successMsg, 'success');

                    // Add a temporary "View Cart" button
                    const viewCartBtn = document.createElement('a');
                    viewCartBtn.href = 'cart.php';
                    viewCartBtn.className = 'btn btn-outline-primary btn-sm ms-2';
                    viewCartBtn.innerHTML = '<i class="fas fa-eye me-1"></i>View Cart';
                    this.parentNode.appendChild(viewCartBtn);

                    // Reset button after 3 seconds
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.classList.remove('btn-success');
                        this.classList.add('btn-primary');
                        this.disabled = false;
                        // Remove view cart button
                        if (viewCartBtn.parentNode) {
                            viewCartBtn.remove();
                        }
                    }, 3000);
                } else {
                    // Error
                    console.error('Add to cart failed:', data.message);
                    this.innerHTML = originalText;
                    this.disabled = false;
                    showAlert(data.message || 'Failed to add product to cart', 'danger');
                }
            })
            .catch(error => {
                console.error('Network error:', error);
                hideSpinner();
                this.innerHTML = originalText;
                this.disabled = false;
                showAlert('Network error. Please try again.', 'danger');
            });
        });
    }

    // Add CSS for quantity animations
    const style = document.createElement('style');
    style.textContent = `
        .quantity-changed {
            background-color: #e3f2fd !important;
            transition: background-color 0.3s ease;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .product-detail-loaded {
            border-left: 3px solid #28a745;
            padding-left: 10px;
        }
    `;
    document.head.appendChild(style);

    // Add a visual indicator that JavaScript is working
    const productDetail = document.querySelector('.product-detail');
    if (productDetail) {
        productDetail.classList.add('product-detail-loaded');
        const indicator = document.createElement('small');
        indicator.style.color = 'green';
        indicator.textContent = '✅ JavaScript loaded';
        productDetail.appendChild(indicator);
    }
});
