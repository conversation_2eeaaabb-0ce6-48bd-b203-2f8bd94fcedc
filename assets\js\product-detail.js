document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Product detail page loaded');

    // Get elements
    const quantityInput = document.getElementById('quantity');
    const decreaseBtn = document.getElementById('decrease-quantity');
    const increaseBtn = document.getElementById('increase-quantity');
    const alertContainer = document.getElementById('alert-container');
    const spinnerElement = document.getElementById('spinner');

    console.log('Elements found:', {
        quantityInput: !!quantityInput,
        decreaseBtn: !!decreaseBtn,
        increaseBtn: !!increaseBtn,
        alertContainer: !!alertContainer,
        spinnerElement: !!spinnerElement
    });

    // Helper functions
    function showSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'block';
        }
    }

    function hideSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'none';
        }
    }

    function showAlert(message, type = 'success') {
        if (!alertContainer) return;

        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        alertContainer.innerHTML = alertHtml;

        // Automatically dismiss alert after 5 seconds
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    if (quantityInput && decreaseBtn && increaseBtn) {
        console.log('Setting up quantity controls');

        // Update quantity
        decreaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            console.log('Decrease clicked, current value:', value);
            if (value > 1) {
                quantityInput.value = value - 1;
                console.log('New value:', quantityInput.value);
            }
        });

        increaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            console.log('Increase clicked, current value:', value, 'max:', max);
            if (value < max) {
                quantityInput.value = value + 1;
                console.log('New value:', quantityInput.value);
            }
        });

        // Validate quantity input
        quantityInput.addEventListener('change', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            if (isNaN(value) || value < 1) {
                quantityInput.value = 1;
            } else if (value > max) {
                quantityInput.value = max;
            }
        });
    }

    // Handle add to cart button for product detail page
    const addToCartBtn = document.querySelector('.add-to-cart-btn');
    console.log('Add to cart button found:', !!addToCartBtn);

    if (addToCartBtn) {
        console.log('Setting up add to cart event listener');
        addToCartBtn.addEventListener('click', function(e) {
            e.preventDefault();

            console.log('Product detail add to cart clicked');

            const productId = this.dataset.productId;
            let quantity = 1;

            if (quantityInput) {
                quantity = parseInt(quantityInput.value) || 1;
            } else {
                console.warn('Quantity input not found, using default quantity of 1');
            }

            console.log('Product ID:', productId, 'Quantity:', quantity);

            // Prevent double clicks
            if (this.disabled) {
                console.log('Button already disabled, ignoring click');
                return;
            }

            // Set loading state
            const originalText = this.innerHTML;
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';

            showSpinner();

            // Create form data
            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('quantity', quantity);

            // Send request to add to cart
            fetch('ajax/simple_add_to_cart.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                console.log('Add to cart response:', data);
                hideSpinner();

                if (data.success) {
                    // Success
                    this.innerHTML = '<i class="fas fa-check"></i> Added!';
                    this.classList.add('btn-success');

                    // Update cart count
                    const cartCount = parseInt(data.cart_count) || 0;
                    if (typeof updateCartCount === 'function') {
                        updateCartCount(cartCount);
                    }

                    // Show success notification
                    showAlert(data.message || 'Product added to cart successfully!', 'success');

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.classList.remove('btn-success');
                        this.disabled = false;
                    }, 2000);
                } else {
                    // Error
                    console.error('Add to cart failed:', data.message);
                    this.innerHTML = originalText;
                    this.disabled = false;
                    showAlert(data.message || 'Failed to add product to cart', 'danger');
                }
            })
            .catch(error => {
                console.error('Network error:', error);
                hideSpinner();
                this.innerHTML = originalText;
                this.disabled = false;
                showAlert('Network error. Please try again.', 'danger');
            });
        });
    }

    // Add a visual indicator that JavaScript is working
    const productDetail = document.querySelector('.product-detail');
    if (productDetail) {
        const indicator = document.createElement('small');
        indicator.style.color = 'green';
        indicator.textContent = '✅ JavaScript loaded';
        productDetail.appendChild(indicator);
    }
});
