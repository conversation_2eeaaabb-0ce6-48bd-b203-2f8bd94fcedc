<?php
// Start output buffering to prevent any accidental output
ob_start();

session_start();
header('Content-Type: application/json');

// Enable error reporting for debugging but don't display
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Clean any previous output
ob_clean();

try {
    // Include required files
    require_once dirname(__DIR__) . '/includes/db_connect.php';
    require_once dirname(__DIR__) . '/includes/functions.php';
    
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Please login first to add items to cart',
            'redirect' => 'login.php'
        ]);
        exit;
    }
    
    // Validate input
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    
    if ($product_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid product ID'
        ]);
        exit;
    }
    
    if ($quantity <= 0) {
        $quantity = 1;
    }
    
    $user_id = $_SESSION['user_id'];
    
    // Check if product exists
    $stmt = $conn->prepare("SELECT product_id, name, price, stock FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        echo json_encode([
            'success' => false,
            'message' => 'Product not found'
        ]);
        exit;
    }
    
    // Check stock
    if ($product['stock'] < $quantity) {
        echo json_encode([
            'success' => false,
            'message' => 'Insufficient stock. Available: ' . $product['stock']
        ]);
        exit;
    }
    
    // Get or create cart
    $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$cart) {
        // Create new cart
        $stmt = $conn->prepare("INSERT INTO carts (user_id) VALUES (?)");
        $stmt->execute([$user_id]);
        $cart_id = $conn->lastInsertId();
    } else {
        $cart_id = $cart['cart_id'];
    }
    
    // Check if item already in cart
    $stmt = $conn->prepare("SELECT cart_item_id, quantity FROM cart_items WHERE cart_id = ? AND product_id = ?");
    $stmt->execute([$cart_id, $product_id]);
    $existing_item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing_item) {
        // Update quantity
        $new_quantity = $existing_item['quantity'] + $quantity;
        
        // Check stock again
        if ($product['stock'] < $new_quantity) {
            echo json_encode([
                'success' => false,
                'message' => 'Cannot add more items. Stock limit: ' . $product['stock'] . ', current in cart: ' . $existing_item['quantity']
            ]);
            exit;
        }
        
        $stmt = $conn->prepare("UPDATE cart_items SET quantity = ? WHERE cart_item_id = ?");
        $stmt->execute([$new_quantity, $existing_item['cart_item_id']]);
        
        $message = "Cart updated! Quantity increased to $new_quantity";
        
        // Create cart notification
        if (function_exists('createCartNotification')) {
            createCartNotification($user_id, 'updated', $product['name'], $new_quantity);
        }
    } else {
        // Add new item
        $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity) VALUES (?, ?, ?)");
        $stmt->execute([$cart_id, $product_id, $quantity]);
        
        $message = $product['name'] . ' added to cart successfully!';
        
        // Create cart notification
        if (function_exists('createCartNotification')) {
            createCartNotification($user_id, 'added', $product['name'], $quantity);
        }
    }
    
    // Get updated cart count
    $stmt = $conn->prepare("
        SELECT SUM(ci.quantity) as total_items
        FROM cart_items ci
        JOIN carts c ON ci.cart_id = c.cart_id
        WHERE c.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $cart_count = $result['total_items'] ?? 0;
    
    // Success response
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => [
            'product_id' => $product_id,
            'product_name' => $product['name'],
            'quantity' => $quantity,
            'cart_count' => $cart_count,
            'price' => $product['price']
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in add_to_cart_simple.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred. Please try again.'
    ]);
} catch (Exception $e) {
    error_log("General error in add_to_cart_simple.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred. Please try again.'
    ]);
}
