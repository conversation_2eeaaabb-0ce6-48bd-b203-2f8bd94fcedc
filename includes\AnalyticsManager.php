<?php
/**
 * Analytics Manager
 * Handles business analytics, reports, and dashboard data
 */

class AnalyticsManager {
    private $conn;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * Get dashboard overview statistics
     */
    public function getDashboardOverview($date_from = null, $date_to = null) {
        if (!$date_from) $date_from = date('Y-m-01'); // First day of current month
        if (!$date_to) $date_to = date('Y-m-d'); // Today
        
        try {
            // Sales overview
            $sales_data = $this->getSalesOverview($date_from, $date_to);
            
            // Order statistics
            $order_stats = $this->getOrderStatistics($date_from, $date_to);
            
            // Customer statistics
            $customer_stats = $this->getCustomerStatistics($date_from, $date_to);
            
            // Product performance
            $product_stats = $this->getProductPerformance($date_from, $date_to);
            
            // Traffic analytics
            $traffic_stats = $this->getTrafficAnalytics($date_from, $date_to);
            
            return [
                'period' => ['from' => $date_from, 'to' => $date_to],
                'sales' => $sales_data,
                'orders' => $order_stats,
                'customers' => $customer_stats,
                'products' => $product_stats,
                'traffic' => $traffic_stats
            ];
            
        } catch (Exception $e) {
            error_log("Error getting dashboard overview: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get sales overview
     */
    private function getSalesOverview($date_from, $date_to) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_orders,
                    SUM(total_amount) as total_revenue,
                    AVG(total_amount) as average_order_value,
                    SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as completed_revenue,
                    SUM(CASE WHEN status = 'pending' THEN total_amount ELSE 0 END) as pending_revenue,
                    SUM(CASE WHEN status = 'cancelled' THEN total_amount ELSE 0 END) as cancelled_revenue
                FROM orders 
                WHERE created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $current_period = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Get previous period for comparison
            $prev_date_from = date('Y-m-d', strtotime($date_from . ' -1 month'));
            $prev_date_to = date('Y-m-d', strtotime($date_to . ' -1 month'));
            
            $stmt->execute([$prev_date_from, $prev_date_to . ' 23:59:59']);
            $previous_period = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Calculate growth percentages
            $revenue_growth = $this->calculateGrowthPercentage(
                $current_period['total_revenue'], 
                $previous_period['total_revenue']
            );
            
            $orders_growth = $this->calculateGrowthPercentage(
                $current_period['total_orders'], 
                $previous_period['total_orders']
            );
            
            return [
                'current' => $current_period,
                'previous' => $previous_period,
                'growth' => [
                    'revenue' => $revenue_growth,
                    'orders' => $orders_growth
                ]
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting sales overview: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get order statistics
     */
    private function getOrderStatistics($date_from, $date_to) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    status,
                    COUNT(*) as count,
                    SUM(total_amount) as total_value,
                    AVG(total_amount) as avg_value
                FROM orders 
                WHERE created_at BETWEEN ? AND ?
                GROUP BY status
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $status_breakdown = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get daily order trends
            $stmt = $this->conn->prepare("
                SELECT 
                    DATE(created_at) as order_date,
                    COUNT(*) as order_count,
                    SUM(total_amount) as daily_revenue
                FROM orders 
                WHERE created_at BETWEEN ? AND ?
                GROUP BY DATE(created_at)
                ORDER BY order_date ASC
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $daily_trends = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'status_breakdown' => $status_breakdown,
                'daily_trends' => $daily_trends
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting order statistics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get customer statistics
     */
    private function getCustomerStatistics($date_from, $date_to) {
        try {
            // New customers
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) as new_customers
                FROM users 
                WHERE created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $new_customers = $stmt->fetchColumn();
            
            // Returning customers
            $stmt = $this->conn->prepare("
                SELECT COUNT(DISTINCT user_id) as returning_customers
                FROM orders 
                WHERE created_at BETWEEN ? AND ?
                AND user_id IN (
                    SELECT user_id FROM orders 
                    WHERE created_at < ?
                    GROUP BY user_id
                )
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59', $date_from]);
            $returning_customers = $stmt->fetchColumn();
            
            // Customer lifetime value
            $stmt = $this->conn->prepare("
                SELECT 
                    AVG(customer_total) as avg_lifetime_value,
                    MAX(customer_total) as max_lifetime_value
                FROM (
                    SELECT user_id, SUM(total_amount) as customer_total
                    FROM orders 
                    WHERE status = 'completed'
                    GROUP BY user_id
                ) as customer_totals
            ");
            $stmt->execute();
            $lifetime_value = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Top customers
            $stmt = $this->conn->prepare("
                SELECT 
                    u.name, u.email,
                    COUNT(o.order_id) as total_orders,
                    SUM(o.total_amount) as total_spent
                FROM users u
                JOIN orders o ON u.user_id = o.user_id
                WHERE o.created_at BETWEEN ? AND ?
                GROUP BY u.user_id
                ORDER BY total_spent DESC
                LIMIT 10
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $top_customers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'new_customers' => $new_customers,
                'returning_customers' => $returning_customers,
                'lifetime_value' => $lifetime_value,
                'top_customers' => $top_customers
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting customer statistics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get product performance
     */
    private function getProductPerformance($date_from, $date_to) {
        try {
            // Best selling products
            $stmt = $this->conn->prepare("
                SELECT 
                    p.name, p.price, p.image,
                    SUM(oi.quantity) as total_sold,
                    SUM(oi.quantity * oi.price) as total_revenue,
                    COUNT(DISTINCT oi.order_id) as order_count
                FROM products p
                JOIN order_items oi ON p.product_id = oi.product_id
                JOIN orders o ON oi.order_id = o.order_id
                WHERE o.created_at BETWEEN ? AND ?
                GROUP BY p.product_id
                ORDER BY total_sold DESC
                LIMIT 10
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $best_selling = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Category performance
            $stmt = $this->conn->prepare("
                SELECT 
                    c.name as category_name,
                    COUNT(DISTINCT p.product_id) as product_count,
                    SUM(oi.quantity) as total_sold,
                    SUM(oi.quantity * oi.price) as total_revenue
                FROM categories c
                JOIN products p ON c.category_id = p.category_id
                JOIN order_items oi ON p.product_id = oi.product_id
                JOIN orders o ON oi.order_id = o.order_id
                WHERE o.created_at BETWEEN ? AND ?
                GROUP BY c.category_id
                ORDER BY total_revenue DESC
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $category_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Low stock alerts
            $stmt = $this->conn->prepare("
                SELECT name, stock, price
                FROM products 
                WHERE stock <= 10 AND is_active = 1
                ORDER BY stock ASC
                LIMIT 20
            ");
            $stmt->execute();
            $low_stock = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'best_selling' => $best_selling,
                'category_performance' => $category_performance,
                'low_stock_alerts' => $low_stock
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting product performance: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get traffic analytics
     */
    private function getTrafficAnalytics($date_from, $date_to) {
        try {
            // Page views
            $stmt = $this->conn->prepare("
                SELECT 
                    page_url,
                    COUNT(*) as page_views,
                    COUNT(DISTINCT session_id) as unique_visitors
                FROM analytics_page_views 
                WHERE created_at BETWEEN ? AND ?
                GROUP BY page_url
                ORDER BY page_views DESC
                LIMIT 10
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $top_pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Traffic sources
            $stmt = $this->conn->prepare("
                SELECT 
                    referrer_domain,
                    COUNT(*) as visits,
                    COUNT(DISTINCT session_id) as unique_visitors
                FROM analytics_sessions 
                WHERE created_at BETWEEN ? AND ?
                GROUP BY referrer_domain
                ORDER BY visits DESC
                LIMIT 10
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $traffic_sources = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Device breakdown
            $stmt = $this->conn->prepare("
                SELECT 
                    device_type,
                    COUNT(*) as sessions,
                    AVG(session_duration) as avg_duration
                FROM analytics_sessions 
                WHERE created_at BETWEEN ? AND ?
                GROUP BY device_type
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $device_breakdown = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'top_pages' => $top_pages,
                'traffic_sources' => $traffic_sources,
                'device_breakdown' => $device_breakdown
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting traffic analytics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Track page view
     */
    public function trackPageView($page_url, $session_id, $user_id = null, $user_agent = null, $ip_address = null) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO analytics_page_views (
                    page_url, session_id, user_id, user_agent, ip_address, created_at
                ) VALUES (?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$page_url, $session_id, $user_id, $user_agent, $ip_address]);
            
        } catch (PDOException $e) {
            error_log("Error tracking page view: " . $e->getMessage());
        }
    }
    
    /**
     * Track user session
     */
    public function trackSession($session_id, $user_id = null, $referrer = null, $device_type = null, $browser = null) {
        try {
            $referrer_domain = $referrer ? parse_url($referrer, PHP_URL_HOST) : 'direct';
            
            $stmt = $this->conn->prepare("
                INSERT INTO analytics_sessions (
                    session_id, user_id, referrer_domain, device_type, browser, created_at
                ) VALUES (?, ?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE updated_at = NOW()
            ");
            $stmt->execute([$session_id, $user_id, $referrer_domain, $device_type, $browser]);
            
        } catch (PDOException $e) {
            error_log("Error tracking session: " . $e->getMessage());
        }
    }
    
    /**
     * Calculate growth percentage
     */
    private function calculateGrowthPercentage($current, $previous) {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100, 2);
    }
    
    /**
     * Get revenue forecast
     */
    public function getRevenueForecast($months = 3) {
        try {
            // Get historical data for the last 12 months
            $stmt = $this->conn->prepare("
                SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    SUM(total_amount) as revenue
                FROM orders 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                AND status = 'completed'
                GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY month ASC
            ");
            $stmt->execute();
            $historical_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Simple linear regression for forecast
            $forecast = $this->calculateLinearForecast($historical_data, $months);
            
            return [
                'historical_data' => $historical_data,
                'forecast' => $forecast
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting revenue forecast: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Calculate linear forecast
     */
    private function calculateLinearForecast($historical_data, $months) {
        if (count($historical_data) < 2) {
            return [];
        }
        
        $n = count($historical_data);
        $sum_x = 0;
        $sum_y = 0;
        $sum_xy = 0;
        $sum_x2 = 0;
        
        foreach ($historical_data as $i => $data) {
            $x = $i + 1;
            $y = (float)$data['revenue'];
            
            $sum_x += $x;
            $sum_y += $y;
            $sum_xy += $x * $y;
            $sum_x2 += $x * $x;
        }
        
        $slope = ($n * $sum_xy - $sum_x * $sum_y) / ($n * $sum_x2 - $sum_x * $sum_x);
        $intercept = ($sum_y - $slope * $sum_x) / $n;
        
        $forecast = [];
        for ($i = 1; $i <= $months; $i++) {
            $x = $n + $i;
            $predicted_revenue = $slope * $x + $intercept;
            $forecast[] = [
                'month' => date('Y-m', strtotime("+{$i} months")),
                'predicted_revenue' => max(0, $predicted_revenue)
            ];
        }
        
        return $forecast;
    }
    
    /**
     * Get conversion funnel
     */
    public function getConversionFunnel($date_from, $date_to) {
        try {
            // Visitors
            $stmt = $this->conn->prepare("
                SELECT COUNT(DISTINCT session_id) as visitors
                FROM analytics_sessions 
                WHERE created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $visitors = $stmt->fetchColumn();
            
            // Product views
            $stmt = $this->conn->prepare("
                SELECT COUNT(DISTINCT session_id) as product_viewers
                FROM analytics_page_views 
                WHERE page_url LIKE '%product-detail%'
                AND created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $product_viewers = $stmt->fetchColumn();
            
            // Cart additions
            $stmt = $this->conn->prepare("
                SELECT COUNT(DISTINCT user_id) as cart_users
                FROM cart 
                WHERE created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $cart_users = $stmt->fetchColumn();
            
            // Orders
            $stmt = $this->conn->prepare("
                SELECT COUNT(DISTINCT user_id) as buyers
                FROM orders 
                WHERE created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$date_from, $date_to . ' 23:59:59']);
            $buyers = $stmt->fetchColumn();
            
            return [
                'visitors' => $visitors,
                'product_viewers' => $product_viewers,
                'cart_users' => $cart_users,
                'buyers' => $buyers,
                'conversion_rates' => [
                    'visitor_to_product' => $visitors > 0 ? round(($product_viewers / $visitors) * 100, 2) : 0,
                    'product_to_cart' => $product_viewers > 0 ? round(($cart_users / $product_viewers) * 100, 2) : 0,
                    'cart_to_purchase' => $cart_users > 0 ? round(($buyers / $cart_users) * 100, 2) : 0,
                    'overall' => $visitors > 0 ? round(($buyers / $visitors) * 100, 2) : 0
                ]
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting conversion funnel: " . $e->getMessage());
            return [];
        }
    }
}
?>
