/**
 * Analytics Tracker
 * Comprehensive analytics tracking for TeWuNeed website
 */

class AnalyticsTracker {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.userId = this.getUserId();
        this.pageLoadTime = Date.now();
        this.events = [];
        this.isTracking = true;
        
        this.init();
    }
    
    init() {
        // Start session tracking
        this.trackSession();
        
        // Track page view
        this.trackPageView();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Set up periodic data sending
        this.setupPeriodicSending();
        
        // Track page unload
        this.setupUnloadTracking();
    }
    
    /**
     * Generate unique session ID
     */
    generateSessionId() {
        let sessionId = localStorage.getItem('analytics_session_id');
        
        if (!sessionId || this.isSessionExpired()) {
            sessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('analytics_session_id', sessionId);
            localStorage.setItem('analytics_session_start', Date.now().toString());
        }
        
        return sessionId;
    }
    
    /**
     * Check if session is expired (30 minutes)
     */
    isSessionExpired() {
        const sessionStart = localStorage.getItem('analytics_session_start');
        if (!sessionStart) return true;
        
        const thirtyMinutes = 30 * 60 * 1000;
        return (Date.now() - parseInt(sessionStart)) > thirtyMinutes;
    }
    
    /**
     * Get user ID from various sources
     */
    getUserId() {
        // Try to get from meta tag
        const userMeta = document.querySelector('meta[name="user-id"]');
        if (userMeta) return userMeta.content;
        
        // Try to get from data attribute
        if (document.body.dataset.userId) return document.body.dataset.userId;
        
        // Try to get from global variable
        if (window.currentUserId) return window.currentUserId;
        
        return null;
    }
    
    /**
     * Track session start
     */
    trackSession() {
        const sessionData = {
            session_id: this.sessionId,
            user_id: this.userId,
            referrer: document.referrer,
            landing_page: window.location.href,
            device_type: this.getDeviceType(),
            browser: this.getBrowserInfo(),
            operating_system: this.getOperatingSystem(),
            screen_resolution: `${screen.width}x${screen.height}`,
            user_agent: navigator.userAgent,
            timestamp: Date.now()
        };
        
        this.sendData('session', sessionData);
    }
    
    /**
     * Track page view
     */
    trackPageView() {
        const pageData = {
            session_id: this.sessionId,
            user_id: this.userId,
            page_url: window.location.href,
            page_title: document.title,
            referrer_url: document.referrer,
            timestamp: Date.now()
        };
        
        this.sendData('pageview', pageData);
        
        // Track time on page when user leaves
        this.startTimeTracking();
    }
    
    /**
     * Track custom event
     */
    trackEvent(eventType, eventCategory, eventAction, eventLabel = null, eventValue = null, customData = null) {
        const eventData = {
            session_id: this.sessionId,
            user_id: this.userId,
            event_type: eventType,
            event_category: eventCategory,
            event_action: eventAction,
            event_label: eventLabel,
            event_value: eventValue,
            page_url: window.location.href,
            custom_data: customData,
            timestamp: Date.now()
        };
        
        this.events.push(eventData);
        
        // Send immediately for important events
        if (['purchase', 'signup', 'error'].includes(eventType)) {
            this.sendData('event', eventData);
        }
    }
    
    /**
     * Track product view
     */
    trackProductView(productId, categoryId = null, viewSource = null) {
        const productData = {
            session_id: this.sessionId,
            user_id: this.userId,
            product_id: productId,
            category_id: categoryId,
            view_source: viewSource,
            timestamp: Date.now()
        };
        
        this.sendData('product_view', productData);
        
        // Start tracking time spent on product
        this.startProductTimeTracking(productId);
    }
    
    /**
     * Track search query
     */
    trackSearch(query, resultsCount = 0, clickedPosition = null, clickedProductId = null) {
        const searchData = {
            session_id: this.sessionId,
            user_id: this.userId,
            search_query: query,
            results_count: resultsCount,
            clicked_result_position: clickedPosition,
            clicked_product_id: clickedProductId,
            no_results: resultsCount === 0,
            timestamp: Date.now()
        };
        
        this.sendData('search', searchData);
    }
    
    /**
     * Track cart events
     */
    trackCartEvent(eventType, productId, quantity = 1, price = null, cartTotal = null, cartItemsCount = 0) {
        const cartData = {
            session_id: this.sessionId,
            user_id: this.userId,
            event_type: eventType, // add, remove, update, view, abandon
            product_id: productId,
            quantity: quantity,
            price: price,
            cart_total: cartTotal,
            cart_items_count: cartItemsCount,
            timestamp: Date.now()
        };
        
        this.sendData('cart_event', cartData);
    }
    
    /**
     * Track conversion
     */
    trackConversion(conversionType, conversionValue = null, orderId = null, productId = null) {
        const conversionData = {
            session_id: this.sessionId,
            user_id: this.userId,
            conversion_type: conversionType,
            conversion_value: conversionValue,
            order_id: orderId,
            product_id: productId,
            attribution_source: this.getAttributionSource(),
            time_to_conversion: this.getTimeToConversion(),
            timestamp: Date.now()
        };
        
        this.sendData('conversion', conversionData);
    }
    
    /**
     * Track heatmap data (clicks, scrolls)
     */
    trackHeatmapData(eventType, x = null, y = null, elementSelector = null, scrollDepth = null) {
        const heatmapData = {
            session_id: this.sessionId,
            page_url: window.location.href,
            element_selector: elementSelector,
            click_x: x,
            click_y: y,
            scroll_depth: scrollDepth,
            viewport_width: window.innerWidth,
            viewport_height: window.innerHeight,
            event_type: eventType,
            timestamp: Date.now()
        };
        
        // Batch heatmap data to avoid too many requests
        this.events.push(heatmapData);
    }
    
    /**
     * Set up event listeners for automatic tracking
     */
    setupEventListeners() {
        // Track clicks
        document.addEventListener('click', (e) => {
            this.handleClick(e);
        });
        
        // Track form submissions
        document.addEventListener('submit', (e) => {
            this.handleFormSubmit(e);
        });
        
        // Track scroll depth
        this.setupScrollTracking();
        
        // Track file downloads
        this.setupDownloadTracking();
        
        // Track external links
        this.setupExternalLinkTracking();
        
        // Track errors
        this.setupErrorTracking();
    }
    
    /**
     * Handle click events
     */
    handleClick(event) {
        const element = event.target;
        const elementInfo = this.getElementInfo(element);
        
        // Track heatmap click
        this.trackHeatmapData('click', event.clientX, event.clientY, elementInfo.selector);
        
        // Track specific element types
        if (element.matches('a')) {
            this.trackEvent('click', 'link', 'click', element.href, null, elementInfo);
        } else if (element.matches('button')) {
            this.trackEvent('click', 'button', 'click', elementInfo.text, null, elementInfo);
        } else if (element.matches('.product-card, .product-item')) {
            const productId = element.dataset.productId;
            if (productId) {
                this.trackEvent('click', 'product', 'click', productId, null, elementInfo);
            }
        }
    }
    
    /**
     * Handle form submissions
     */
    handleFormSubmit(event) {
        const form = event.target;
        const formInfo = {
            id: form.id,
            action: form.action,
            method: form.method,
            fields: this.getFormFields(form)
        };
        
        this.trackEvent('form', 'submit', form.id || 'unnamed_form', null, null, formInfo);
    }
    
    /**
     * Set up scroll tracking
     */
    setupScrollTracking() {
        let maxScroll = 0;
        let scrollTimer;
        
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimer);
            
            const scrollPercent = Math.round(
                (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
            );
            
            if (scrollPercent > maxScroll) {
                maxScroll = scrollPercent;
            }
            
            scrollTimer = setTimeout(() => {
                this.trackHeatmapData('scroll', null, null, null, scrollPercent);
            }, 250);
        });
        
        // Track final scroll depth on page unload
        window.addEventListener('beforeunload', () => {
            if (maxScroll > 0) {
                this.trackEvent('scroll', 'depth', 'max_scroll', null, maxScroll);
            }
        });
    }
    
    /**
     * Set up download tracking
     */
    setupDownloadTracking() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && this.isDownloadLink(link.href)) {
                this.trackEvent('download', 'file', 'download', link.href);
            }
        });
    }
    
    /**
     * Set up external link tracking
     */
    setupExternalLinkTracking() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && this.isExternalLink(link.href)) {
                this.trackEvent('outbound', 'link', 'click', link.href);
            }
        });
    }
    
    /**
     * Set up error tracking
     */
    setupErrorTracking() {
        window.addEventListener('error', (e) => {
            this.trackEvent('error', 'javascript', 'error', e.message, null, {
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                stack: e.error ? e.error.stack : null
            });
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            this.trackEvent('error', 'promise', 'rejection', e.reason, null, {
                reason: e.reason
            });
        });
    }
    
    /**
     * Start time tracking for current page
     */
    startTimeTracking() {
        this.pageStartTime = Date.now();
    }
    
    /**
     * Start time tracking for product view
     */
    startProductTimeTracking(productId) {
        this.productStartTime = Date.now();
        this.currentProductId = productId;
    }
    
    /**
     * Set up periodic data sending
     */
    setupPeriodicSending() {
        // Send batched events every 30 seconds
        setInterval(() => {
            this.sendBatchedEvents();
        }, 30000);
    }
    
    /**
     * Set up unload tracking
     */
    setupUnloadTracking() {
        window.addEventListener('beforeunload', () => {
            // Send final page time
            if (this.pageStartTime) {
                const timeOnPage = Math.round((Date.now() - this.pageStartTime) / 1000);
                this.trackEvent('timing', 'page', 'time_on_page', window.location.href, timeOnPage);
            }
            
            // Send final product time
            if (this.productStartTime && this.currentProductId) {
                const timeOnProduct = Math.round((Date.now() - this.productStartTime) / 1000);
                this.trackEvent('timing', 'product', 'time_on_product', this.currentProductId, timeOnProduct);
            }
            
            // Send any remaining events
            this.sendBatchedEvents(true);
        });
    }
    
    /**
     * Send data to server
     */
    sendData(type, data) {
        if (!this.isTracking) return;
        
        const payload = {
            type: type,
            data: data,
            timestamp: Date.now()
        };
        
        // Use sendBeacon for reliability, fallback to fetch
        if (navigator.sendBeacon) {
            navigator.sendBeacon('ajax/analytics_track.php', JSON.stringify(payload));
        } else {
            fetch('ajax/analytics_track.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload),
                keepalive: true
            }).catch(error => {
                console.warn('Analytics tracking failed:', error);
            });
        }
    }
    
    /**
     * Send batched events
     */
    sendBatchedEvents(force = false) {
        if (this.events.length === 0) return;
        
        if (this.events.length >= 10 || force) {
            const eventsToSend = this.events.splice(0, 50); // Send max 50 events at once
            
            this.sendData('batch_events', {
                events: eventsToSend,
                session_id: this.sessionId,
                user_id: this.userId
            });
        }
    }
    
    /**
     * Utility functions
     */
    getDeviceType() {
        const width = window.innerWidth;
        if (width <= 768) return 'mobile';
        if (width <= 1024) return 'tablet';
        return 'desktop';
    }
    
    getBrowserInfo() {
        const ua = navigator.userAgent;
        if (ua.includes('Chrome')) return 'Chrome';
        if (ua.includes('Firefox')) return 'Firefox';
        if (ua.includes('Safari')) return 'Safari';
        if (ua.includes('Edge')) return 'Edge';
        return 'Other';
    }
    
    getOperatingSystem() {
        const ua = navigator.userAgent;
        if (ua.includes('Windows')) return 'Windows';
        if (ua.includes('Mac')) return 'macOS';
        if (ua.includes('Linux')) return 'Linux';
        if (ua.includes('Android')) return 'Android';
        if (ua.includes('iOS')) return 'iOS';
        return 'Other';
    }
    
    getElementInfo(element) {
        return {
            tag: element.tagName.toLowerCase(),
            id: element.id,
            classes: Array.from(element.classList),
            text: element.textContent.trim().substring(0, 100),
            selector: this.getElementSelector(element)
        };
    }
    
    getElementSelector(element) {
        if (element.id) return `#${element.id}`;
        
        let selector = element.tagName.toLowerCase();
        if (element.className) {
            selector += '.' + Array.from(element.classList).join('.');
        }
        
        return selector;
    }
    
    getFormFields(form) {
        const fields = {};
        const formData = new FormData(form);
        
        for (let [key, value] of formData.entries()) {
            // Don't track sensitive data
            if (!this.isSensitiveField(key)) {
                fields[key] = typeof value === 'string' ? value.substring(0, 100) : 'file';
            }
        }
        
        return fields;
    }
    
    isSensitiveField(fieldName) {
        const sensitiveFields = ['password', 'credit_card', 'ssn', 'social_security'];
        return sensitiveFields.some(field => fieldName.toLowerCase().includes(field));
    }
    
    isDownloadLink(url) {
        const downloadExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar'];
        return downloadExtensions.some(ext => url.toLowerCase().includes(ext));
    }
    
    isExternalLink(url) {
        try {
            const link = new URL(url);
            return link.hostname !== window.location.hostname;
        } catch {
            return false;
        }
    }
    
    getAttributionSource() {
        const urlParams = new URLSearchParams(window.location.search);
        return {
            utm_source: urlParams.get('utm_source'),
            utm_medium: urlParams.get('utm_medium'),
            utm_campaign: urlParams.get('utm_campaign'),
            utm_term: urlParams.get('utm_term'),
            utm_content: urlParams.get('utm_content')
        };
    }
    
    getTimeToConversion() {
        const sessionStart = localStorage.getItem('analytics_session_start');
        if (sessionStart) {
            return Math.round((Date.now() - parseInt(sessionStart)) / 1000);
        }
        return null;
    }
    
    /**
     * Public methods for manual tracking
     */
    disable() {
        this.isTracking = false;
    }
    
    enable() {
        this.isTracking = true;
    }
    
    setUserId(userId) {
        this.userId = userId;
    }
    
    setCustomDimension(key, value) {
        this.customDimensions = this.customDimensions || {};
        this.customDimensions[key] = value;
    }
}

// Initialize analytics tracker
document.addEventListener('DOMContentLoaded', () => {
    window.analyticsTracker = new AnalyticsTracker();
    
    // Expose tracking functions globally
    window.trackEvent = (type, category, action, label, value, data) => {
        window.analyticsTracker.trackEvent(type, category, action, label, value, data);
    };
    
    window.trackProductView = (productId, categoryId, source) => {
        window.analyticsTracker.trackProductView(productId, categoryId, source);
    };
    
    window.trackSearch = (query, count, position, productId) => {
        window.analyticsTracker.trackSearch(query, count, position, productId);
    };
    
    window.trackCartEvent = (type, productId, quantity, price, total, count) => {
        window.analyticsTracker.trackCartEvent(type, productId, quantity, price, total, count);
    };
    
    window.trackConversion = (type, value, orderId, productId) => {
        window.analyticsTracker.trackConversion(type, value, orderId, productId);
    };
});

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnalyticsTracker;
}
