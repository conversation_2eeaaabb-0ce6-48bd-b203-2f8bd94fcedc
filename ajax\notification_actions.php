<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/NotificationManager.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Please login to access notifications'
    ]);
    exit;
}

$user_id = $_SESSION['user_id'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

$notificationManager = new NotificationManager();

try {
    switch ($action) {
        case 'get_notifications':
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 20);
            $filter = $_GET['filter'] ?? 'all';
            
            $result = $notificationManager->getUserNotifications($user_id, $page, $limit, $filter);
            
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'get_unread':
            $limit = (int)($_GET['limit'] ?? 10);
            $notifications = $notificationManager->getUnreadNotifications($user_id, $limit);
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications
            ]);
            break;
            
        case 'get_counts':
            $counts = $notificationManager->getNotificationCounts($user_id);
            
            echo json_encode([
                'success' => true,
                'counts' => $counts
            ]);
            break;
            
        case 'mark_read':
            $notification_id = (int)($_POST['notification_id'] ?? 0);
            
            if (!$notification_id) {
                throw new Exception('Notification ID is required');
            }
            
            $result = $notificationManager->markAsRead($notification_id, $user_id);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Notification marked as read'
                ]);
            } else {
                throw new Exception('Failed to mark notification as read');
            }
            break;
            
        case 'mark_all_read':
            $result = $notificationManager->markAllAsRead($user_id);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'All notifications marked as read'
                ]);
            } else {
                throw new Exception('Failed to mark all notifications as read');
            }
            break;
            
        case 'delete':
            $notification_id = (int)($_POST['notification_id'] ?? 0);
            
            if (!$notification_id) {
                throw new Exception('Notification ID is required');
            }
            
            $result = $notificationManager->deleteNotification($notification_id, $user_id);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Notification deleted'
                ]);
            } else {
                throw new Exception('Failed to delete notification');
            }
            break;
            
        case 'create_test':
            // For testing purposes - create a test notification
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('POST method required');
            }
            
            $type = $_POST['type'] ?? 'system';
            $title = $_POST['title'] ?? 'Test Notification';
            $message = $_POST['message'] ?? 'This is a test notification';
            $priority = $_POST['priority'] ?? 'normal';
            
            $notification_id = $notificationManager->createNotification(
                $user_id, 
                $type, 
                $title, 
                $message, 
                ['test' => true], 
                $priority
            );
            
            if ($notification_id) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Test notification created',
                    'notification_id' => $notification_id
                ]);
            } else {
                throw new Exception('Failed to create test notification');
            }
            break;
            
        case 'simulate_order_update':
            // Simulate order status update notification
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('POST method required');
            }
            
            $order_id = $_POST['order_id'] ?? 'TEST001';
            $status = $_POST['status'] ?? 'shipped';
            
            $notification_id = $notificationManager->createOrderNotification(
                $user_id, 
                $order_id, 
                $status
            );
            
            if ($notification_id) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Order notification created',
                    'notification_id' => $notification_id
                ]);
            } else {
                throw new Exception('Failed to create order notification');
            }
            break;
            
        case 'simulate_payment_update':
            // Simulate payment status update notification
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('POST method required');
            }
            
            $order_id = $_POST['order_id'] ?? 'TEST001';
            $status = $_POST['status'] ?? 'confirmed';
            $amount = (float)($_POST['amount'] ?? 100000);
            $method = $_POST['method'] ?? 'GoPay';
            
            $notification_id = $notificationManager->createPaymentNotification(
                $user_id, 
                $order_id, 
                $status, 
                $amount, 
                $method
            );
            
            if ($notification_id) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Payment notification created',
                    'notification_id' => $notification_id
                ]);
            } else {
                throw new Exception('Failed to create payment notification');
            }
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
