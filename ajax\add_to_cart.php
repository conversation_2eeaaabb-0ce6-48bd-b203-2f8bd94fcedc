<?php
// Enhanced Add to Cart with Debug Support
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in JSON response
session_start();

// Log function for debugging
function logDebug($message) {
    error_log("[ADD_TO_CART] " . $message);
}

logDebug("Add to cart request started");

require_once '../includes/db_connect.php';

// Set response headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Debug: Log all POST data
logDebug("POST data: " . json_encode($_POST));
logDebug("Session user_id: " . ($_SESSION['user_id'] ?? 'not set'));

// Validation with detailed error messages
if (!isset($_SESSION['user_id'])) {
    logDebug("User not logged in");
    echo json_encode([
        'success' => false,
        'message' => 'Please login to add items to cart',
        'debug' => 'User not logged in'
    ]);
    exit;
}

if (!isset($_POST['product_id']) || !is_numeric($_POST['product_id'])) {
    logDebug("Invalid product ID: " . ($_POST['product_id'] ?? 'not set'));
    echo json_encode([
        'success' => false,
        'message' => 'Invalid product ID',
        'debug' => 'Product ID missing or invalid'
    ]);
    exit;
}

$product_id = (int)$_POST['product_id'];
$quantity = isset($_POST['quantity']) && is_numeric($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
$user_id = $_SESSION['user_id'];

logDebug("Processing: Product ID {$product_id}, Quantity {$quantity}, User ID {$user_id}");

if ($quantity <= 0) {
    logDebug("Invalid quantity: {$quantity}");
    echo json_encode([
        'success' => false,
        'message' => 'Quantity must be greater than 0',
        'debug' => 'Invalid quantity'
    ]);
    exit;
}

try {
    logDebug("Starting database operations");

    // Check if product exists and has enough stock
    $stmt = $conn->prepare("
        SELECT product_id, name, price, stock, is_active
        FROM products
        WHERE product_id = ?
    ");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        logDebug("Product not found: {$product_id}");
        throw new Exception('Product not found');
    }

    if (!$product['is_active']) {
        logDebug("Product not active: {$product_id}");
        throw new Exception('Product is not available');
    }

    if ($product['stock'] < $quantity) {
        logDebug("Insufficient stock. Available: {$product['stock']}, Requested: {$quantity}");
        throw new Exception("Insufficient stock. Available: {$product['stock']}");
    }

    logDebug("Product validation passed: {$product['name']}");

    // Get or create cart
    $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $cart_id = $stmt->fetchColumn();

    if (!$cart_id) {
        logDebug("Creating new cart for user {$user_id}");
        $stmt = $conn->prepare("INSERT INTO carts (user_id, created_at) VALUES (?, NOW())");
        $stmt->execute([$user_id]);
        $cart_id = $conn->lastInsertId();
        logDebug("New cart created with ID: {$cart_id}");
    } else {
        logDebug("Using existing cart ID: {$cart_id}");
    }

    // Check if item already exists in cart
    $stmt = $conn->prepare("SELECT quantity FROM cart_items WHERE cart_id = ? AND product_id = ?");
    $stmt->execute([$cart_id, $product_id]);
    $existing_quantity = $stmt->fetchColumn();

    if ($existing_quantity) {
        logDebug("Updating existing cart item. Current quantity: {$existing_quantity}");
        $new_quantity = $existing_quantity + $quantity;

        // Check if new total quantity exceeds stock
        if ($new_quantity > $product['stock']) {
            throw new Exception("Cannot add {$quantity} more. Maximum available: " . ($product['stock'] - $existing_quantity));
        }

        // Update cart item quantity
        $stmt = $conn->prepare("UPDATE cart_items SET quantity = ? WHERE cart_id = ? AND product_id = ?");
        $stmt->execute([$new_quantity, $cart_id, $product_id]);
        $final_quantity = $new_quantity;
    } else {
        logDebug("Adding new item to cart");
        // Add new cart item
        $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity) VALUES (?, ?, ?)");
        $stmt->execute([$cart_id, $product_id, $quantity]);
        $final_quantity = $quantity;
    }

    // Get total cart count
    $stmt = $conn->prepare("SELECT SUM(quantity) FROM cart_items WHERE cart_id = ?");
    $stmt->execute([$cart_id]);
    $cart_count = $stmt->fetchColumn() ?: 0;

    logDebug("Cart updated successfully. Total items: {$cart_count}");

    // Success response
    $response = [
        'success' => true,
        'message' => $product['name'] . ' added to cart successfully!',
        'cart_count' => (int)$cart_count,
        'product_id' => $product_id,
        'product_name' => $product['name'],
        'quantity_added' => $quantity,
        'total_quantity' => $final_quantity,
        'product_price' => (float)$product['price'],
        'available_stock' => (int)$product['stock']
    ];

    logDebug("Sending response: " . json_encode($response));
    echo json_encode($response);

} catch (Exception $e) {
    logDebug("Error occurred: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => 'Check error log for details'
    ]);
}

logDebug("Add to cart request completed");
?>
