<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';
require_once '../includes/PromotionManager.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$promotionManager = new PromotionManager();

// Handle campaign creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'create_campaign':
            $result = createPromotionCampaign($_POST);
            $message = $result['success'] ? 'Campaign created successfully!' : 'Error: ' . $result['message'];
            break;
            
        case 'create_flash_sale':
            $result = createFlashSale($_POST);
            $message = $result['success'] ? 'Flash sale created successfully!' : 'Error: ' . $result['message'];
            break;
            
        case 'create_seasonal':
            $result = createSeasonalCampaign($_POST);
            $message = $result['success'] ? 'Seasonal campaign created successfully!' : 'Error: ' . $result['message'];
            break;
    }
}

// Get active campaigns
$active_campaigns = $promotionManager->getActivePromotions();

$page_title = 'Promotion Campaigns';
require_once 'includes/admin_header.php';

/**
 * Create promotion campaign
 */
function createPromotionCampaign($data) {
    global $promotionManager;
    
    $campaign_data = [
        'name' => $data['campaign_name'],
        'description' => $data['campaign_description'],
        'code' => strtoupper($data['campaign_code']),
        'type' => $data['campaign_type'],
        'discount_type' => $data['discount_type'],
        'discount_value' => (float)$data['discount_value'],
        'max_discount_amount' => !empty($data['max_discount']) ? (float)$data['max_discount'] : null,
        'min_order_amount' => !empty($data['min_order']) ? (float)$data['min_order'] : 0,
        'usage_limit' => !empty($data['usage_limit']) ? (int)$data['usage_limit'] : 0,
        'user_usage_limit' => !empty($data['user_limit']) ? (int)$data['user_limit'] : 0,
        'start_date' => $data['start_date'],
        'end_date' => !empty($data['end_date']) ? $data['end_date'] : null,
        'priority' => (int)$data['priority'],
        'is_active' => 1
    ];
    
    return $promotionManager->createPromotion($campaign_data);
}

/**
 * Create flash sale
 */
function createFlashSale($data) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO flash_sales (
                product_id, original_price, discount_percentage, stock_quantity,
                start_time, end_time, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, 1)
        ");
        
        $stmt->execute([
            $data['product_id'],
            $data['original_price'],
            $data['discount_percentage'],
            $data['stock_quantity'],
            $data['start_time'],
            $data['end_time']
        ]);
        
        return ['success' => true, 'flash_sale_id' => $conn->lastInsertId()];
        
    } catch (PDOException $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Create seasonal campaign
 */
function createSeasonalCampaign($data) {
    global $promotionManager;
    
    // Create multiple related promotions for seasonal campaign
    $campaigns = [];
    
    // Main seasonal discount
    $main_campaign = [
        'name' => $data['season_name'] . ' Sale',
        'description' => $data['season_description'],
        'code' => strtoupper($data['season_code']),
        'type' => 'seasonal',
        'discount_type' => 'percentage',
        'discount_value' => (float)$data['main_discount'],
        'min_order_amount' => (float)$data['min_order'],
        'start_date' => $data['start_date'],
        'end_date' => $data['end_date'],
        'priority' => 5,
        'is_active' => 1
    ];
    
    $result1 = $promotionManager->createPromotion($main_campaign);
    
    // Free shipping promotion
    $shipping_campaign = [
        'name' => $data['season_name'] . ' Free Shipping',
        'description' => 'Free shipping during ' . $data['season_name'],
        'code' => strtoupper($data['season_code']) . 'SHIP',
        'type' => 'seasonal',
        'discount_type' => 'free_shipping',
        'discount_value' => 0,
        'min_order_amount' => (float)$data['free_ship_min'],
        'start_date' => $data['start_date'],
        'end_date' => $data['end_date'],
        'priority' => 3,
        'is_active' => 1
    ];
    
    $result2 = $promotionManager->createPromotion($shipping_campaign);
    
    return [
        'success' => $result1['success'] && $result2['success'],
        'message' => 'Seasonal campaign with ' . ($result1['success'] + $result2['success']) . ' promotions created'
    ];
}
?>

<style>
.campaign-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease;
}

.campaign-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.campaign-template {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.campaign-template:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.campaign-template.active {
    border-color: #007bff;
    background: #e3f2fd;
}

.promotion-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.promotion-badge.coupon {
    background: #e3f2fd;
    color: #1976d2;
}

.promotion-badge.flash_sale {
    background: #ffebee;
    color: #d32f2f;
}

.promotion-badge.seasonal {
    background: #f3e5f5;
    color: #7b1fa2;
}

.promotion-badge.voucher {
    background: #e8f5e8;
    color: #388e3c;
}

.stats-card {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.stats-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}
</style>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-bullhorn me-3"></i>Promotion Campaigns</h1>
                <div class="btn-group">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#campaignModal">
                        <i class="fas fa-plus me-1"></i>New Campaign
                    </button>
                    <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#flashSaleModal">
                        <i class="fas fa-bolt me-1"></i>Flash Sale
                    </button>
                    <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#seasonalModal">
                        <i class="fas fa-calendar me-1"></i>Seasonal Campaign
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($message)): ?>
    <div class="alert alert-info alert-dismissible fade show">
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- Campaign Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-value"><?php echo count($active_campaigns); ?></div>
                <div class="stats-label">Active Campaigns</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-value">
                    <?php 
                    $total_usage = 0;
                    foreach ($active_campaigns as $campaign) {
                        $total_usage += $campaign['total_usage'] ?? 0;
                    }
                    echo number_format($total_usage);
                    ?>
                </div>
                <div class="stats-label">Total Usage</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-value">
                    <?php 
                    $discount_given = 0;
                    // This would be calculated from promotion_usage table
                    echo 'Rp ' . number_format($discount_given);
                    ?>
                </div>
                <div class="stats-label">Discount Given</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-value">
                    <?php 
                    $conversion_rate = 0;
                    // This would be calculated from analytics
                    echo number_format($conversion_rate, 1) . '%';
                    ?>
                </div>
                <div class="stats-label">Conversion Rate</div>
            </div>
        </div>
    </div>

    <!-- Pre-built Campaign Templates -->
    <div class="row">
        <div class="col-12">
            <div class="campaign-card">
                <h3><i class="fas fa-magic me-2"></i>Quick Campaign Templates</h3>
                <p class="text-muted">Choose from pre-built campaign templates to get started quickly</p>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="campaign-template" onclick="loadTemplate('welcome')">
                            <i class="fas fa-hand-wave fa-3x text-primary mb-3"></i>
                            <h5>Welcome Discount</h5>
                            <p class="text-muted">10% off for new customers</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="campaign-template" onclick="loadTemplate('flash')">
                            <i class="fas fa-bolt fa-3x text-danger mb-3"></i>
                            <h5>Flash Sale</h5>
                            <p class="text-muted">Limited time 50% off</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="campaign-template" onclick="loadTemplate('shipping')">
                            <i class="fas fa-shipping-fast fa-3x text-success mb-3"></i>
                            <h5>Free Shipping</h5>
                            <p class="text-muted">Free delivery above Rp 100k</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="campaign-template" onclick="loadTemplate('loyalty')">
                            <i class="fas fa-heart fa-3x text-warning mb-3"></i>
                            <h5>Loyalty Reward</h5>
                            <p class="text-muted">15% off for returning customers</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Campaigns -->
    <div class="row">
        <div class="col-12">
            <div class="campaign-card">
                <h3><i class="fas fa-list me-2"></i>Active Campaigns</h3>
                
                <?php if (empty($active_campaigns)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-bullhorn fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No Active Campaigns</h5>
                    <p class="text-muted">Create your first promotional campaign to boost sales!</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#campaignModal">
                        <i class="fas fa-plus me-1"></i>Create Campaign
                    </button>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Campaign</th>
                                <th>Type</th>
                                <th>Code</th>
                                <th>Discount</th>
                                <th>Usage</th>
                                <th>Period</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($active_campaigns as $campaign): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($campaign['name']); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo htmlspecialchars($campaign['description']); ?></small>
                                </td>
                                <td>
                                    <span class="promotion-badge <?php echo $campaign['type']; ?>">
                                        <?php echo ucfirst($campaign['type']); ?>
                                    </span>
                                </td>
                                <td>
                                    <code><?php echo htmlspecialchars($campaign['code']); ?></code>
                                </td>
                                <td>
                                    <?php if ($campaign['discount_type'] === 'percentage'): ?>
                                        <?php echo $campaign['discount_value']; ?>%
                                    <?php elseif ($campaign['discount_type'] === 'fixed'): ?>
                                        Rp <?php echo number_format($campaign['discount_value']); ?>
                                    <?php else: ?>
                                        <?php echo ucfirst(str_replace('_', ' ', $campaign['discount_type'])); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $campaign['total_usage'] ?? 0; ?>
                                    <?php if ($campaign['usage_limit'] > 0): ?>
                                        / <?php echo $campaign['usage_limit']; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small>
                                        <?php echo date('M j', strtotime($campaign['start_date'])); ?>
                                        <?php if ($campaign['end_date']): ?>
                                            - <?php echo date('M j', strtotime($campaign['end_date'])); ?>
                                        <?php endif; ?>
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-success">Active</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="editCampaign(<?php echo $campaign['promotion_id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="viewStats(<?php echo $campaign['promotion_id']; ?>)">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deactivateCampaign(<?php echo $campaign['promotion_id']; ?>)">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Campaign Creation Modal -->
<div class="modal fade" id="campaignModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Campaign</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_campaign">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Campaign Name</label>
                                <input type="text" class="form-control" name="campaign_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Campaign Code</label>
                                <input type="text" class="form-control" name="campaign_code" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="campaign_description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Campaign Type</label>
                                <select class="form-select" name="campaign_type" required>
                                    <option value="coupon">Coupon</option>
                                    <option value="voucher">Voucher</option>
                                    <option value="seasonal">Seasonal</option>
                                    <option value="loyalty">Loyalty</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Discount Type</label>
                                <select class="form-select" name="discount_type" required>
                                    <option value="percentage">Percentage</option>
                                    <option value="fixed">Fixed Amount</option>
                                    <option value="free_shipping">Free Shipping</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Discount Value</label>
                                <input type="number" class="form-control" name="discount_value" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Max Discount (Optional)</label>
                                <input type="number" class="form-control" name="max_discount" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Min Order Amount</label>
                                <input type="number" class="form-control" name="min_order" step="0.01" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Usage Limit (0 = unlimited)</label>
                                <input type="number" class="form-control" name="usage_limit" value="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">User Limit (0 = unlimited)</label>
                                <input type="number" class="form-control" name="user_limit" value="1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Priority (1-10)</label>
                                <input type="number" class="form-control" name="priority" min="1" max="10" value="5">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Start Date</label>
                                <input type="datetime-local" class="form-control" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">End Date (Optional)</label>
                                <input type="datetime-local" class="form-control" name="end_date">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Campaign</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Flash Sale Modal -->
<div class="modal fade" id="flashSaleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Flash Sale</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_flash_sale">
                    
                    <div class="mb-3">
                        <label class="form-label">Product</label>
                        <select class="form-select" name="product_id" required>
                            <option value="">Select Product</option>
                            <?php
                            $stmt = $conn->prepare("SELECT product_id, name, price FROM products WHERE is_active = 1 ORDER BY name");
                            $stmt->execute();
                            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            foreach ($products as $product):
                            ?>
                            <option value="<?php echo $product['product_id']; ?>" data-price="<?php echo $product['price']; ?>">
                                <?php echo htmlspecialchars($product['name']); ?> - Rp <?php echo number_format($product['price']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Original Price</label>
                                <input type="number" class="form-control" name="original_price" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Discount %</label>
                                <input type="number" class="form-control" name="discount_percentage" min="1" max="90" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Flash Sale Stock</label>
                        <input type="number" class="form-control" name="stock_quantity" min="1" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Start Time</label>
                                <input type="datetime-local" class="form-control" name="start_time" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">End Time</label>
                                <input type="datetime-local" class="form-control" name="end_time" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Create Flash Sale</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Seasonal Campaign Modal -->
<div class="modal fade" id="seasonalModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Seasonal Campaign</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_seasonal">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Season Name</label>
                                <input type="text" class="form-control" name="season_name" placeholder="e.g., Christmas, Ramadan, New Year" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Campaign Code</label>
                                <input type="text" class="form-control" name="season_code" placeholder="e.g., XMAS2024" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Campaign Description</label>
                        <textarea class="form-control" name="season_description" rows="3" placeholder="Describe your seasonal campaign..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Main Discount %</label>
                                <input type="number" class="form-control" name="main_discount" min="1" max="50" value="20" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Min Order for Discount</label>
                                <input type="number" class="form-control" name="min_order" value="100000" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Free Shipping Min</label>
                                <input type="number" class="form-control" name="free_ship_min" value="150000" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Campaign Start</label>
                                <input type="datetime-local" class="form-control" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Campaign End</label>
                                <input type="datetime-local" class="form-control" name="end_date" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Create Seasonal Campaign</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Load campaign templates
function loadTemplate(type) {
    const templates = {
        welcome: {
            name: 'Welcome New Customer',
            code: 'WELCOME10',
            description: 'Special discount for first-time customers',
            type: 'coupon',
            discount_type: 'percentage',
            discount_value: 10,
            min_order: 50000,
            user_limit: 1
        },
        flash: {
            name: 'Flash Sale 50% Off',
            code: 'FLASH50',
            description: 'Limited time flash sale',
            type: 'flash_sale',
            discount_type: 'percentage',
            discount_value: 50,
            usage_limit: 100
        },
        shipping: {
            name: 'Free Shipping Promo',
            code: 'FREESHIP',
            description: 'Free shipping for orders above Rp 100,000',
            type: 'voucher',
            discount_type: 'free_shipping',
            discount_value: 0,
            min_order: 100000
        },
        loyalty: {
            name: 'Loyalty Customer Reward',
            code: 'LOYAL15',
            description: 'Special discount for returning customers',
            type: 'loyalty',
            discount_type: 'percentage',
            discount_value: 15,
            min_order: 75000
        }
    };
    
    const template = templates[type];
    if (template) {
        // Fill form with template data
        document.querySelector('[name="campaign_name"]').value = template.name;
        document.querySelector('[name="campaign_code"]').value = template.code;
        document.querySelector('[name="campaign_description"]').value = template.description;
        document.querySelector('[name="campaign_type"]').value = template.type;
        document.querySelector('[name="discount_type"]').value = template.discount_type;
        document.querySelector('[name="discount_value"]').value = template.discount_value;
        document.querySelector('[name="min_order"]').value = template.min_order || 0;
        document.querySelector('[name="usage_limit"]').value = template.usage_limit || 0;
        document.querySelector('[name="user_limit"]').value = template.user_limit || 0;
        
        // Set start date to now
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        document.querySelector('[name="start_date"]').value = now.toISOString().slice(0, 16);
        
        // Show modal
        new bootstrap.Modal(document.getElementById('campaignModal')).show();
    }
}

// Auto-fill original price when product is selected
document.querySelector('[name="product_id"]').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const price = selectedOption.dataset.price;
    if (price) {
        document.querySelector('[name="original_price"]').value = price;
    }
});

// Campaign management functions
function editCampaign(id) {
    // Implementation for editing campaign
    alert('Edit campaign functionality would be implemented here');
}

function viewStats(id) {
    // Implementation for viewing campaign statistics
    alert('Campaign statistics would be shown here');
}

function deactivateCampaign(id) {
    if (confirm('Are you sure you want to deactivate this campaign?')) {
        // Implementation for deactivating campaign
        alert('Campaign deactivation would be implemented here');
    }
}

// Set default dates
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    
    // Set default start time to current time
    const startInputs = document.querySelectorAll('[name="start_date"], [name="start_time"]');
    startInputs.forEach(input => {
        if (!input.value) {
            input.value = now.toISOString().slice(0, 16);
        }
    });
    
    // Set default end time to 7 days from now
    const endDate = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    const endInputs = document.querySelectorAll('[name="end_date"], [name="end_time"]');
    endInputs.forEach(input => {
        if (!input.value) {
            input.value = endDate.toISOString().slice(0, 16);
        }
    });
});
</script>

<?php require_once 'includes/admin_footer.php'; ?>
