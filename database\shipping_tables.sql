-- Shipping System Tables
USE db_tewuneed;

-- Create shipping table
CREATE TABLE IF NOT EXISTS shipping (
    shipping_id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(50) NOT NULL,
    provider_code VARCHAR(20) NOT NULL,
    service_code VARCHAR(20) NOT NULL,
    tracking_number VARCHAR(50) UNIQUE NOT NULL,
    shipping_cost DECIMAL(15,2) NOT NULL DEFAULT 0,
    recipient_name VARCHAR(100) NOT NULL,
    recipient_phone VARCHAR(20) NOT NULL,
    recipient_address TEXT NOT NULL,
    recipient_city VARCHAR(100) NOT NULL,
    recipient_postal_code VARCHAR(10) NOT NULL,
    status ENUM('pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed', 'returned') DEFAULT 'pending',
    notes TEXT NULL,
    estimated_delivery DATE NULL,
    actual_delivery DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON>N KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    INDEX idx_tracking_number (tracking_number),
    INDEX idx_order_id (order_id),
    INDEX idx_status (status),
    INDEX idx_provider (provider_code, service_code)
);

-- Create shipping tracking history table
CREATE TABLE IF NOT EXISTS shipping_tracking (
    tracking_id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_number VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    location VARCHAR(100) NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tracking_number) REFERENCES shipping(tracking_number) ON DELETE CASCADE,
    INDEX idx_tracking_number (tracking_number),
    INDEX idx_created_at (created_at)
);

-- Create shipping providers table for dynamic configuration
CREATE TABLE IF NOT EXISTS shipping_providers (
    provider_id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    logo VARCHAR(255) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    api_endpoint VARCHAR(255) NULL,
    api_key VARCHAR(255) NULL,
    base_rate DECIMAL(10,2) DEFAULT 0,
    free_shipping_threshold DECIMAL(15,2) DEFAULT 100000,
    coverage_areas JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create shipping services table
CREATE TABLE IF NOT EXISTS shipping_services (
    service_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    code VARCHAR(20) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    estimated_days VARCHAR(10) NOT NULL,
    rate_per_kg DECIMAL(10,2) NOT NULL,
    min_weight DECIMAL(5,2) DEFAULT 0,
    max_weight DECIMAL(5,2) DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES shipping_providers(provider_id) ON DELETE CASCADE,
    UNIQUE KEY unique_provider_service (provider_id, code),
    INDEX idx_provider_active (provider_id, is_active)
);

-- Insert shipping providers
INSERT INTO shipping_providers (code, name, logo, base_rate, free_shipping_threshold) VALUES
('jne', 'JNE', 'assets/img/shipping/jne.png', 5000, 100000),
('pos', 'Pos Indonesia', 'assets/img/shipping/pos.png', 5000, 100000),
('tiki', 'TIKI', 'assets/img/shipping/tiki.png', 5000, 100000),
('sicepat', 'SiCepat', 'assets/img/shipping/sicepat.png', 5000, 100000),
('jnt', 'J&T Express', 'assets/img/shipping/jnt.png', 5000, 100000),
('same_day', 'Same Day Delivery', 'assets/img/shipping/same-day.png', 15000, 0);

-- Insert shipping services
INSERT INTO shipping_services (provider_id, code, name, description, estimated_days, rate_per_kg) VALUES
-- JNE Services
(1, 'REG', 'Regular', 'Reliable standard delivery service', '2-3', 9000),
(1, 'YES', 'Yakin Esok Sampai', 'Next day delivery guarantee', '1', 15000),
(1, 'OKE', 'Ongkos Kirim Ekonomis', 'Economical shipping option', '3-4', 7000),

-- Pos Indonesia Services
(2, 'PAKET_KILAT_KHUSUS', 'Paket Kilat Khusus', 'Express delivery by Pos Indonesia', '1', 12000),
(2, 'EXPRESS', 'Pos Express', 'Fast and reliable express service', '1-2', 10000),
(2, 'REGULER', 'Pos Reguler', 'Standard postal service', '3-5', 6000),

-- TIKI Services
(3, 'ONS', 'Over Night Service', 'Next day delivery service', '1', 14000),
(3, 'REG', 'Regular Service', 'Standard TIKI delivery', '2-3', 8000),
(3, 'ECO', 'Economy Service', 'Budget-friendly option', '4-5', 6500),

-- SiCepat Services
(4, 'SIUNT', 'SiUntung', 'Best value delivery service', '2-3', 7500),
(4, 'BEST', 'Best', 'Premium delivery service', '1-2', 11000),
(4, 'GOKIL', 'Gokil', 'Super fast delivery', '1', 16000),

-- J&T Express Services
(5, 'EZ', 'EZ', 'Easy and affordable delivery', '2-3', 8000),
(5, 'REG', 'Regular', 'Standard J&T delivery', '3-4', 7000),

-- Same Day Delivery Services
(6, 'INSTANT', 'Instant', 'Ultra-fast delivery within hours', '0', 25000),
(6, 'SAME_DAY', 'Same Day', 'Delivered on the same day', '0', 18000);

-- Create shipping zones table for different pricing
CREATE TABLE IF NOT EXISTS shipping_zones (
    zone_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    cities JSON NOT NULL,
    multiplier DECIMAL(3,2) DEFAULT 1.00,
    additional_days INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE
);

-- Insert shipping zones
INSERT INTO shipping_zones (name, cities, multiplier, additional_days) VALUES
('Jakarta & Sekitarnya', '["Jakarta", "Bogor", "Depok", "Tangerang", "Bekasi"]', 1.00, 0),
('Jawa Barat', '["Bandung", "Cirebon", "Sukabumi", "Tasikmalaya", "Garut"]', 1.20, 1),
('Jawa Tengah', '["Semarang", "Solo", "Yogyakarta", "Magelang", "Purwokerto"]', 1.50, 2),
('Jawa Timur', '["Surabaya", "Malang", "Kediri", "Madiun", "Jember"]', 1.50, 2),
('Sumatera', '["Medan", "Palembang", "Pekanbaru", "Padang", "Lampung"]', 2.00, 3),
('Kalimantan', '["Balikpapan", "Banjarmasin", "Pontianak", "Samarinda"]', 2.50, 4),
('Sulawesi', '["Makassar", "Manado", "Palu", "Kendari"]', 2.50, 4),
('Indonesia Timur', '["Denpasar", "Mataram", "Kupang", "Jayapura", "Ambon"]', 3.00, 5);

-- Create shipping rates table for zone-based pricing
CREATE TABLE IF NOT EXISTS shipping_rates (
    rate_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    service_id INT NOT NULL,
    zone_id INT NOT NULL,
    weight_min DECIMAL(5,2) NOT NULL,
    weight_max DECIMAL(5,2) NOT NULL,
    rate DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES shipping_providers(provider_id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES shipping_services(service_id) ON DELETE CASCADE,
    FOREIGN KEY (zone_id) REFERENCES shipping_zones(zone_id) ON DELETE CASCADE,
    UNIQUE KEY unique_rate (provider_id, service_id, zone_id, weight_min, weight_max)
);

-- Add shipping cost to orders table if not exists
ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipping_cost DECIMAL(15,2) DEFAULT 0 AFTER total_amount;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipping_provider VARCHAR(50) NULL AFTER shipping_cost;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipping_service VARCHAR(50) NULL AFTER shipping_provider;

-- Create indexes for better performance
CREATE INDEX idx_orders_shipping ON orders(shipping_provider, shipping_service);
CREATE INDEX idx_shipping_status_date ON shipping(status, created_at);
CREATE INDEX idx_tracking_status_date ON shipping_tracking(status, created_at);

-- Promotions System Tables

-- Create promotions table
CREATE TABLE IF NOT EXISTS promotions (
    promotion_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT NULL,
    code VARCHAR(50) UNIQUE NULL,
    type ENUM('coupon', 'voucher', 'flash_sale', 'seasonal', 'loyalty', 'referral') NOT NULL,
    discount_type ENUM('percentage', 'fixed', 'buy_x_get_y', 'free_shipping') NOT NULL,
    discount_value DECIMAL(10,2) NOT NULL,
    max_discount_amount DECIMAL(15,2) NULL,
    min_order_amount DECIMAL(15,2) DEFAULT 0,
    usage_limit INT DEFAULT 0,
    user_usage_limit INT DEFAULT 0,
    total_usage INT DEFAULT 0,
    applicable_products JSON NULL,
    applicable_categories JSON NULL,
    conditions JSON NULL,
    start_date DATETIME NOT NULL,
    end_date DATETIME NULL,
    priority INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_type_active (type, is_active),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_priority (priority)
);

-- Create promotion usage tracking table
CREATE TABLE IF NOT EXISTS promotion_usage (
    usage_id INT AUTO_INCREMENT PRIMARY KEY,
    promotion_id INT NOT NULL,
    user_id INT NOT NULL,
    order_id VARCHAR(50) NULL,
    discount_amount DECIMAL(15,2) NOT NULL,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (promotion_id) REFERENCES promotions(promotion_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_promotion_user (promotion_id, user_id),
    INDEX idx_used_at (used_at)
);

-- Create flash sales table
CREATE TABLE IF NOT EXISTS flash_sales (
    flash_sale_id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    original_price DECIMAL(15,2) NOT NULL,
    discount_percentage DECIMAL(5,2) NOT NULL,
    stock_quantity INT NOT NULL,
    sold_quantity INT DEFAULT 0,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    INDEX idx_product_active (product_id, is_active),
    INDEX idx_time_range (start_time, end_time),
    INDEX idx_stock (stock_quantity, sold_quantity)
);

-- Insert sample promotions
INSERT INTO promotions (name, description, code, type, discount_type, discount_value, min_order_amount, usage_limit, start_date, end_date) VALUES
('Welcome Discount', 'Get 10% off on your first order', 'WELCOME10', 'coupon', 'percentage', 10.00, 50000, 1000, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
('Free Shipping', 'Free shipping for orders above Rp 100,000', 'FREESHIP', 'voucher', 'free_shipping', 0, 100000, 0, NOW(), DATE_ADD(NOW(), INTERVAL 60 DAY)),
('Flash Sale 50%', 'Limited time 50% discount', 'FLASH50', 'flash_sale', 'percentage', 50.00, 0, 100, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY)),
('Buy 2 Get 1', 'Buy 2 items get 1 free', 'BUY2GET1', 'coupon', 'buy_x_get_y', 0, 0, 500, NOW(), DATE_ADD(NOW(), INTERVAL 14 DAY));

SELECT 'Shipping and Promotions system tables created successfully!' as status;
