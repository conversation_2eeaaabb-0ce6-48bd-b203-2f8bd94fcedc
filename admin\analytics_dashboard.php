<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';
require_once '../includes/AnalyticsManager.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$analyticsManager = new AnalyticsManager();

// Get date range from request
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // Today

// Get dashboard data
$dashboard_data = $analyticsManager->getDashboardOverview($date_from, $date_to);
$conversion_funnel = $analyticsManager->getConversionFunnel($date_from, $date_to);
$revenue_forecast = $analyticsManager->getRevenueForecast(3);

$page_title = 'Analytics Dashboard';
require_once 'includes/admin_header.php';
?>

<style>
.analytics-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease;
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.metric-card {
    text-align: center;
    padding: 2rem 1rem;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #7f8c8d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.metric-change {
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.metric-change.positive {
    color: #27ae60;
}

.metric-change.negative {
    color: #e74c3c;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.funnel-step {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    padding: 1rem;
    margin: 0.5rem 0;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.funnel-step::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: rgba(255,255,255,0.1);
    transition: width 0.3s ease;
}

.funnel-percentage {
    position: relative;
    z-index: 2;
}

.top-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #ecf0f1;
}

.top-item:last-child {
    border-bottom: none;
}

.top-item img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
    margin-right: 1rem;
}

.date-range-picker {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>

<!-- Date Range Picker -->
<div class="date-range-picker">
    <form method="GET" class="row align-items-end">
        <div class="col-md-3">
            <label class="form-label">From Date</label>
            <input type="date" class="form-control" name="date_from" value="<?php echo $date_from; ?>">
        </div>
        <div class="col-md-3">
            <label class="form-label">To Date</label>
            <input type="date" class="form-control" name="date_to" value="<?php echo $date_to; ?>">
        </div>
        <div class="col-md-3">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search me-1"></i>Update Report
            </button>
        </div>
        <div class="col-md-3 text-end">
            <div class="btn-group">
                <a href="?date_from=<?php echo date('Y-m-d'); ?>&date_to=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-secondary btn-sm">Today</a>
                <a href="?date_from=<?php echo date('Y-m-01'); ?>&date_to=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-secondary btn-sm">This Month</a>
                <a href="?date_from=<?php echo date('Y-01-01'); ?>&date_to=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-secondary btn-sm">This Year</a>
            </div>
        </div>
    </form>
</div>

<!-- Key Metrics -->
<div class="row">
    <div class="col-md-3">
        <div class="analytics-card metric-card">
            <div class="metric-value">Rp <?php echo number_format($dashboard_data['sales']['current']['total_revenue'] ?? 0); ?></div>
            <div class="metric-label">Total Revenue</div>
            <?php if (isset($dashboard_data['sales']['growth']['revenue'])): ?>
            <div class="metric-change <?php echo $dashboard_data['sales']['growth']['revenue'] >= 0 ? 'positive' : 'negative'; ?>">
                <i class="fas fa-arrow-<?php echo $dashboard_data['sales']['growth']['revenue'] >= 0 ? 'up' : 'down'; ?>"></i>
                <?php echo abs($dashboard_data['sales']['growth']['revenue']); ?>% vs last period
            </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="col-md-3">
        <div class="analytics-card metric-card">
            <div class="metric-value"><?php echo number_format($dashboard_data['sales']['current']['total_orders'] ?? 0); ?></div>
            <div class="metric-label">Total Orders</div>
            <?php if (isset($dashboard_data['sales']['growth']['orders'])): ?>
            <div class="metric-change <?php echo $dashboard_data['sales']['growth']['orders'] >= 0 ? 'positive' : 'negative'; ?>">
                <i class="fas fa-arrow-<?php echo $dashboard_data['sales']['growth']['orders'] >= 0 ? 'up' : 'down'; ?>"></i>
                <?php echo abs($dashboard_data['sales']['growth']['orders']); ?>% vs last period
            </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="col-md-3">
        <div class="analytics-card metric-card">
            <div class="metric-value">Rp <?php echo number_format($dashboard_data['sales']['current']['average_order_value'] ?? 0); ?></div>
            <div class="metric-label">Average Order Value</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="analytics-card metric-card">
            <div class="metric-value"><?php echo number_format($dashboard_data['customers']['new_customers'] ?? 0); ?></div>
            <div class="metric-label">New Customers</div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <!-- Sales Trend Chart -->
    <div class="col-md-8">
        <div class="analytics-card">
            <h5><i class="fas fa-chart-line me-2"></i>Sales Trend</h5>
            <div class="chart-container">
                <canvas id="salesTrendChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Order Status Breakdown -->
    <div class="col-md-4">
        <div class="analytics-card">
            <h5><i class="fas fa-chart-pie me-2"></i>Order Status</h5>
            <div class="chart-container">
                <canvas id="orderStatusChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Conversion Funnel -->
<div class="row">
    <div class="col-md-6">
        <div class="analytics-card">
            <h5><i class="fas fa-funnel-dollar me-2"></i>Conversion Funnel</h5>
            <div class="funnel-step" style="width: 100%;">
                <div class="d-flex justify-content-between funnel-percentage">
                    <span>Visitors</span>
                    <span><?php echo number_format($conversion_funnel['visitors'] ?? 0); ?></span>
                </div>
            </div>
            <div class="funnel-step" style="width: 85%;">
                <div class="d-flex justify-content-between funnel-percentage">
                    <span>Product Viewers</span>
                    <span><?php echo number_format($conversion_funnel['product_viewers'] ?? 0); ?> (<?php echo $conversion_funnel['conversion_rates']['visitor_to_product'] ?? 0; ?>%)</span>
                </div>
            </div>
            <div class="funnel-step" style="width: 60%;">
                <div class="d-flex justify-content-between funnel-percentage">
                    <span>Added to Cart</span>
                    <span><?php echo number_format($conversion_funnel['cart_users'] ?? 0); ?> (<?php echo $conversion_funnel['conversion_rates']['product_to_cart'] ?? 0; ?>%)</span>
                </div>
            </div>
            <div class="funnel-step" style="width: 35%;">
                <div class="d-flex justify-content-between funnel-percentage">
                    <span>Purchased</span>
                    <span><?php echo number_format($conversion_funnel['buyers'] ?? 0); ?> (<?php echo $conversion_funnel['conversion_rates']['cart_to_purchase'] ?? 0; ?>%)</span>
                </div>
            </div>
            <div class="mt-3 text-center">
                <strong>Overall Conversion Rate: <?php echo $conversion_funnel['conversion_rates']['overall'] ?? 0; ?>%</strong>
            </div>
        </div>
    </div>
    
    <!-- Top Products -->
    <div class="col-md-6">
        <div class="analytics-card">
            <h5><i class="fas fa-trophy me-2"></i>Best Selling Products</h5>
            <?php if (!empty($dashboard_data['products']['best_selling'])): ?>
                <?php foreach (array_slice($dashboard_data['products']['best_selling'], 0, 5) as $product): ?>
                <div class="top-item">
                    <img src="../<?php echo $product['image'] ? 'uploads/' . $product['image'] : 'assets/img/product-default.jpg'; ?>" alt="Product">
                    <div class="flex-grow-1">
                        <div class="fw-bold"><?php echo htmlspecialchars($product['name']); ?></div>
                        <small class="text-muted"><?php echo $product['total_sold']; ?> sold • Rp <?php echo number_format($product['total_revenue']); ?></small>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="text-muted">No sales data available for this period.</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Additional Analytics -->
<div class="row">
    <!-- Traffic Sources -->
    <div class="col-md-4">
        <div class="analytics-card">
            <h5><i class="fas fa-globe me-2"></i>Traffic Sources</h5>
            <?php if (!empty($dashboard_data['traffic']['traffic_sources'])): ?>
                <?php foreach ($dashboard_data['traffic']['traffic_sources'] as $source): ?>
                <div class="d-flex justify-content-between py-2 border-bottom">
                    <span><?php echo $source['referrer_domain'] ?: 'Direct'; ?></span>
                    <span class="fw-bold"><?php echo $source['visits']; ?></span>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="text-muted">No traffic data available.</p>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Device Breakdown -->
    <div class="col-md-4">
        <div class="analytics-card">
            <h5><i class="fas fa-devices me-2"></i>Device Types</h5>
            <div class="chart-container" style="height: 200px;">
                <canvas id="deviceChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Top Customers -->
    <div class="col-md-4">
        <div class="analytics-card">
            <h5><i class="fas fa-users me-2"></i>Top Customers</h5>
            <?php if (!empty($dashboard_data['customers']['top_customers'])): ?>
                <?php foreach (array_slice($dashboard_data['customers']['top_customers'], 0, 5) as $customer): ?>
                <div class="top-item">
                    <div class="flex-grow-1">
                        <div class="fw-bold"><?php echo htmlspecialchars($customer['name']); ?></div>
                        <small class="text-muted"><?php echo $customer['total_orders']; ?> orders • Rp <?php echo number_format($customer['total_spent']); ?></small>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="text-muted">No customer data available.</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Sales Trend Chart
const salesTrendCtx = document.getElementById('salesTrendChart').getContext('2d');
const salesTrendChart = new Chart(salesTrendCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($dashboard_data['orders']['daily_trends'] ?? [], 'order_date')); ?>,
        datasets: [{
            label: 'Daily Revenue',
            data: <?php echo json_encode(array_column($dashboard_data['orders']['daily_trends'] ?? [], 'daily_revenue')); ?>,
            borderColor: '#3498db',
            backgroundColor: 'rgba(52, 152, 219, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Daily Orders',
            data: <?php echo json_encode(array_column($dashboard_data['orders']['daily_trends'] ?? [], 'order_count')); ?>,
            borderColor: '#e74c3c',
            backgroundColor: 'rgba(231, 76, 60, 0.1)',
            tension: 0.4,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});

// Order Status Chart
const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
const orderStatusChart = new Chart(orderStatusCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode(array_column($dashboard_data['orders']['status_breakdown'] ?? [], 'status')); ?>,
        datasets: [{
            data: <?php echo json_encode(array_column($dashboard_data['orders']['status_breakdown'] ?? [], 'count')); ?>,
            backgroundColor: ['#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Device Chart
const deviceCtx = document.getElementById('deviceChart').getContext('2d');
const deviceChart = new Chart(deviceCtx, {
    type: 'pie',
    data: {
        labels: <?php echo json_encode(array_column($dashboard_data['traffic']['device_breakdown'] ?? [], 'device_type')); ?>,
        datasets: [{
            data: <?php echo json_encode(array_column($dashboard_data['traffic']['device_breakdown'] ?? [], 'sessions')); ?>,
            backgroundColor: ['#3498db', '#2ecc71', '#f39c12']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>

<?php require_once 'includes/admin_footer.php'; ?>
