<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Add to Cart</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>Test AJAX Add to Cart</h1>
    
    <div class="test-section">
        <h2>Test 1: Simple Add to Cart</h2>
        <p>Product ID: 29, Quantity: 1</p>
        <button onclick="testAddToCart(29, 1, 'result1')">Test Add to Cart</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Add Multiple Quantity</h2>
        <p>Product ID: 29, Quantity: 3</p>
        <button onclick="testAddToCart(29, 3, 'result2')">Test Add Multiple</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Invalid Product</h2>
        <p>Product ID: 99999, Quantity: 1</p>
        <button onclick="testAddToCart(99999, 1, 'result3')">Test Invalid Product</button>
        <div id="result3" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Console Log</h2>
        <div id="console-log" style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
    </div>

    <script>
        function log(message) {
            const consoleLog = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            consoleLog.textContent += `[${timestamp}] ${message}\n`;
            consoleLog.scrollTop = consoleLog.scrollHeight;
            console.log(message);
        }
        
        function testAddToCart(productId, quantity, resultId) {
            log(`Testing add to cart: Product ${productId}, Quantity ${quantity}`);
            
            const resultDiv = document.getElementById(resultId);
            resultDiv.innerHTML = '<div class="info">Testing...</div>';
            
            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('quantity', quantity);
            
            fetch('ajax/simple_add_to_cart.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                log(`Response status: ${response.status}`);
                return response.text();
            })
            .then(text => {
                log(`Raw response: ${text}`);
                try {
                    const data = JSON.parse(text);
                    log(`Parsed JSON: ${JSON.stringify(data)}`);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `<div class="success">✅ Success: ${data.message}</div>`;
                        if (data.cart_count) {
                            resultDiv.innerHTML += `<div class="info">Cart count: ${data.cart_count}</div>`;
                        }
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                    }
                } catch (e) {
                    log(`JSON parse error: ${e.message}`);
                    resultDiv.innerHTML = `<div class="error">❌ Invalid JSON response: ${text}</div>`;
                }
            })
            .catch(error => {
                log(`Fetch error: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            });
        }
        
        log('Test page loaded');
    </script>
</body>
</html>
