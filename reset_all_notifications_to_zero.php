<?php
/**
 * Reset All Notifications to Zero
 * This script will completely reset all notification counts to 0
 */

session_start();
require_once 'includes/db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$action = $_GET['action'] ?? 'show';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset All Notifications - TeWuNeed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .reset-card { border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .btn-reset { border-radius: 25px; padding: 12px 30px; font-weight: 600; }
        .counter { font-size: 3rem; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card reset-card">
                    <div class="card-header bg-danger text-white text-center">
                        <h3 class="mb-0">
                            <i class="fas fa-power-off me-2"></i>
                            Reset All Notifications to Zero
                        </h3>
                    </div>
                    <div class="card-body p-5">
                        
                        <?php if ($action === 'show'): ?>
                        
                        <!-- Current Status -->
                        <div class="text-center mb-4">
                            <h4 class="text-muted mb-4">Current Notification Status</h4>
                            
                            <?php
                            // Get current counts
                            $total_notifications = 0;
                            $unread_notifications = 0;
                            
                            try {
                                // Count from order_notifications
                                $stmt = $conn->prepare("SELECT COUNT(*) as total, SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread FROM order_notifications WHERE user_id = ?");
                                $stmt->execute([$user_id]);
                                $order_stats = $stmt->fetch(PDO::FETCH_ASSOC);
                                
                                $total_notifications += $order_stats['total'] ?? 0;
                                $unread_notifications += $order_stats['unread'] ?? 0;
                                
                                // Count from notifications table
                                $stmt = $conn->prepare("SELECT COUNT(*) as total, SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread FROM notifications WHERE user_id = ?");
                                $stmt->execute([$user_id]);
                                $general_stats = $stmt->fetch(PDO::FETCH_ASSOC);
                                
                                $total_notifications += $general_stats['total'] ?? 0;
                                $unread_notifications += $general_stats['unread'] ?? 0;
                                
                            } catch (Exception $e) {
                                echo '<div class="alert alert-warning">Error checking notifications: ' . $e->getMessage() . '</div>';
                            }
                            ?>
                            
                            <div class="row">
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <div class="counter text-primary"><?php echo $total_notifications; ?></div>
                                            <h6 class="text-muted">Total Notifications</h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <div class="counter text-danger"><?php echo $unread_notifications; ?></div>
                                            <h6 class="text-muted">Unread Notifications</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Reset Options -->
                        <div class="text-center">
                            <h5 class="mb-4">Choose Reset Method:</h5>
                            
                            <div class="d-grid gap-3">
                                <a href="?action=mark_all_read" class="btn btn-warning btn-reset btn-lg">
                                    <i class="fas fa-check-double me-2"></i>
                                    Mark All as Read (Keep History)
                                </a>
                                
                                <a href="?action=delete_all" class="btn btn-danger btn-reset btn-lg" 
                                   onclick="return confirm('⚠️ This will PERMANENTLY DELETE all notifications. Are you sure?')">
                                    <i class="fas fa-trash-alt me-2"></i>
                                    Delete All Notifications (Permanent)
                                </a>
                                
                                <a href="?action=complete_reset" class="btn btn-dark btn-reset btn-lg"
                                   onclick="return confirm('🔥 This will do a COMPLETE RESET of everything. Continue?')">
                                    <i class="fas fa-power-off me-2"></i>
                                    Complete System Reset
                                </a>
                            </div>
                        </div>
                        
                        <?php elseif ($action === 'mark_all_read'): ?>
                        
                        <!-- Mark All as Read -->
                        <?php
                        $success = true;
                        $affected_rows = 0;
                        
                        try {
                            // Mark order_notifications as read
                            $stmt = $conn->prepare("UPDATE order_notifications SET is_read = TRUE WHERE user_id = ?");
                            $stmt->execute([$user_id]);
                            $affected_rows += $stmt->rowCount();
                            
                            // Mark notifications as read
                            $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE user_id = ?");
                            $stmt->execute([$user_id]);
                            $affected_rows += $stmt->rowCount();
                            
                        } catch (Exception $e) {
                            $success = false;
                            $error_message = $e->getMessage();
                        }
                        ?>
                        
                        <div class="text-center">
                            <?php if ($success): ?>
                                <div class="alert alert-success">
                                    <h4><i class="fas fa-check-circle me-2"></i>Success!</h4>
                                    <p>Marked <strong><?php echo $affected_rows; ?></strong> notifications as read.</p>
                                </div>
                                <div class="counter text-success mb-3">0</div>
                                <h5 class="text-success">All notifications are now read!</h5>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h4><i class="fas fa-exclamation-circle me-2"></i>Error!</h4>
                                    <p><?php echo $error_message; ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php elseif ($action === 'delete_all'): ?>
                        
                        <!-- Delete All -->
                        <?php
                        $success = true;
                        $deleted_rows = 0;
                        
                        try {
                            // Delete from order_notifications
                            $stmt = $conn->prepare("DELETE FROM order_notifications WHERE user_id = ?");
                            $stmt->execute([$user_id]);
                            $deleted_rows += $stmt->rowCount();
                            
                            // Delete from notifications
                            $stmt = $conn->prepare("DELETE FROM notifications WHERE user_id = ?");
                            $stmt->execute([$user_id]);
                            $deleted_rows += $stmt->rowCount();
                            
                        } catch (Exception $e) {
                            $success = false;
                            $error_message = $e->getMessage();
                        }
                        ?>
                        
                        <div class="text-center">
                            <?php if ($success): ?>
                                <div class="alert alert-success">
                                    <h4><i class="fas fa-trash-alt me-2"></i>Deleted!</h4>
                                    <p>Permanently deleted <strong><?php echo $deleted_rows; ?></strong> notifications.</p>
                                </div>
                                <div class="counter text-success mb-3">0</div>
                                <h5 class="text-success">All notifications have been removed!</h5>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h4><i class="fas fa-exclamation-circle me-2"></i>Error!</h4>
                                    <p><?php echo $error_message; ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php elseif ($action === 'complete_reset'): ?>
                        
                        <!-- Complete Reset -->
                        <?php
                        $success = true;
                        $total_affected = 0;
                        $operations = [];
                        
                        try {
                            // 1. Delete all notifications
                            $stmt = $conn->prepare("DELETE FROM order_notifications WHERE user_id = ?");
                            $stmt->execute([$user_id]);
                            $deleted1 = $stmt->rowCount();
                            $total_affected += $deleted1;
                            $operations[] = "Deleted {$deleted1} order notifications";
                            
                            $stmt = $conn->prepare("DELETE FROM notifications WHERE user_id = ?");
                            $stmt->execute([$user_id]);
                            $deleted2 = $stmt->rowCount();
                            $total_affected += $deleted2;
                            $operations[] = "Deleted {$deleted2} general notifications";
                            
                            // 2. Clear session notifications
                            $session_keys = ['error', 'error_message', 'success_message', 'notification', 'flash_message'];
                            $cleared_sessions = 0;
                            foreach ($session_keys as $key) {
                                if (isset($_SESSION[$key])) {
                                    unset($_SESSION[$key]);
                                    $cleared_sessions++;
                                }
                            }
                            $operations[] = "Cleared {$cleared_sessions} session notifications";
                            
                            // 3. Clear cookies
                            $cookie_keys = ['notification', 'error_message', 'success_message'];
                            $cleared_cookies = 0;
                            foreach ($cookie_keys as $key) {
                                if (isset($_COOKIE[$key])) {
                                    setcookie($key, '', time() - 3600, '/');
                                    $cleared_cookies++;
                                }
                            }
                            $operations[] = "Cleared {$cleared_cookies} notification cookies";
                            
                        } catch (Exception $e) {
                            $success = false;
                            $error_message = $e->getMessage();
                        }
                        ?>
                        
                        <div class="text-center">
                            <?php if ($success): ?>
                                <div class="alert alert-success">
                                    <h4><i class="fas fa-power-off me-2"></i>Complete Reset Done!</h4>
                                    <ul class="list-unstyled">
                                        <?php foreach ($operations as $op): ?>
                                            <li><i class="fas fa-check text-success me-2"></i><?php echo $op; ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                                <div class="counter text-success mb-3">0</div>
                                <h5 class="text-success">System completely reset!</h5>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h4><i class="fas fa-exclamation-circle me-2"></i>Error!</h4>
                                    <p><?php echo $error_message; ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php endif; ?>
                        
                        <!-- Navigation -->
                        <div class="text-center mt-4">
                            <a href="user_notifications.php" class="btn btn-primary me-2">
                                <i class="fas fa-bell me-1"></i>View Notifications
                            </a>
                            <a href="index.php" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                            <?php if ($action !== 'show'): ?>
                            <a href="reset_all_notifications_to_zero.php" class="btn btn-outline-info">
                                <i class="fas fa-redo me-1"></i>Reset Again
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-update header badge
        <?php if ($action !== 'show'): ?>
        setTimeout(function() {
            // Hide header notification badge
            const headerBadge = document.getElementById('headerNotificationBadge');
            if (headerBadge) {
                headerBadge.style.display = 'none';
            }
            
            // Update any notification counters
            const counters = document.querySelectorAll('.notification-count, .badge');
            counters.forEach(counter => {
                if (counter.textContent.match(/^\d+$/)) {
                    counter.textContent = '0';
                    counter.style.display = 'none';
                }
            });
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
