<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$page = 'notifications';
$page_title = 'Notifikasi';

require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

$user_id = $_SESSION['user_id'];

// Handle mark as read
if (isset($_POST['mark_read']) && isset($_POST['notification_id'])) {
    $notification_id = intval($_POST['notification_id']);
    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1, read_at = NOW() WHERE notification_id = ? AND user_id = ?");
    $stmt->execute([$notification_id, $user_id]);
}

// Handle mark all as read
if (isset($_POST['mark_all_read'])) {
    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1, read_at = NOW() WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$user_id]);
}

// Get filter
$filter = $_GET['filter'] ?? 'all';
$whereClause = "WHERE user_id = ?";
$params = [$user_id];

if ($filter !== 'all') {
    if ($filter === 'unread') {
        $whereClause .= " AND is_read = 0";
    } else {
        $whereClause .= " AND type = ?";
        $params[] = $filter;
    }
}

// Get notifications
$stmt = $conn->prepare("
    SELECT * FROM notifications 
    $whereClause 
    ORDER BY created_at DESC 
    LIMIT 50
");
$stmt->execute($params);
$notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get notification counts
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
        SUM(CASE WHEN type = 'payment' THEN 1 ELSE 0 END) as payment,
        SUM(CASE WHEN type = 'order' THEN 1 ELSE 0 END) as order_count,
        SUM(CASE WHEN type = 'cart' THEN 1 ELSE 0 END) as cart,
        SUM(CASE WHEN type = 'system' THEN 1 ELSE 0 END) as system,
        SUM(CASE WHEN type = 'promotion' THEN 1 ELSE 0 END) as promotion
    FROM notifications 
    WHERE user_id = ?
");
$stmt->execute([$user_id]);
$counts = $stmt->fetch(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<style>
.notifications-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.notification-filters {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid #e2e8f0;
    background: white;
    color: #64748b;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.filter-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    text-decoration: none;
}

.filter-btn.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.filter-btn .badge {
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
}

.notification-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #e2e8f0;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.notification-item.unread {
    border-left-color: #3b82f6;
    background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
}

.notification-item.payment {
    border-left-color: #10b981;
}

.notification-item.order {
    border-left-color: #f59e0b;
}

.notification-item.cart {
    border-left-color: #8b5cf6;
}

.notification-item.system {
    border-left-color: #6b7280;
}

.notification-item.promotion {
    border-left-color: #ef4444;
}

.notification-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.notification-icon.payment {
    background: #dcfce7;
    color: #16a34a;
}

.notification-icon.order {
    background: #fef3c7;
    color: #d97706;
}

.notification-icon.cart {
    background: #ede9fe;
    color: #7c3aed;
}

.notification-icon.system {
    background: #f3f4f6;
    color: #4b5563;
}

.notification-icon.promotion {
    background: #fee2e2;
    color: #dc2626;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
}

.notification-message {
    color: #6b7280;
    line-height: 1.5;
    margin-bottom: 10px;
}

.notification-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: #9ca3af;
}

.notification-actions {
    display: flex;
    gap: 10px;
}

.btn-mark-read {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 5px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-mark-read:hover {
    background: #2563eb;
}

.priority-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.priority-low {
    background: #f3f4f6;
    color: #6b7280;
}

.priority-medium {
    background: #fef3c7;
    color: #d97706;
}

.priority-high {
    background: #fee2e2;
    color: #dc2626;
}

.priority-urgent {
    background: #7f1d1d;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #d1d5db;
}

@media (max-width: 768px) {
    .filter-buttons {
        justify-content: center;
    }
    
    .notification-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .notification-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
</style>

<div class="notifications-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2 mb-0">
            <i class="fas fa-bell me-2"></i>Notifikasi
            <?php if ($counts['unread'] > 0): ?>
                <span class="badge bg-danger"><?php echo $counts['unread']; ?></span>
            <?php endif; ?>
        </h1>
        
        <?php if ($counts['unread'] > 0): ?>
            <form method="POST" style="display: inline;">
                <button type="submit" name="mark_all_read" class="btn btn-outline-primary">
                    <i class="fas fa-check-double me-1"></i>Tandai Semua Dibaca
                </button>
            </form>
        <?php endif; ?>
    </div>

    <!-- Filter Buttons -->
    <div class="notification-filters">
        <div class="filter-buttons">
            <a href="?filter=all" class="filter-btn <?php echo $filter === 'all' ? 'active' : ''; ?>">
                <i class="fas fa-list me-1"></i>Semua
                <span class="badge"><?php echo $counts['total']; ?></span>
            </a>
            <a href="?filter=unread" class="filter-btn <?php echo $filter === 'unread' ? 'active' : ''; ?>">
                <i class="fas fa-envelope me-1"></i>Belum Dibaca
                <?php if ($counts['unread'] > 0): ?>
                    <span class="badge"><?php echo $counts['unread']; ?></span>
                <?php endif; ?>
            </a>
            <a href="?filter=payment" class="filter-btn <?php echo $filter === 'payment' ? 'active' : ''; ?>">
                <i class="fas fa-credit-card me-1"></i>Pembayaran
                <?php if ($counts['payment'] > 0): ?>
                    <span class="badge"><?php echo $counts['payment']; ?></span>
                <?php endif; ?>
            </a>
            <a href="?filter=order" class="filter-btn <?php echo $filter === 'order' ? 'active' : ''; ?>">
                <i class="fas fa-shopping-bag me-1"></i>Pesanan
                <?php if ($counts['order_count'] > 0): ?>
                    <span class="badge"><?php echo $counts['order_count']; ?></span>
                <?php endif; ?>
            </a>
            <a href="?filter=cart" class="filter-btn <?php echo $filter === 'cart' ? 'active' : ''; ?>">
                <i class="fas fa-shopping-cart me-1"></i>Keranjang
                <?php if ($counts['cart'] > 0): ?>
                    <span class="badge"><?php echo $counts['cart']; ?></span>
                <?php endif; ?>
            </a>
            <a href="?filter=promotion" class="filter-btn <?php echo $filter === 'promotion' ? 'active' : ''; ?>">
                <i class="fas fa-tags me-1"></i>Promosi
                <?php if ($counts['promotion'] > 0): ?>
                    <span class="badge"><?php echo $counts['promotion']; ?></span>
                <?php endif; ?>
            </a>
        </div>
    </div>

    <!-- Notifications List -->
    <?php if (!empty($notifications)): ?>
        <?php foreach ($notifications as $notification): ?>
            <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?> <?php echo $notification['type']; ?>">
                <div class="notification-header">
                    <div class="notification-icon <?php echo $notification['type']; ?>">
                        <?php
                        $icons = [
                            'payment' => 'fas fa-credit-card',
                            'order' => 'fas fa-shopping-bag',
                            'cart' => 'fas fa-shopping-cart',
                            'system' => 'fas fa-cog',
                            'promotion' => 'fas fa-tags'
                        ];
                        $icon = $icons[$notification['type']] ?? 'fas fa-bell';
                        ?>
                        <i class="<?php echo $icon; ?>"></i>
                    </div>
                    
                    <div class="notification-content">
                        <div class="notification-title">
                            <?php echo htmlspecialchars($notification['title']); ?>
                            <?php if (!$notification['is_read']): ?>
                                <span class="badge bg-primary ms-2">Baru</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="notification-message">
                            <?php echo htmlspecialchars($notification['message']); ?>
                        </div>
                        
                        <div class="notification-meta">
                            <div>
                                <i class="fas fa-clock me-1"></i>
                                <?php echo date('d M Y, H:i', strtotime($notification['created_at'])); ?>
                                <span class="priority-badge priority-<?php echo $notification['priority']; ?>">
                                    <?php echo ucfirst($notification['priority']); ?>
                                </span>
                            </div>
                            
                            <?php if (!$notification['is_read']): ?>
                                <div class="notification-actions">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="notification_id" value="<?php echo $notification['notification_id']; ?>">
                                        <button type="submit" name="mark_read" class="btn-mark-read">
                                            <i class="fas fa-check me-1"></i>Tandai Dibaca
                                        </button>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="empty-state">
            <i class="fas fa-bell-slash"></i>
            <h4>Tidak Ada Notifikasi</h4>
            <p>
                <?php if ($filter === 'unread'): ?>
                    Semua notifikasi sudah dibaca.
                <?php elseif ($filter !== 'all'): ?>
                    Tidak ada notifikasi untuk kategori ini.
                <?php else: ?>
                    Anda belum memiliki notifikasi.
                <?php endif; ?>
            </p>
            <a href="products_public.php" class="btn btn-primary">
                <i class="fas fa-shopping-bag me-2"></i>Mulai Berbelanja
            </a>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
