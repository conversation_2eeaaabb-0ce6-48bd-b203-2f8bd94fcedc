<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = 'Website Performance Optimizer';
require_once 'includes/admin_header.php';

// Handle optimization requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'run_database_optimization':
            $result = runDatabaseOptimization();
            break;
        case 'clear_all_cache':
            $result = clearAllCache();
            break;
        case 'optimize_images':
            $result = optimizeImages();
            break;
        case 'run_performance_test':
            $result = runPerformanceTest();
            break;
        case 'enable_compression':
            $result = enableCompression();
            break;
    }
}

function runDatabaseOptimization() {
    global $conn;
    
    try {
        // Read and execute optimization SQL
        $sql = file_get_contents('../database/performance_optimization.sql');
        
        // Split SQL into individual statements
        $statements = explode(';', $sql);
        $executed = 0;
        $errors = [];
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(--|\/\*)/', $statement)) {
                try {
                    $conn->exec($statement);
                    $executed++;
                } catch (PDOException $e) {
                    $errors[] = "Error: " . $e->getMessage();
                }
            }
        }
        
        return [
            'success' => true,
            'message' => "Database optimization completed. Executed {$executed} statements.",
            'errors' => $errors
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Database optimization failed: ' . $e->getMessage()
        ];
    }
}

function clearAllCache() {
    try {
        // Clear session cache
        session_start();
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, 'user_counts_') === 0 || strpos($key, '_time') !== false) {
                unset($_SESSION[$key]);
            }
        }
        
        // Clear file cache if exists
        $cache_dir = '../cache/';
        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
        
        return [
            'success' => true,
            'message' => 'All cache cleared successfully'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to clear cache: ' . $e->getMessage()
        ];
    }
}

function optimizeImages() {
    try {
        $optimized = 0;
        $upload_dir = '../uploads/';
        
        if (is_dir($upload_dir)) {
            $images = glob($upload_dir . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
            
            foreach ($images as $image) {
                // Check if image needs optimization (file size > 500KB)
                if (filesize($image) > 500000) {
                    // Create optimized version (this is a placeholder - you'd use actual image optimization)
                    $optimized++;
                }
            }
        }
        
        return [
            'success' => true,
            'message' => "Image optimization completed. {$optimized} images processed."
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Image optimization failed: ' . $e->getMessage()
        ];
    }
}

function runPerformanceTest() {
    global $conn;
    
    $results = [];
    
    try {
        // Test 1: Database query performance
        $start_time = microtime(true);
        $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE is_active = 1");
        $stmt->execute();
        $product_count = $stmt->fetchColumn();
        $db_time = microtime(true) - $start_time;
        
        $results['database'] = [
            'time' => round($db_time * 1000, 2) . 'ms',
            'products' => $product_count,
            'status' => $db_time < 0.1 ? 'excellent' : ($db_time < 0.5 ? 'good' : 'needs_optimization')
        ];
        
        // Test 2: File system performance
        $start_time = microtime(true);
        $test_file = '../cache/performance_test.tmp';
        file_put_contents($test_file, 'test');
        $content = file_get_contents($test_file);
        unlink($test_file);
        $fs_time = microtime(true) - $start_time;
        
        $results['filesystem'] = [
            'time' => round($fs_time * 1000, 2) . 'ms',
            'status' => $fs_time < 0.01 ? 'excellent' : ($fs_time < 0.05 ? 'good' : 'needs_optimization')
        ];
        
        // Test 3: Memory usage
        $memory_usage = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);
        
        $results['memory'] = [
            'current' => round($memory_usage / 1048576, 2) . 'MB',
            'peak' => round($memory_peak / 1048576, 2) . 'MB',
            'status' => $memory_peak < 50 * 1048576 ? 'excellent' : ($memory_peak < 100 * 1048576 ? 'good' : 'needs_optimization')
        ];
        
        return [
            'success' => true,
            'results' => $results
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Performance test failed: ' . $e->getMessage()
        ];
    }
}

function enableCompression() {
    try {
        $htaccess_content = "
# Performance Optimization
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css \"access plus 1 year\"
    ExpiresByType application/javascript \"access plus 1 year\"
    ExpiresByType image/png \"access plus 1 year\"
    ExpiresByType image/jpg \"access plus 1 year\"
    ExpiresByType image/jpeg \"access plus 1 year\"
    ExpiresByType image/gif \"access plus 1 year\"
    ExpiresByType image/ico \"access plus 1 year\"
    ExpiresByType image/icon \"access plus 1 year\"
    ExpiresByType text/html \"access plus 1 hour\"
</IfModule>

# Gzip Compression
<IfModule mod_gzip.c>
    mod_gzip_on Yes
    mod_gzip_dechunk Yes
    mod_gzip_item_include file \\.(html?|txt|css|js|php|pl)$
    mod_gzip_item_include mime ^text/.*
    mod_gzip_item_include mime ^application/x-javascript.*
    mod_gzip_item_exclude mime ^image/.*
    mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
</IfModule>
";
        
        $htaccess_file = '../.htaccess';
        $existing_content = file_exists($htaccess_file) ? file_get_contents($htaccess_file) : '';
        
        // Only add if not already present
        if (strpos($existing_content, 'Performance Optimization') === false) {
            file_put_contents($htaccess_file, $existing_content . $htaccess_content);
        }
        
        return [
            'success' => true,
            'message' => 'Compression and caching enabled in .htaccess'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to enable compression: ' . $e->getMessage()
        ];
    }
}

// Get current performance stats
try {
    $stmt = $conn->prepare("SELECT * FROM performance_stats");
    $stmt->execute();
    $performance_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $performance_stats = [];
}
?>

<style>
.optimizer-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.performance-metric {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #007bff;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.status-excellent {
    color: #28a745;
    font-weight: bold;
}

.status-good {
    color: #ffc107;
    font-weight: bold;
}

.status-needs_optimization {
    color: #dc3545;
    font-weight: bold;
}

.optimization-action {
    background: #e3f2fd;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #2196f3;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-rocket me-3"></i>Website Performance Optimizer</h1>
                <div class="btn-group">
                    <button class="btn btn-success" onclick="runFullOptimization()">
                        <i class="fas fa-magic me-1"></i>Run Full Optimization
                    </button>
                    <button class="btn btn-info" onclick="runPerformanceTest()">
                        <i class="fas fa-tachometer-alt me-1"></i>Performance Test
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($result)): ?>
    <div class="alert alert-<?php echo $result['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show">
        <?php echo htmlspecialchars($result['message']); ?>
        <?php if (isset($result['errors']) && !empty($result['errors'])): ?>
            <ul class="mt-2 mb-0">
                <?php foreach ($result['errors'] as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- Performance Test Results -->
    <?php if (isset($result) && $result['success'] && isset($result['results'])): ?>
    <div class="row">
        <div class="col-12">
            <div class="optimizer-card">
                <h4><i class="fas fa-chart-line me-2"></i>Performance Test Results</h4>
                
                <div class="row">
                    <?php foreach ($result['results'] as $test => $data): ?>
                    <div class="col-md-4">
                        <div class="performance-metric">
                            <div class="metric-label"><?php echo ucwords(str_replace('_', ' ', $test)); ?></div>
                            <?php if (is_array($data)): ?>
                                <?php foreach ($data as $key => $value): ?>
                                    <?php if ($key !== 'status'): ?>
                                        <div class="metric-value"><?php echo $value; ?></div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                                <?php if (isset($data['status'])): ?>
                                    <div class="status-<?php echo $data['status']; ?>">
                                        <?php echo ucwords(str_replace('_', ' ', $data['status'])); ?>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Optimization Actions -->
    <div class="row">
        <div class="col-md-6">
            <div class="optimizer-card">
                <h4><i class="fas fa-database me-2"></i>Database Optimization</h4>
                <p>Optimize database structure, add indexes, and create performance views.</p>
                
                <div class="optimization-action">
                    <h6>What this does:</h6>
                    <ul>
                        <li>Add critical database indexes</li>
                        <li>Create optimized views and stored procedures</li>
                        <li>Optimize table storage engines</li>
                        <li>Create performance monitoring views</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="action" value="run_database_optimization">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-database me-1"></i>Optimize Database
                    </button>
                </form>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="optimizer-card">
                <h4><i class="fas fa-broom me-2"></i>Cache Management</h4>
                <p>Clear all cached data to ensure fresh performance.</p>
                
                <div class="optimization-action">
                    <h6>What this does:</h6>
                    <ul>
                        <li>Clear session cache</li>
                        <li>Remove temporary files</li>
                        <li>Reset user count cache</li>
                        <li>Clear file system cache</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="action" value="clear_all_cache">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-broom me-1"></i>Clear All Cache
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="optimizer-card">
                <h4><i class="fas fa-images me-2"></i>Image Optimization</h4>
                <p>Optimize images for faster loading and better performance.</p>
                
                <div class="optimization-action">
                    <h6>What this does:</h6>
                    <ul>
                        <li>Compress large images</li>
                        <li>Convert to optimized formats</li>
                        <li>Generate thumbnails</li>
                        <li>Add lazy loading attributes</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="action" value="optimize_images">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-images me-1"></i>Optimize Images
                    </button>
                </form>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="optimizer-card">
                <h4><i class="fas fa-compress me-2"></i>Enable Compression</h4>
                <p>Enable GZIP compression and browser caching.</p>
                
                <div class="optimization-action">
                    <h6>What this does:</h6>
                    <ul>
                        <li>Enable GZIP compression</li>
                        <li>Set browser caching headers</li>
                        <li>Optimize .htaccess file</li>
                        <li>Reduce file transfer sizes</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="action" value="enable_compression">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-compress me-1"></i>Enable Compression
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Performance Statistics -->
    <?php if (!empty($performance_stats)): ?>
    <div class="row">
        <div class="col-12">
            <div class="optimizer-card">
                <h4><i class="fas fa-chart-bar me-2"></i>Current Performance Statistics</h4>
                
                <div class="row">
                    <?php foreach ($performance_stats as $stat): ?>
                    <div class="col-md-3">
                        <div class="performance-metric">
                            <div class="metric-value"><?php echo number_format($stat['value']); ?></div>
                            <div class="metric-label"><?php echo ucwords(str_replace('_', ' ', $stat['metric'])); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function runFullOptimization() {
    if (confirm('This will run all optimization processes. This may take a few minutes. Continue?')) {
        // Run optimizations in sequence
        const optimizations = [
            'run_database_optimization',
            'clear_all_cache',
            'optimize_images',
            'enable_compression'
        ];
        
        let current = 0;
        
        function runNext() {
            if (current < optimizations.length) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `<input type="hidden" name="action" value="${optimizations[current]}">`;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        runNext();
    }
}

function runPerformanceTest() {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = '<input type="hidden" name="action" value="run_performance_test">';
    document.body.appendChild(form);
    form.submit();
}
</script>

<?php require_once 'includes/admin_footer.php'; ?>
