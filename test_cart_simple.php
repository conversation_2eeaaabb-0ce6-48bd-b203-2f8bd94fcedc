<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Cart Test - TeWuNeed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">🛒 Simple Cart Test</h1>
                
                <!-- Test Status -->
                <div id="test-status" class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Testing cart functionality...
                </div>

                <!-- Manual Test Buttons -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>Manual Tests</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-primary me-2 mb-2" onclick="testCartCount()">
                                    <i class="fas fa-calculator me-1"></i>Test Cart Count
                                </button>
                                <button class="btn btn-success me-2 mb-2" onclick="testAddToCart()">
                                    <i class="fas fa-plus me-1"></i>Test Add to Cart
                                </button>
                                <button class="btn btn-warning me-2 mb-2" onclick="testDatabase()">
                                    <i class="fas fa-database me-1"></i>Test Database
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>Current Cart Count:</h6>
                                <div class="alert alert-light">
                                    <span id="cart-count-display" class="h4">0</span> items
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clipboard-list me-2"></i>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <p class="text-muted">Click buttons above to run tests...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.className = `alert alert-${type}`;
            statusDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'danger' ? 'times' : 'info'}-circle me-2"></i>${message}`;
        }

        function addResult(test, result, details = '') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const icon = result === 'PASS' ? 'check text-success' : 'times text-danger';
            
            if (resultsDiv.innerHTML.includes('Click buttons above')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.innerHTML += `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-${icon} me-2"></i><strong>${test}</strong></span>
                        <small class="text-muted">${timestamp}</small>
                    </div>
                    <div class="text-muted small">${details}</div>
                </div>
            `;
        }

        async function testCartCount() {
            updateStatus('Testing cart count...', 'info');
            
            try {
                const response = await fetch('ajax/get_cart_count.php', {
                    method: 'GET',
                    credentials: 'same-origin'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Cart count response:', data);
                
                if (data.success !== undefined) {
                    document.getElementById('cart-count-display').textContent = data.cart_count || 0;
                    addResult('Cart Count Test', 'PASS', `Cart count: ${data.cart_count || 0}`);
                    updateStatus('Cart count test passed!', 'success');
                } else {
                    // Handle old format
                    const count = data.count || 0;
                    document.getElementById('cart-count-display').textContent = count;
                    addResult('Cart Count Test', 'PASS', `Cart count: ${count}`);
                    updateStatus('Cart count test passed!', 'success');
                }
            } catch (error) {
                addResult('Cart Count Test', 'FAIL', `Error: ${error.message}`);
                updateStatus('Cart count test failed!', 'danger');
                console.error('Cart count test error:', error);
            }
        }

        async function testAddToCart() {
            updateStatus('Testing add to cart...', 'info');
            
            try {
                const formData = new FormData();
                formData.append('product_id', '1');
                formData.append('quantity', '1');

                // Try simple endpoint first
                const response = await fetch('ajax/add_to_cart_simple.php', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Add to cart response:', data);
                
                if (data.success) {
                    addResult('Add to Cart Test', 'PASS', 
                        `Product added successfully. Cart count: ${data.cart_count || 'unknown'}`);
                    updateStatus('Add to cart test passed!', 'success');
                    
                    // Update cart count display
                    if (data.cart_count !== undefined) {
                        document.getElementById('cart-count-display').textContent = data.cart_count;
                    }
                } else {
                    addResult('Add to Cart Test', 'FAIL', 
                        `Error: ${data.message || 'Unknown error'}`);
                    updateStatus('Add to cart test failed!', 'danger');
                }
            } catch (error) {
                addResult('Add to Cart Test', 'FAIL', 
                    `Network error: ${error.message}`);
                updateStatus('Add to cart test failed!', 'danger');
                console.error('Add to cart test error:', error);
            }
        }

        async function testDatabase() {
            updateStatus('Testing database connection...', 'info');
            
            try {
                // Test with a simple PHP script
                const response = await fetch('test_db_connection.php', {
                    method: 'GET',
                    credentials: 'same-origin'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Database test response:', data);
                
                if (data.success) {
                    addResult('Database Test', 'PASS', data.message);
                    updateStatus('Database test passed!', 'success');
                } else {
                    addResult('Database Test', 'FAIL', data.message || 'Database connection failed');
                    updateStatus('Database test failed!', 'danger');
                }
            } catch (error) {
                addResult('Database Test', 'FAIL', `Error: ${error.message}`);
                updateStatus('Database test failed!', 'danger');
                console.error('Database test error:', error);
            }
        }

        // Auto-run cart count test on load
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Simple cart test page loaded. Ready to test!', 'success');
            setTimeout(testCartCount, 1000);
        });
    </script>
</body>
</html>
