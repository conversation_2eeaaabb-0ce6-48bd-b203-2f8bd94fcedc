<?php
session_start();
require_once 'includes/db_connect.php';

echo "<h2>Cart Debug Information</h2>";

// Check session
echo "<h3>Session Information:</h3>";
echo "<pre>";
echo "Session ID: " . session_id() . "\n";
echo "User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "\n";
echo "Firebase User ID: " . ($_SESSION['firebase_user_id'] ?? 'Not set') . "\n";
echo "Local User ID: " . ($_SESSION['local_user_id'] ?? 'Not set') . "\n";
echo "Username: " . ($_SESSION['username'] ?? 'Not set') . "\n";
echo "Email: " . ($_SESSION['email'] ?? 'Not set') . "\n";
echo "</pre>";

// Check if user is logged in
if (!isset($_SESSION['user_id']) && !isset($_SESSION['firebase_user_id'])) {
    echo "<p style='color: red;'>❌ User is NOT logged in!</p>";
    echo "<p><a href='login.php'>Please login first</a></p>";
} else {
    echo "<p style='color: green;'>✅ User is logged in!</p>";
    
    // Test database connection
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM products WHERE product_id = 29");
        $stmt->execute();
        $result = $stmt->fetch();
        echo "<p>✅ Database connection working. Product 29 exists: " . ($result['count'] > 0 ? 'Yes' : 'No') . "</p>";
        
        // Check cart table
        $user_id = $_SESSION['local_user_id'] ?? $_SESSION['user_id'];
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM cart WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $cart_result = $stmt->fetch();
        echo "<p>Current cart items for user: " . $cart_result['count'] . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
    }
}

// Test add to cart manually
if (isset($_POST['test_add'])) {
    echo "<h3>Manual Add to Cart Test:</h3>";
    
    $product_id = 29;
    $quantity = 1;
    $user_id = $_SESSION['local_user_id'] ?? $_SESSION['user_id'];
    
    try {
        // Check if item already exists in cart
        $stmt = $conn->prepare("SELECT quantity FROM cart WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$user_id, $product_id]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            // Update quantity
            $new_quantity = $existing['quantity'] + $quantity;
            $stmt = $conn->prepare("UPDATE cart SET quantity = ?, updated_at = NOW() WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$new_quantity, $user_id, $product_id]);
            echo "<p style='color: green;'>✅ Updated cart item. New quantity: $new_quantity</p>";
        } else {
            // Insert new item
            $stmt = $conn->prepare("INSERT INTO cart (user_id, product_id, quantity, created_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$user_id, $product_id, $quantity]);
            echo "<p style='color: green;'>✅ Added new item to cart</p>";
        }
        
        // Get updated cart count
        $stmt = $conn->prepare("SELECT SUM(quantity) as total FROM cart WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $total = $stmt->fetch();
        echo "<p>Total items in cart: " . ($total['total'] ?? 0) . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error adding to cart: " . $e->getMessage() . "</p>";
    }
}
?>

<form method="post">
    <button type="submit" name="test_add" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
        Test Add Product 29 to Cart
    </button>
</form>

<p><a href="product-detail.php?id=29">Go to Product Detail Page</a></p>
<p><a href="index.php">Go to Home</a></p>
