<?php
/**
 * Advanced Search Manager
 * Handles complex search queries, filters, and sorting
 */

class SearchManager {
    private $conn;
    private $search_weights = [
        'exact_name' => 10,
        'name_start' => 8,
        'name_contains' => 6,
        'description' => 4,
        'category' => 3,
        'tags' => 2
    ];
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * Perform advanced product search with filters
     */
    public function searchProducts($params = []) {
        $search_query = $params['search'] ?? '';
        $category_id = $params['category'] ?? null;
        $min_price = $params['min_price'] ?? null;
        $max_price = $params['max_price'] ?? null;
        $min_rating = $params['min_rating'] ?? null;
        $in_stock = $params['in_stock'] ?? null;
        $sort_by = $params['sort'] ?? 'relevance';
        $page = (int)($params['page'] ?? 1);
        $limit = (int)($params['limit'] ?? 12);
        $offset = ($page - 1) * $limit;
        
        // Build the search query
        $sql = $this->buildSearchQuery($search_query, $category_id, $min_price, $max_price, $min_rating, $in_stock, $sort_by);
        
        try {
            // Get total count
            $count_sql = $this->buildCountQuery($search_query, $category_id, $min_price, $max_price, $min_rating, $in_stock);
            $count_params = $this->buildSearchParams($search_query, $category_id, $min_price, $max_price, $min_rating, $in_stock);
            
            $count_stmt = $this->conn->prepare($count_sql);
            $count_stmt->execute($count_params);
            $total_count = $count_stmt->fetchColumn();
            
            // Get paginated results
            $sql .= " LIMIT ? OFFSET ?";
            $params_array = $this->buildSearchParams($search_query, $category_id, $min_price, $max_price, $min_rating, $in_stock);
            $params_array[] = $limit;
            $params_array[] = $offset;
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params_array);
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Add search metadata
            foreach ($products as &$product) {
                $product['search_score'] = $this->calculateSearchScore($product, $search_query);
                $product['price_formatted'] = 'Rp ' . number_format($product['price']);
                $product['rating_stars'] = $this->generateStarRating($product['average_rating'] ?? 0);
                $product['image_url'] = !empty($product['image']) ? 'uploads/' . $product['image'] : 'assets/img/product-default.jpg';
            }
            
            return [
                'products' => $products,
                'total_count' => $total_count,
                'page' => $page,
                'total_pages' => ceil($total_count / $limit),
                'search_query' => $search_query,
                'filters_applied' => $this->getAppliedFilters($params)
            ];
            
        } catch (PDOException $e) {
            error_log("Search error: " . $e->getMessage());
            return [
                'products' => [],
                'total_count' => 0,
                'page' => 1,
                'total_pages' => 0,
                'error' => 'Search failed'
            ];
        }
    }
    
    /**
     * Build main search query
     */
    private function buildSearchQuery($search_query, $category_id, $min_price, $max_price, $min_rating, $in_stock, $sort_by) {
        $sql = "
            SELECT DISTINCT p.*, 
                   c.name as category_name,
                   COALESCE(prs.average_rating, 0) as average_rating,
                   COALESCE(prs.total_reviews, 0) as total_reviews,
                   COALESCE(ps.total_sold, 0) as total_sold
        ";
        
        // Add search relevance score if searching
        if (!empty($search_query)) {
            $sql .= ", (
                CASE 
                    WHEN p.name = ? THEN " . $this->search_weights['exact_name'] . "
                    WHEN p.name LIKE ? THEN " . $this->search_weights['name_start'] . "
                    WHEN p.name LIKE ? THEN " . $this->search_weights['name_contains'] . "
                    WHEN p.description LIKE ? THEN " . $this->search_weights['description'] . "
                    WHEN c.name LIKE ? THEN " . $this->search_weights['category'] . "
                    ELSE 1
                END
            ) as relevance_score";
        }
        
        $sql .= "
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.category_id
            LEFT JOIN product_rating_summary prs ON p.product_id = prs.product_id
            LEFT JOIN product_sales_summary ps ON p.product_id = ps.product_id
            WHERE p.is_active = 1
        ";
        
        // Add search conditions
        if (!empty($search_query)) {
            $sql .= " AND (
                p.name LIKE ? OR 
                p.description LIKE ? OR 
                c.name LIKE ?
            )";
        }
        
        // Add filters
        if ($category_id) {
            $sql .= " AND p.category_id = ?";
        }
        
        if ($min_price !== null) {
            $sql .= " AND p.price >= ?";
        }
        
        if ($max_price !== null) {
            $sql .= " AND p.price <= ?";
        }
        
        if ($min_rating !== null) {
            $sql .= " AND COALESCE(prs.average_rating, 0) >= ?";
        }
        
        if ($in_stock) {
            $sql .= " AND p.stock > 0";
        }
        
        // Add sorting
        $sql .= $this->buildOrderClause($sort_by, !empty($search_query));
        
        return $sql;
    }
    
    /**
     * Build count query for pagination
     */
    private function buildCountQuery($search_query, $category_id, $min_price, $max_price, $min_rating, $in_stock) {
        $sql = "
            SELECT COUNT(DISTINCT p.product_id)
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.category_id
            LEFT JOIN product_rating_summary prs ON p.product_id = prs.product_id
            WHERE p.is_active = 1
        ";
        
        // Add search conditions
        if (!empty($search_query)) {
            $sql .= " AND (
                p.name LIKE ? OR 
                p.description LIKE ? OR 
                c.name LIKE ?
            )";
        }
        
        // Add filters
        if ($category_id) {
            $sql .= " AND p.category_id = ?";
        }
        
        if ($min_price !== null) {
            $sql .= " AND p.price >= ?";
        }
        
        if ($max_price !== null) {
            $sql .= " AND p.price <= ?";
        }
        
        if ($min_rating !== null) {
            $sql .= " AND COALESCE(prs.average_rating, 0) >= ?";
        }
        
        if ($in_stock) {
            $sql .= " AND p.stock > 0";
        }
        
        return $sql;
    }
    
    /**
     * Build parameters array for prepared statements
     */
    private function buildSearchParams($search_query, $category_id, $min_price, $max_price, $min_rating, $in_stock) {
        $params = [];
        
        // Search relevance parameters (if searching)
        if (!empty($search_query)) {
            $params[] = $search_query; // exact match
            $params[] = $search_query . '%'; // starts with
            $params[] = '%' . $search_query . '%'; // contains
            $params[] = '%' . $search_query . '%'; // description
            $params[] = '%' . $search_query . '%'; // category
        }
        
        // Search condition parameters
        if (!empty($search_query)) {
            $params[] = '%' . $search_query . '%'; // name
            $params[] = '%' . $search_query . '%'; // description
            $params[] = '%' . $search_query . '%'; // category
        }
        
        // Filter parameters
        if ($category_id) {
            $params[] = $category_id;
        }
        
        if ($min_price !== null) {
            $params[] = $min_price;
        }
        
        if ($max_price !== null) {
            $params[] = $max_price;
        }
        
        if ($min_rating !== null) {
            $params[] = $min_rating;
        }
        
        return $params;
    }
    
    /**
     * Build ORDER BY clause
     */
    private function buildOrderClause($sort_by, $has_search = false) {
        switch ($sort_by) {
            case 'relevance':
                return $has_search ? " ORDER BY relevance_score DESC, p.name ASC" : " ORDER BY p.created_at DESC";
                
            case 'price_low':
                return " ORDER BY p.price ASC";
                
            case 'price_high':
                return " ORDER BY p.price DESC";
                
            case 'rating':
                return " ORDER BY average_rating DESC, total_reviews DESC";
                
            case 'popularity':
                return " ORDER BY total_sold DESC, average_rating DESC";
                
            case 'newest':
                return " ORDER BY p.created_at DESC";
                
            case 'oldest':
                return " ORDER BY p.created_at ASC";
                
            case 'name_asc':
                return " ORDER BY p.name ASC";
                
            case 'name_desc':
                return " ORDER BY p.name DESC";
                
            default:
                return " ORDER BY p.name ASC";
        }
    }
    
    /**
     * Calculate search relevance score
     */
    private function calculateSearchScore($product, $search_query) {
        if (empty($search_query)) return 0;
        
        $score = 0;
        $query_lower = strtolower($search_query);
        $name_lower = strtolower($product['name']);
        $desc_lower = strtolower($product['description'] ?? '');
        $cat_lower = strtolower($product['category_name'] ?? '');
        
        // Exact name match
        if ($name_lower === $query_lower) {
            $score += $this->search_weights['exact_name'];
        }
        // Name starts with query
        elseif (strpos($name_lower, $query_lower) === 0) {
            $score += $this->search_weights['name_start'];
        }
        // Name contains query
        elseif (strpos($name_lower, $query_lower) !== false) {
            $score += $this->search_weights['name_contains'];
        }
        
        // Description contains query
        if (strpos($desc_lower, $query_lower) !== false) {
            $score += $this->search_weights['description'];
        }
        
        // Category contains query
        if (strpos($cat_lower, $query_lower) !== false) {
            $score += $this->search_weights['category'];
        }
        
        return $score;
    }
    
    /**
     * Generate star rating HTML
     */
    private function generateStarRating($rating) {
        $stars = '';
        $full_stars = floor($rating);
        $half_star = ($rating - $full_stars) >= 0.5;
        
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $full_stars) {
                $stars .= '<i class="fas fa-star text-warning"></i>';
            } elseif ($i == $full_stars + 1 && $half_star) {
                $stars .= '<i class="fas fa-star-half-alt text-warning"></i>';
            } else {
                $stars .= '<i class="far fa-star text-warning"></i>';
            }
        }
        
        return $stars;
    }
    
    /**
     * Get applied filters for display
     */
    private function getAppliedFilters($params) {
        $filters = [];
        
        if (!empty($params['search'])) {
            $filters[] = 'Search: "' . htmlspecialchars($params['search']) . '"';
        }
        
        if (!empty($params['category'])) {
            // Get category name
            try {
                $stmt = $this->conn->prepare("SELECT name FROM categories WHERE category_id = ?");
                $stmt->execute([$params['category']]);
                $category_name = $stmt->fetchColumn();
                if ($category_name) {
                    $filters[] = 'Category: ' . $category_name;
                }
            } catch (PDOException $e) {
                // Ignore error
            }
        }
        
        if (!empty($params['min_price']) || !empty($params['max_price'])) {
            $price_filter = 'Price: ';
            if (!empty($params['min_price'])) {
                $price_filter .= 'Rp ' . number_format($params['min_price']);
            }
            if (!empty($params['min_price']) && !empty($params['max_price'])) {
                $price_filter .= ' - ';
            }
            if (!empty($params['max_price'])) {
                $price_filter .= 'Rp ' . number_format($params['max_price']);
            }
            $filters[] = $price_filter;
        }
        
        if (!empty($params['min_rating'])) {
            $filters[] = 'Rating: ' . $params['min_rating'] . '+ stars';
        }
        
        if (!empty($params['in_stock'])) {
            $filters[] = 'In Stock Only';
        }
        
        return $filters;
    }
    
    /**
     * Get search suggestions
     */
    public function getSearchSuggestions($query, $limit = 10) {
        if (strlen($query) < 2) {
            return [];
        }
        
        try {
            $stmt = $this->conn->prepare("
                (SELECT 
                    p.name as title,
                    'product' as type,
                    p.product_id as id,
                    p.price,
                    p.image,
                    c.name as category_name,
                    COALESCE(prs.average_rating, 0) as rating
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.category_id
                LEFT JOIN product_rating_summary prs ON p.product_id = prs.product_id
                WHERE p.is_active = 1 AND (p.name LIKE ? OR p.description LIKE ?)
                ORDER BY 
                    CASE 
                        WHEN p.name LIKE ? THEN 1
                        WHEN p.name LIKE ? THEN 2
                        ELSE 3
                    END,
                    p.name ASC
                LIMIT ?)
                
                UNION ALL
                
                (SELECT 
                    c.name as title,
                    'category' as type,
                    c.category_id as id,
                    NULL as price,
                    c.image,
                    NULL as category_name,
                    NULL as rating
                FROM categories c
                WHERE c.name LIKE ?
                ORDER BY c.name ASC
                LIMIT 3)
            ");
            
            $search_term = '%' . $query . '%';
            $search_start = $query . '%';
            
            $stmt->execute([
                $search_term, $search_term, // product search
                $search_start, $search_term, // ordering
                $limit - 3, // leave room for categories
                $search_term // category search
            ]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Search suggestions error: " . $e->getMessage());
            return [];
        }
    }
}
?>
