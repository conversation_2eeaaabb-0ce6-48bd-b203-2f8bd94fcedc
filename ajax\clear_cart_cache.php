<?php
/**
 * Clear Cart Cache
 * Clears the cart count cache to force refresh on next page load
 */

session_start();

// Set response header to JSON
header('Content-Type: application/json');

try {
    if (isset($_SESSION['user_id'])) {
        $cache_key = 'user_counts_' . $_SESSION['user_id'];
        
        // Clear the cache
        unset($_SESSION[$cache_key]);
        unset($_SESSION[$cache_key . '_time']);
        
        echo json_encode([
            'success' => true,
            'message' => 'Cache cleared successfully'
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'No cache to clear (not logged in)'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error clearing cache: ' . $e->getMessage()
    ]);
}
?>
