<?php
/**
 * Debug Add to Cart - For troubleshooting
 */

// Prevent any output before JSON
ob_start();
error_reporting(0); // Suppress all errors for clean JSON
session_start();

// Clear any previous output
if (ob_get_length()) ob_clean();

// Set JSON headers
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

try {
    // Include database connection
    require_once '../includes/db_connect.php';
    
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Please login first',
            'debug' => 'User not logged in'
        ]);
        exit;
    }
    
    // Validate input
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    $user_id = $_SESSION['user_id'];
    
    if ($product_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid product ID',
            'debug' => "Product ID: {$product_id}"
        ]);
        exit;
    }
    
    if ($quantity <= 0) {
        $quantity = 1;
    }
    
    // Check if product exists
    $stmt = $conn->prepare("SELECT product_id, name, price, stock FROM products WHERE product_id = ? LIMIT 1");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        echo json_encode([
            'success' => false,
            'message' => 'Product not found',
            'debug' => "Product ID {$product_id} not found in database"
        ]);
        exit;
    }
    
    // Check stock if available
    if (isset($product['stock']) && $product['stock'] < $quantity) {
        echo json_encode([
            'success' => false,
            'message' => "Insufficient stock. Available: {$product['stock']}",
            'debug' => "Stock check failed"
        ]);
        exit;
    }
    
    // Get or create cart
    $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ? LIMIT 1");
    $stmt->execute([$user_id]);
    $cart_id = $stmt->fetchColumn();
    
    if (!$cart_id) {
        // Create new cart
        $stmt = $conn->prepare("INSERT INTO carts (user_id) VALUES (?)");
        $stmt->execute([$user_id]);
        $cart_id = $conn->lastInsertId();
    }
    
    // Check if item already in cart
    $stmt = $conn->prepare("SELECT quantity FROM cart_items WHERE cart_id = ? AND product_id = ? LIMIT 1");
    $stmt->execute([$cart_id, $product_id]);
    $existing_quantity = $stmt->fetchColumn();
    
    if ($existing_quantity) {
        // Update existing item
        $new_quantity = $existing_quantity + $quantity;
        
        // Check total stock
        if (isset($product['stock']) && $new_quantity > $product['stock']) {
            echo json_encode([
                'success' => false,
                'message' => "Cannot add {$quantity} more. Maximum available: " . ($product['stock'] - $existing_quantity),
                'debug' => "Stock limit exceeded"
            ]);
            exit;
        }
        
        $stmt = $conn->prepare("UPDATE cart_items SET quantity = ? WHERE cart_id = ? AND product_id = ?");
        $stmt->execute([$new_quantity, $cart_id, $product_id]);
        $final_quantity = $new_quantity;
        $message = "Cart updated! Quantity increased to {$final_quantity}";
    } else {
        // Add new item
        $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity) VALUES (?, ?, ?)");
        $stmt->execute([$cart_id, $product_id, $quantity]);
        $final_quantity = $quantity;
        $message = "Product added to cart successfully!";
    }
    
    // Get total cart count
    $stmt = $conn->prepare("SELECT COALESCE(SUM(quantity), 0) FROM cart_items WHERE cart_id = ?");
    $stmt->execute([$cart_id]);
    $cart_count = $stmt->fetchColumn();
    
    // Success response
    echo json_encode([
        'success' => true,
        'message' => $message,
        'cart_count' => (int)$cart_count,
        'product_id' => $product_id,
        'product_name' => $product['name'],
        'quantity_added' => $quantity,
        'total_quantity' => $final_quantity,
        'product_price' => (float)$product['price'],
        'debug' => [
            'cart_id' => $cart_id,
            'user_id' => $user_id,
            'existing_quantity' => $existing_quantity,
            'final_quantity' => $final_quantity
        ]
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred',
        'debug' => 'PDO Exception: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred',
        'debug' => 'Exception: ' . $e->getMessage()
    ]);
}

// Ensure clean output
ob_end_flush();
?>
