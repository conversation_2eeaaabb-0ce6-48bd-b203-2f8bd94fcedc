-- Payment Methods Expansion for Indonesian Market
-- Add more payment methods popular in Indonesia

USE db_tewuneed;

-- Create payment_methods table if not exists
CREATE TABLE IF NOT EXISTS payment_methods (
    method_id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    type ENUM('ewallet', 'va', 'card', 'bank_transfer', 'cod', 'installment', 'paylater', 'qris') NOT NULL,
    provider VARCHAR(50) NOT NULL,
    icon VARCHAR(100) NULL,
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    min_amount DECIMAL(15,2) DEFAULT 0,
    max_amount DECIMAL(15,2) DEFAULT *********,
    fee_type ENUM('fixed', 'percentage', 'mixed') DEFAULT 'fixed',
    fee_amount DECIMAL(15,2) DEFAULT 0,
    fee_percentage DECIMAL(5,2) DEFAULT 0,
    processing_time VARCHAR(100) NULL,
    instructions TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Clear existing data and insert comprehensive Indonesian payment methods
DELETE FROM payment_methods;

-- E-Wallet Methods
INSERT INTO payment_methods (code, name, type, provider, icon, description, min_amount, max_amount, fee_type, fee_amount, processing_time, instructions) VALUES
('gopay', 'GoPay', 'ewallet', 'midtrans', 'fab fa-google-pay', 'Bayar dengan GoPay - mudah, cepat, dan aman', 1000, ********, 'fixed', 0, 'Instant', 'Scan QR code atau klik link untuk membayar dengan GoPay'),
('ovo', 'OVO', 'ewallet', 'midtrans', 'fas fa-wallet', 'Bayar dengan OVO - dapatkan cashback dan poin', 1000, ********, 'fixed', 0, 'Instant', 'Login ke aplikasi OVO dan konfirmasi pembayaran'),
('dana', 'DANA', 'ewallet', 'midtrans', 'fas fa-mobile-alt', 'Bayar dengan DANA - digital wallet terpercaya', 1000, ********, 'fixed', 0, 'Instant', 'Scan QR code atau masukkan nomor DANA untuk pembayaran'),
('linkaja', 'LinkAja', 'ewallet', 'midtrans', 'fas fa-link', 'Bayar dengan LinkAja - BUMN digital wallet', 1000, ********, 'fixed', 0, 'Instant', 'Gunakan aplikasi LinkAja untuk menyelesaikan pembayaran'),
('shopeepay', 'ShopeePay', 'ewallet', 'midtrans', 'fas fa-shopping-bag', 'Bayar dengan ShopeePay - dapatkan cashback', 1000, ********, 'fixed', 0, 'Instant', 'Buka aplikasi Shopee dan pilih ShopeePay untuk pembayaran');

-- Virtual Account Methods
INSERT INTO payment_methods (code, name, type, provider, icon, description, min_amount, max_amount, fee_type, fee_amount, processing_time, instructions) VALUES
('va_bca', 'BCA Virtual Account', 'va', 'midtrans', 'fas fa-university', 'Transfer melalui ATM, mobile banking, atau internet banking BCA', 10000, ********, 'fixed', 4000, '1-3 minutes', 'Transfer ke nomor Virtual Account BCA yang diberikan'),
('va_mandiri', 'Mandiri Virtual Account', 'va', 'midtrans', 'fas fa-university', 'Transfer melalui ATM, mobile banking, atau internet banking Mandiri', 10000, ********, 'fixed', 4000, '1-3 minutes', 'Transfer ke nomor Virtual Account Mandiri yang diberikan'),
('va_bni', 'BNI Virtual Account', 'va', 'midtrans', 'fas fa-university', 'Transfer melalui ATM, mobile banking, atau internet banking BNI', 10000, ********, 'fixed', 4000, '1-3 minutes', 'Transfer ke nomor Virtual Account BNI yang diberikan'),
('va_bri', 'BRI Virtual Account', 'va', 'midtrans', 'fas fa-university', 'Transfer melalui ATM, mobile banking, atau internet banking BRI', 10000, ********, 'fixed', 4000, '1-3 minutes', 'Transfer ke nomor Virtual Account BRI yang diberikan'),
('va_permata', 'Permata Virtual Account', 'va', 'midtrans', 'fas fa-university', 'Transfer melalui ATM, mobile banking, atau internet banking Permata', 10000, ********, 'fixed', 4000, '1-3 minutes', 'Transfer ke nomor Virtual Account Permata yang diberikan'),
('va_cimb', 'CIMB Niaga Virtual Account', 'va', 'midtrans', 'fas fa-university', 'Transfer melalui ATM, mobile banking, atau internet banking CIMB Niaga', 10000, ********, 'fixed', 4000, '1-3 minutes', 'Transfer ke nomor Virtual Account CIMB Niaga yang diberikan');

-- Credit/Debit Card Methods
INSERT INTO payment_methods (code, name, type, provider, icon, description, min_amount, max_amount, fee_type, fee_amount, fee_percentage, processing_time, instructions) VALUES
('credit_card', 'Credit Card', 'card', 'midtrans', 'fab fa-cc-visa', 'Bayar dengan kartu kredit Visa, Mastercard, JCB', 10000, ********, 'mixed', 2000, 2.9, 'Instant', 'Masukkan detail kartu kredit untuk pembayaran'),
('debit_card', 'Debit Card', 'card', 'midtrans', 'fas fa-credit-card', 'Bayar dengan kartu debit online', 10000, ********, 'mixed', 2000, 2.9, 'Instant', 'Masukkan detail kartu debit untuk pembayaran');

-- Bank Transfer Methods
INSERT INTO payment_methods (code, name, type, provider, icon, description, min_amount, max_amount, fee_type, fee_amount, processing_time, instructions) VALUES
('bank_transfer', 'Bank Transfer', 'bank_transfer', 'manual', 'fas fa-exchange-alt', 'Transfer manual ke rekening bank TeWuNeed', 10000, ********, 'fixed', 0, '1-24 hours', 'Transfer ke rekening yang diberikan dan upload bukti transfer');

-- QRIS Methods
INSERT INTO payment_methods (code, name, type, provider, icon, description, min_amount, max_amount, fee_type, fee_amount, processing_time, instructions) VALUES
('qris', 'QRIS', 'qris', 'midtrans', 'fas fa-qrcode', 'Bayar dengan QRIS - scan QR code dengan aplikasi bank atau e-wallet apapun', 1000, ********, 'fixed', 0, 'Instant', 'Scan QR code dengan aplikasi mobile banking atau e-wallet yang mendukung QRIS');

-- Cash on Delivery
INSERT INTO payment_methods (code, name, type, provider, icon, description, min_amount, max_amount, fee_type, fee_amount, processing_time, instructions) VALUES
('cod', 'Cash on Delivery (COD)', 'cod', 'manual', 'fas fa-hand-holding-usd', 'Bayar tunai saat barang diterima', 10000, 5000000, 'fixed', 5000, 'On delivery', 'Siapkan uang pas saat kurir datang mengantarkan pesanan');

-- Installment Methods
INSERT INTO payment_methods (code, name, type, provider, icon, description, min_amount, max_amount, fee_type, fee_percentage, processing_time, instructions) VALUES
('kredivo', 'Kredivo', 'installment', 'kredivo', 'fas fa-calendar-alt', 'Bayar dengan cicilan 0% hingga 12 bulan', 500000, ********, 'percentage', 0, 'Instant', 'Login ke akun Kredivo dan pilih tenor cicilan yang diinginkan'),
('akulaku', 'Akulaku PayLater', 'paylater', 'akulaku', 'fas fa-clock', 'Bayar nanti dengan Akulaku - cicilan 0%', 100000, ********, 'percentage', 0, 'Instant', 'Verifikasi dengan Akulaku dan pilih metode pembayaran'),
('indodana', 'Indodana PayLater', 'paylater', 'indodana', 'fas fa-calendar-check', 'Bayar nanti dengan Indodana - fleksibel dan mudah', 100000, 25000000, 'percentage', 0, 'Instant', 'Login ke akun Indodana dan konfirmasi pembayaran');

-- Crypto Payment (for modern users)
INSERT INTO payment_methods (code, name, type, provider, icon, description, min_amount, max_amount, fee_type, fee_percentage, processing_time, instructions) VALUES
('crypto_btc', 'Bitcoin', 'crypto', 'coinbase', 'fab fa-bitcoin', 'Bayar dengan Bitcoin - cryptocurrency terpopuler', 50000, ********0, 'percentage', 1.0, '10-60 minutes', 'Transfer Bitcoin ke alamat wallet yang diberikan'),
('crypto_eth', 'Ethereum', 'crypto', 'coinbase', 'fab fa-ethereum', 'Bayar dengan Ethereum - smart contract platform', 50000, ********0, 'percentage', 1.0, '5-30 minutes', 'Transfer Ethereum ke alamat wallet yang diberikan');

-- Create payment method categories for better organization
CREATE TABLE IF NOT EXISTS payment_categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    icon VARCHAR(100) NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE
);

INSERT INTO payment_categories (name, description, icon, sort_order) VALUES
('E-Wallet', 'Dompet digital untuk pembayaran instant', 'fas fa-mobile-alt', 1),
('Virtual Account', 'Transfer bank melalui nomor virtual account', 'fas fa-university', 2),
('Kartu Kredit/Debit', 'Pembayaran dengan kartu kredit atau debit', 'fas fa-credit-card', 3),
('QRIS', 'Quick Response Code Indonesian Standard', 'fas fa-qrcode', 4),
('Bank Transfer', 'Transfer manual ke rekening bank', 'fas fa-exchange-alt', 5),
('Bayar Nanti', 'Cicilan dan pay later services', 'fas fa-calendar-alt', 6),
('Cash on Delivery', 'Bayar tunai saat barang diterima', 'fas fa-hand-holding-usd', 7),
('Cryptocurrency', 'Pembayaran dengan mata uang digital', 'fab fa-bitcoin', 8);

-- Add category mapping
ALTER TABLE payment_methods ADD COLUMN category_id INT NULL;
ALTER TABLE payment_methods ADD FOREIGN KEY (category_id) REFERENCES payment_categories(category_id);

-- Update payment methods with categories
UPDATE payment_methods SET category_id = 1 WHERE type = 'ewallet';
UPDATE payment_methods SET category_id = 2 WHERE type = 'va';
UPDATE payment_methods SET category_id = 3 WHERE type = 'card';
UPDATE payment_methods SET category_id = 4 WHERE type = 'qris';
UPDATE payment_methods SET category_id = 5 WHERE type = 'bank_transfer';
UPDATE payment_methods SET category_id = 6 WHERE type IN ('installment', 'paylater');
UPDATE payment_methods SET category_id = 7 WHERE type = 'cod';
UPDATE payment_methods SET category_id = 8 WHERE type = 'crypto';

-- Create payment method availability by region
CREATE TABLE IF NOT EXISTS payment_method_regions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    method_id INT NOT NULL,
    region VARCHAR(100) NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (method_id) REFERENCES payment_methods(method_id) ON DELETE CASCADE,
    UNIQUE KEY unique_method_region (method_id, region)
);

-- Add regional availability (all methods available in major cities)
INSERT INTO payment_method_regions (method_id, region, is_available)
SELECT method_id, 'jakarta', TRUE FROM payment_methods;

INSERT INTO payment_method_regions (method_id, region, is_available)
SELECT method_id, 'bandung', TRUE FROM payment_methods;

INSERT INTO payment_method_regions (method_id, region, is_available)
SELECT method_id, 'surabaya', TRUE FROM payment_methods;

-- Limited availability for smaller cities (no COD, no crypto)
INSERT INTO payment_method_regions (method_id, region, is_available)
SELECT method_id, 'medan', TRUE FROM payment_methods WHERE type NOT IN ('cod', 'crypto');

INSERT INTO payment_method_regions (method_id, region, is_available)
SELECT method_id, 'makassar', TRUE FROM payment_methods WHERE type NOT IN ('cod', 'crypto');

-- Create indexes for better performance
CREATE INDEX idx_payment_methods_type ON payment_methods(type);
CREATE INDEX idx_payment_methods_provider ON payment_methods(provider);
CREATE INDEX idx_payment_methods_active ON payment_methods(is_active);
CREATE INDEX idx_payment_method_regions_region ON payment_method_regions(region);

SELECT 'Payment methods expansion completed successfully!' as status;
