<?php
/**
 * Shipping API Manager
 * Integrates with real shipping provider APIs for live rates and tracking
 */

class ShippingAPIManager {
    private $conn;
    private $api_configs = [];
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
        $this->loadAPIConfigs();
    }
    
    /**
     * Load API configurations from database
     */
    private function loadAPIConfigs() {
        $this->api_configs = [
            'jne' => [
                'base_url' => 'https://apiv2.jne.co.id:10102',
                'api_key' => getenv('JNE_API_KEY') ?: 'your_jne_api_key',
                'username' => getenv('JNE_USERNAME') ?: 'your_jne_username',
                'enabled' => true
            ],
            'pos' => [
                'base_url' => 'https://trackingapi.pos.co.id',
                'api_key' => getenv('POS_API_KEY') ?: 'your_pos_api_key',
                'enabled' => true
            ],
            'tiki' => [
                'base_url' => 'https://api.tiki.id',
                'api_key' => getenv('TIKI_API_KEY') ?: 'your_tiki_api_key',
                'enabled' => true
            ],
            'sicepat' => [
                'base_url' => 'https://api.sicepat.com',
                'api_key' => getenv('SICEPAT_API_KEY') ?: 'your_sicepat_api_key',
                'enabled' => true
            ],
            'jnt' => [
                'base_url' => 'https://api.jet.co.id',
                'api_key' => getenv('JNT_API_KEY') ?: 'your_jnt_api_key',
                'enabled' => true
            ]
        ];
    }
    
    /**
     * Get live shipping rates from APIs
     */
    public function getLiveShippingRates($origin, $destination, $weight, $dimensions = null) {
        $rates = [];
        
        foreach ($this->api_configs as $provider => $config) {
            if (!$config['enabled']) continue;
            
            try {
                switch ($provider) {
                    case 'jne':
                        $provider_rates = $this->getJNERates($origin, $destination, $weight);
                        break;
                    case 'pos':
                        $provider_rates = $this->getPosRates($origin, $destination, $weight);
                        break;
                    case 'tiki':
                        $provider_rates = $this->getTikiRates($origin, $destination, $weight);
                        break;
                    case 'sicepat':
                        $provider_rates = $this->getSicepatRates($origin, $destination, $weight);
                        break;
                    case 'jnt':
                        $provider_rates = $this->getJNTRates($origin, $destination, $weight);
                        break;
                    default:
                        continue 2;
                }
                
                if (!empty($provider_rates)) {
                    $rates = array_merge($rates, $provider_rates);
                }
                
            } catch (Exception $e) {
                error_log("Error getting rates from {$provider}: " . $e->getMessage());
                // Fall back to static rates if API fails
                $rates = array_merge($rates, $this->getFallbackRates($provider, $weight));
            }
        }
        
        // Sort by price
        usort($rates, function($a, $b) {
            return $a['cost'] <=> $b['cost'];
        });
        
        return $rates;
    }
    
    /**
     * Get JNE shipping rates
     */
    private function getJNERates($origin, $destination, $weight) {
        $config = $this->api_configs['jne'];
        
        $data = [
            'username' => $config['username'],
            'api_key' => $config['api_key'],
            'from' => $origin,
            'thru' => $destination,
            'weight' => $weight
        ];
        
        $response = $this->makeAPIRequest($config['base_url'] . '/tarif/v1', $data, 'POST');
        
        if (!$response || !isset($response['price'])) {
            throw new Exception('Invalid JNE API response');
        }
        
        $rates = [];
        foreach ($response['price'] as $service) {
            $rates[] = [
                'provider_code' => 'jne',
                'provider_name' => 'JNE',
                'service_code' => $service['service_code'],
                'service_name' => $service['service_display'],
                'cost' => (int)$service['price'],
                'estimated_days' => $service['etd'],
                'description' => $service['service_display']
            ];
        }
        
        return $rates;
    }
    
    /**
     * Get Pos Indonesia shipping rates
     */
    private function getPosRates($origin, $destination, $weight) {
        $config = $this->api_configs['pos'];
        
        $data = [
            'api_key' => $config['api_key'],
            'origin' => $origin,
            'destination' => $destination,
            'weight' => $weight,
            'courier' => 'pos'
        ];
        
        $response = $this->makeAPIRequest($config['base_url'] . '/cost', $data, 'POST');
        
        if (!$response || !isset($response['rajaongkir']['results'])) {
            throw new Exception('Invalid Pos API response');
        }
        
        $rates = [];
        foreach ($response['rajaongkir']['results'][0]['costs'] as $service) {
            $rates[] = [
                'provider_code' => 'pos',
                'provider_name' => 'Pos Indonesia',
                'service_code' => $service['service'],
                'service_name' => $service['description'],
                'cost' => (int)$service['cost'][0]['value'],
                'estimated_days' => $service['cost'][0]['etd'],
                'description' => $service['description']
            ];
        }
        
        return $rates;
    }
    
    /**
     * Get TIKI shipping rates
     */
    private function getTikiRates($origin, $destination, $weight) {
        $config = $this->api_configs['tiki'];
        
        $data = [
            'api_key' => $config['api_key'],
            'origin' => $origin,
            'destination' => $destination,
            'weight' => $weight
        ];
        
        $response = $this->makeAPIRequest($config['base_url'] . '/rates', $data, 'POST');
        
        if (!$response || !isset($response['data'])) {
            throw new Exception('Invalid TIKI API response');
        }
        
        $rates = [];
        foreach ($response['data'] as $service) {
            $rates[] = [
                'provider_code' => 'tiki',
                'provider_name' => 'TIKI',
                'service_code' => $service['service_code'],
                'service_name' => $service['service_name'],
                'cost' => (int)$service['tariff'],
                'estimated_days' => $service['etd'],
                'description' => $service['service_name']
            ];
        }
        
        return $rates;
    }
    
    /**
     * Get SiCepat shipping rates
     */
    private function getSicepatRates($origin, $destination, $weight) {
        $config = $this->api_configs['sicepat'];
        
        $data = [
            'api_key' => $config['api_key'],
            'origin' => $origin,
            'destination' => $destination,
            'weight' => $weight
        ];
        
        $response = $this->makeAPIRequest($config['base_url'] . '/customer/tariff', $data, 'POST');
        
        if (!$response || !isset($response['sicepat']['results'])) {
            throw new Exception('Invalid SiCepat API response');
        }
        
        $rates = [];
        foreach ($response['sicepat']['results'] as $service) {
            $rates[] = [
                'provider_code' => 'sicepat',
                'provider_name' => 'SiCepat',
                'service_code' => $service['service'],
                'service_name' => $service['description'],
                'cost' => (int)$service['tariff'],
                'estimated_days' => $service['etd'],
                'description' => $service['description']
            ];
        }
        
        return $rates;
    }
    
    /**
     * Get J&T Express shipping rates
     */
    private function getJNTRates($origin, $destination, $weight) {
        $config = $this->api_configs['jnt'];
        
        $data = [
            'api_key' => $config['api_key'],
            'origin' => $origin,
            'destination' => $destination,
            'weight' => $weight
        ];
        
        $response = $this->makeAPIRequest($config['base_url'] . '/pricing', $data, 'POST');
        
        if (!$response || !isset($response['data'])) {
            throw new Exception('Invalid J&T API response');
        }
        
        $rates = [];
        foreach ($response['data'] as $service) {
            $rates[] = [
                'provider_code' => 'jnt',
                'provider_name' => 'J&T Express',
                'service_code' => $service['product_code'],
                'service_name' => $service['product_name'],
                'cost' => (int)$service['price'],
                'estimated_days' => $service['lead_time'],
                'description' => $service['product_name']
            ];
        }
        
        return $rates;
    }
    
    /**
     * Get live tracking information
     */
    public function getLiveTracking($provider, $tracking_number) {
        if (!isset($this->api_configs[$provider]) || !$this->api_configs[$provider]['enabled']) {
            return null;
        }
        
        try {
            switch ($provider) {
                case 'jne':
                    return $this->getJNETracking($tracking_number);
                case 'pos':
                    return $this->getPosTracking($tracking_number);
                case 'tiki':
                    return $this->getTikiTracking($tracking_number);
                case 'sicepat':
                    return $this->getSicepatTracking($tracking_number);
                case 'jnt':
                    return $this->getJNTTracking($tracking_number);
                default:
                    return null;
            }
        } catch (Exception $e) {
            error_log("Error getting tracking from {$provider}: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get JNE tracking
     */
    private function getJNETracking($tracking_number) {
        $config = $this->api_configs['jne'];
        
        $data = [
            'username' => $config['username'],
            'api_key' => $config['api_key'],
            'awb' => $tracking_number
        ];
        
        $response = $this->makeAPIRequest($config['base_url'] . '/tracing/v1', $data, 'POST');
        
        if (!$response || !isset($response['cnote'])) {
            return null;
        }
        
        $tracking_data = [];
        foreach ($response['cnote']['history'] as $history) {
            $tracking_data[] = [
                'date' => $history['date'],
                'time' => $history['time'],
                'description' => $history['desc'],
                'location' => $history['city']
            ];
        }
        
        return [
            'status' => $response['cnote']['pod_status'],
            'current_location' => $response['cnote']['last_status'],
            'tracking_history' => $tracking_data
        ];
    }
    
    /**
     * Make API request
     */
    private function makeAPIRequest($url, $data, $method = 'POST') {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ]
        ]);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_error($ch)) {
            curl_close($ch);
            throw new Exception('cURL Error: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        if ($http_code !== 200) {
            throw new Exception("HTTP Error: {$http_code}");
        }
        
        $decoded_response = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON response');
        }
        
        return $decoded_response;
    }
    
    /**
     * Get fallback rates when API fails
     */
    private function getFallbackRates($provider, $weight) {
        $fallback_rates = [
            'jne' => [
                ['service_code' => 'REG', 'service_name' => 'Regular', 'rate_per_kg' => 9000, 'etd' => '2-3'],
                ['service_code' => 'YES', 'service_name' => 'YES', 'rate_per_kg' => 15000, 'etd' => '1'],
                ['service_code' => 'OKE', 'service_name' => 'OKE', 'rate_per_kg' => 7000, 'etd' => '3-4']
            ],
            'pos' => [
                ['service_code' => 'REGULER', 'service_name' => 'Pos Reguler', 'rate_per_kg' => 6000, 'etd' => '3-5'],
                ['service_code' => 'EXPRESS', 'service_name' => 'Pos Express', 'rate_per_kg' => 10000, 'etd' => '1-2']
            ],
            'tiki' => [
                ['service_code' => 'REG', 'service_name' => 'Regular', 'rate_per_kg' => 8000, 'etd' => '2-3'],
                ['service_code' => 'ONS', 'service_name' => 'ONS', 'rate_per_kg' => 14000, 'etd' => '1']
            ],
            'sicepat' => [
                ['service_code' => 'SIUNT', 'service_name' => 'SiUntung', 'rate_per_kg' => 7500, 'etd' => '2-3'],
                ['service_code' => 'BEST', 'service_name' => 'Best', 'rate_per_kg' => 11000, 'etd' => '1-2']
            ],
            'jnt' => [
                ['service_code' => 'EZ', 'service_name' => 'EZ', 'rate_per_kg' => 8000, 'etd' => '2-3'],
                ['service_code' => 'REG', 'service_name' => 'Regular', 'rate_per_kg' => 7000, 'etd' => '3-4']
            ]
        ];
        
        $rates = [];
        if (isset($fallback_rates[$provider])) {
            foreach ($fallback_rates[$provider] as $service) {
                $rates[] = [
                    'provider_code' => $provider,
                    'provider_name' => ucfirst($provider),
                    'service_code' => $service['service_code'],
                    'service_name' => $service['service_name'],
                    'cost' => (int)($service['rate_per_kg'] * max(1, ceil($weight))),
                    'estimated_days' => $service['etd'],
                    'description' => $service['service_name'] . ' (Estimated)',
                    'is_fallback' => true
                ];
            }
        }
        
        return $rates;
    }
    
    /**
     * Test API connections
     */
    public function testAPIConnections() {
        $results = [];
        
        foreach ($this->api_configs as $provider => $config) {
            if (!$config['enabled']) {
                $results[$provider] = ['status' => 'disabled'];
                continue;
            }
            
            try {
                // Test with sample data
                $test_rates = $this->getLiveShippingRates('Jakarta', 'Bandung', 1);
                $provider_rates = array_filter($test_rates, function($rate) use ($provider) {
                    return $rate['provider_code'] === $provider;
                });
                
                if (!empty($provider_rates)) {
                    $results[$provider] = [
                        'status' => 'connected',
                        'rates_count' => count($provider_rates)
                    ];
                } else {
                    $results[$provider] = [
                        'status' => 'no_rates',
                        'message' => 'Connected but no rates returned'
                    ];
                }
                
            } catch (Exception $e) {
                $results[$provider] = [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Update API configuration
     */
    public function updateAPIConfig($provider, $config) {
        if (isset($this->api_configs[$provider])) {
            $this->api_configs[$provider] = array_merge($this->api_configs[$provider], $config);
            
            // Save to environment or database
            // This would typically update environment variables or database settings
            return true;
        }
        
        return false;
    }
}
?>
