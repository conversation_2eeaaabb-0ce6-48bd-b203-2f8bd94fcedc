<?php
/**
 * Analytics Tracking Endpoint
 * Receives and processes analytics data from the frontend
 */

session_start();
require_once '../includes/db_connect.php';
require_once '../includes/AnalyticsManager.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get raw POST data
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON data']);
    exit;
}

$analyticsManager = new AnalyticsManager();

try {
    $type = $data['type'] ?? '';
    $payload = $data['data'] ?? [];
    
    switch ($type) {
        case 'session':
            handleSessionTracking($analyticsManager, $payload);
            break;
            
        case 'pageview':
            handlePageViewTracking($analyticsManager, $payload);
            break;
            
        case 'event':
            handleEventTracking($analyticsManager, $payload);
            break;
            
        case 'product_view':
            handleProductViewTracking($analyticsManager, $payload);
            break;
            
        case 'search':
            handleSearchTracking($analyticsManager, $payload);
            break;
            
        case 'cart_event':
            handleCartEventTracking($analyticsManager, $payload);
            break;
            
        case 'conversion':
            handleConversionTracking($analyticsManager, $payload);
            break;
            
        case 'batch_events':
            handleBatchEvents($analyticsManager, $payload);
            break;
            
        default:
            throw new Exception('Unknown tracking type: ' . $type);
    }
    
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    error_log('Analytics tracking error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Tracking failed']);
}

/**
 * Handle session tracking
 */
function handleSessionTracking($analyticsManager, $data) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO analytics_sessions (
                session_id, user_id, referrer_domain, landing_page, device_type, 
                browser, operating_system, screen_resolution, ip_address, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE 
                updated_at = NOW(),
                page_views = page_views + 1
        ");
        
        $referrer_domain = $data['referrer'] ? parse_url($data['referrer'], PHP_URL_HOST) : 'direct';
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        
        $stmt->execute([
            $data['session_id'],
            $data['user_id'],
            $referrer_domain,
            $data['landing_page'],
            $data['device_type'],
            $data['browser'],
            $data['operating_system'],
            $data['screen_resolution'],
            $ip_address
        ]);
        
    } catch (PDOException $e) {
        error_log('Session tracking error: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Handle page view tracking
 */
function handlePageViewTracking($analyticsManager, $data) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO analytics_page_views (
                session_id, user_id, page_url, page_title, referrer_url, 
                user_agent, ip_address, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        
        $stmt->execute([
            $data['session_id'],
            $data['user_id'],
            $data['page_url'],
            $data['page_title'],
            $data['referrer_url'],
            $user_agent,
            $ip_address
        ]);
        
        // Update session page views count
        $stmt = $conn->prepare("
            UPDATE analytics_sessions 
            SET page_views = page_views + 1, updated_at = NOW()
            WHERE session_id = ?
        ");
        $stmt->execute([$data['session_id']]);
        
    } catch (PDOException $e) {
        error_log('Page view tracking error: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Handle event tracking
 */
function handleEventTracking($analyticsManager, $data) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO analytics_events (
                session_id, user_id, event_type, event_category, event_action,
                event_label, event_value, page_url, custom_data, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $custom_data = isset($data['custom_data']) ? json_encode($data['custom_data']) : null;
        
        $stmt->execute([
            $data['session_id'],
            $data['user_id'],
            $data['event_type'],
            $data['event_category'],
            $data['event_action'],
            $data['event_label'],
            $data['event_value'],
            $data['page_url'],
            $custom_data
        ]);
        
    } catch (PDOException $e) {
        error_log('Event tracking error: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Handle product view tracking
 */
function handleProductViewTracking($analyticsManager, $data) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO analytics_product_views (
                session_id, user_id, product_id, category_id, view_source, created_at
            ) VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $data['session_id'],
            $data['user_id'],
            $data['product_id'],
            $data['category_id'],
            $data['view_source']
        ]);
        
        // Update product view count
        $stmt = $conn->prepare("
            UPDATE products 
            SET view_count = view_count + 1 
            WHERE product_id = ?
        ");
        $stmt->execute([$data['product_id']]);
        
    } catch (PDOException $e) {
        error_log('Product view tracking error: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Handle search tracking
 */
function handleSearchTracking($analyticsManager, $data) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO analytics_search_queries (
                session_id, user_id, search_query, results_count, 
                clicked_result_position, clicked_product_id, no_results, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $data['session_id'],
            $data['user_id'],
            $data['search_query'],
            $data['results_count'],
            $data['clicked_result_position'],
            $data['clicked_product_id'],
            $data['no_results'] ? 1 : 0
        ]);
        
    } catch (PDOException $e) {
        error_log('Search tracking error: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Handle cart event tracking
 */
function handleCartEventTracking($analyticsManager, $data) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO analytics_cart_events (
                session_id, user_id, event_type, product_id, quantity,
                price, cart_total, cart_items_count, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $data['session_id'],
            $data['user_id'],
            $data['event_type'],
            $data['product_id'],
            $data['quantity'],
            $data['price'],
            $data['cart_total'],
            $data['cart_items_count']
        ]);
        
    } catch (PDOException $e) {
        error_log('Cart event tracking error: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Handle conversion tracking
 */
function handleConversionTracking($analyticsManager, $data) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO analytics_conversions (
                session_id, user_id, conversion_type, conversion_value,
                order_id, product_id, attribution_source, attribution_medium,
                attribution_campaign, time_to_conversion, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $attribution = $data['attribution_source'] ?? [];
        
        $stmt->execute([
            $data['session_id'],
            $data['user_id'],
            $data['conversion_type'],
            $data['conversion_value'],
            $data['order_id'],
            $data['product_id'],
            $attribution['utm_source'] ?? null,
            $attribution['utm_medium'] ?? null,
            $attribution['utm_campaign'] ?? null,
            $data['time_to_conversion']
        ]);
        
    } catch (PDOException $e) {
        error_log('Conversion tracking error: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Handle batch events
 */
function handleBatchEvents($analyticsManager, $data) {
    global $conn;
    
    $events = $data['events'] ?? [];
    
    if (empty($events)) {
        return;
    }
    
    try {
        $conn->beginTransaction();
        
        foreach ($events as $event) {
            // Determine event type and handle accordingly
            if (isset($event['event_type'])) {
                // Regular event
                handleEventTracking($analyticsManager, $event);
            } elseif (isset($event['click_x'])) {
                // Heatmap data
                handleHeatmapTracking($event);
            } else {
                // Generic event data
                $stmt = $conn->prepare("
                    INSERT INTO analytics_events (
                        session_id, user_id, event_type, event_category, 
                        event_action, page_url, custom_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $data['session_id'],
                    $data['user_id'],
                    'batch_event',
                    'user_interaction',
                    'unknown',
                    $event['page_url'] ?? '',
                    json_encode($event)
                ]);
            }
        }
        
        $conn->commit();
        
    } catch (Exception $e) {
        $conn->rollback();
        error_log('Batch events tracking error: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Handle heatmap tracking
 */
function handleHeatmapTracking($data) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO analytics_heatmap_data (
                session_id, page_url, element_selector, click_x, click_y,
                scroll_depth, viewport_width, viewport_height, event_type, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $data['session_id'],
            $data['page_url'],
            $data['element_selector'],
            $data['click_x'],
            $data['click_y'],
            $data['scroll_depth'],
            $data['viewport_width'],
            $data['viewport_height'],
            $data['event_type']
        ]);
        
    } catch (PDOException $e) {
        error_log('Heatmap tracking error: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Update session duration on page unload
 */
function updateSessionDuration($session_id, $duration) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            UPDATE analytics_sessions 
            SET session_duration = session_duration + ?, updated_at = NOW()
            WHERE session_id = ?
        ");
        $stmt->execute([$duration, $session_id]);
        
    } catch (PDOException $e) {
        error_log('Session duration update error: ' . $e->getMessage());
    }
}

/**
 * Clean old analytics data (run periodically)
 */
function cleanOldAnalyticsData($days = 90) {
    global $conn;
    
    try {
        $tables = [
            'analytics_page_views',
            'analytics_events',
            'analytics_heatmap_data',
            'analytics_search_queries',
            'analytics_cart_events'
        ];
        
        foreach ($tables as $table) {
            $stmt = $conn->prepare("
                DELETE FROM {$table} 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            $stmt->execute([$days]);
        }
        
        // Keep sessions and conversions longer (1 year)
        $stmt = $conn->prepare("
            DELETE FROM analytics_sessions 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 365 DAY)
        ");
        $stmt->execute();
        
        $stmt = $conn->prepare("
            DELETE FROM analytics_conversions 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 365 DAY)
        ");
        $stmt->execute();
        
    } catch (PDOException $e) {
        error_log('Analytics cleanup error: ' . $e->getMessage());
    }
}

// Handle special cleanup request
if (isset($_GET['cleanup']) && $_GET['cleanup'] === 'true') {
    cleanOldAnalyticsData();
    echo json_encode(['success' => true, 'message' => 'Cleanup completed']);
    exit;
}
?>
