/* Performance Optimized CSS for TeWuNeed */
/* Reduces layout thrashing and improves rendering performance */

/* Critical CSS - Above the fold content */
.critical-content {
    contain: layout style paint;
    will-change: auto;
}

/* Optimized product grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
    contain: layout;
}

.product-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    overflow: hidden;
    contain: layout style paint;
    transform: translateZ(0); /* Force hardware acceleration */
}

.product-card:hover {
    transform: translateY(-4px) translateZ(0);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Optimized image loading */
.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: #f8f9fa;
    transition: opacity 0.3s ease;
    will-change: opacity;
}

.product-image[data-loaded="false"] {
    opacity: 0;
}

.product-image[data-loaded="true"] {
    opacity: 1;
}

/* Lazy loading placeholder */
.image-placeholder {
    width: 100%;
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Optimized buttons */
.btn-optimized {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    contain: layout style;
    transform: translateZ(0);
}

.btn-optimized:hover {
    background: #0056b3;
}

.btn-optimized:active {
    transform: translateY(1px) translateZ(0);
}

.btn-optimized:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Optimized loading states */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Optimized notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    pointer-events: none;
    contain: layout;
}

.notification-toast {
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: none;
    border-radius: 8px;
    pointer-events: auto;
    transform: translateZ(0);
    will-change: transform, opacity;
}

.notification-enter {
    opacity: 0;
    transform: translateX(100%) translateZ(0);
}

.notification-enter-active {
    opacity: 1;
    transform: translateX(0) translateZ(0);
    transition: all 0.3s ease;
}

.notification-exit {
    opacity: 1;
    transform: translateX(0) translateZ(0);
}

.notification-exit-active {
    opacity: 0;
    transform: translateX(100%) translateZ(0);
    transition: all 0.3s ease;
}

/* Optimized search and filters */
.search-container {
    position: relative;
    contain: layout;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
    contain: layout;
}

.search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.filter-container {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
    contain: layout;
}

.filter-select {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: border-color 0.2s ease;
    contain: layout;
}

.filter-select:focus {
    outline: none;
    border-color: #007bff;
}

/* Optimized cart badge */
.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    transform: translateZ(0);
    will-change: transform;
}

.cart-badge-animate {
    animation: bounce 0.3s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0) translateZ(0); }
    40% { transform: translateY(-10px) translateZ(0); }
    80% { transform: translateY(-5px) translateZ(0); }
}

/* Performance optimizations */
* {
    box-sizing: border-box;
}

/* Reduce paint complexity */
.no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Optimize scrolling */
.smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Reduce layout thrashing */
.layout-stable {
    contain: layout;
}

.paint-stable {
    contain: paint;
}

.style-stable {
    contain: style;
}

/* Optimized responsive design */
@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
        padding: 0.5rem;
    }
    
    .filter-container {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .notification-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }
}

/* Critical path CSS for above-the-fold content */
.above-fold {
    contain: layout style paint;
}

/* Defer non-critical animations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Optimize font loading */
.font-display-swap {
    font-display: swap;
}

/* Optimize images */
.responsive-image {
    max-width: 100%;
    height: auto;
    loading: lazy;
    decoding: async;
}

/* Optimize form elements */
.form-optimized {
    contain: layout;
}

.form-optimized input,
.form-optimized select,
.form-optimized textarea {
    contain: layout;
    will-change: auto;
}

/* Optimize table rendering */
.table-optimized {
    table-layout: fixed;
    contain: layout;
}

/* Critical loading states */
.skeleton-loader {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-title {
    height: 1.5rem;
    width: 60%;
    margin-bottom: 1rem;
}

.skeleton-button {
    height: 2.5rem;
    width: 120px;
    border-radius: 6px;
}
