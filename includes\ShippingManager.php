<?php
/**
 * Shipping Manager
 * Handles shipping options, cost calculation, and delivery tracking
 */

class ShippingManager {
    private $conn;
    private $shipping_providers = [
        'jne' => [
            'name' => 'JNE',
            'logo' => 'assets/img/shipping/jne.png',
            'services' => [
                'REG' => ['name' => 'Regular', 'days' => '2-3', 'rate_per_kg' => 9000],
                'YES' => ['name' => 'Yakin Esok Sampai', 'days' => '1', 'rate_per_kg' => 15000],
                'OKE' => ['name' => 'Ongkos Kirim Ekonomis', 'days' => '3-4', 'rate_per_kg' => 7000]
            ]
        ],
        'pos' => [
            'name' => 'Pos Indonesia',
            'logo' => 'assets/img/shipping/pos.png',
            'services' => [
                'PAKET_KILAT_KHUSUS' => ['name' => 'Paket Kilat Khusus', 'days' => '1', 'rate_per_kg' => 12000],
                'EXPRESS' => ['name' => 'Pos Express', 'days' => '1-2', 'rate_per_kg' => 10000],
                'REGULER' => ['name' => 'Pos Reguler', 'days' => '3-5', 'rate_per_kg' => 6000]
            ]
        ],
        'tiki' => [
            'name' => 'TIKI',
            'logo' => 'assets/img/shipping/tiki.png',
            'services' => [
                'ONS' => ['name' => 'Over Night Service', 'days' => '1', 'rate_per_kg' => 14000],
                'REG' => ['name' => 'Regular Service', 'days' => '2-3', 'rate_per_kg' => 8000],
                'ECO' => ['name' => 'Economy Service', 'days' => '4-5', 'rate_per_kg' => 6500]
            ]
        ],
        'sicepat' => [
            'name' => 'SiCepat',
            'logo' => 'assets/img/shipping/sicepat.png',
            'services' => [
                'SIUNT' => ['name' => 'SiUntung', 'days' => '2-3', 'rate_per_kg' => 7500],
                'BEST' => ['name' => 'Best', 'days' => '1-2', 'rate_per_kg' => 11000],
                'GOKIL' => ['name' => 'Gokil', 'days' => '1', 'rate_per_kg' => 16000]
            ]
        ],
        'jnt' => [
            'name' => 'J&T Express',
            'logo' => 'assets/img/shipping/jnt.png',
            'services' => [
                'EZ' => ['name' => 'EZ', 'days' => '2-3', 'rate_per_kg' => 8000],
                'REG' => ['name' => 'Regular', 'days' => '3-4', 'rate_per_kg' => 7000]
            ]
        ],
        'same_day' => [
            'name' => 'Same Day Delivery',
            'logo' => 'assets/img/shipping/same-day.png',
            'services' => [
                'INSTANT' => ['name' => 'Instant (2-4 hours)', 'days' => '0', 'rate_per_kg' => 25000],
                'SAME_DAY' => ['name' => 'Same Day (6-8 hours)', 'days' => '0', 'rate_per_kg' => 18000]
            ]
        ]
    ];
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * Get available shipping options for destination
     */
    public function getShippingOptions($origin_city, $destination_city, $weight_kg, $order_value = 0) {
        $shipping_options = [];
        
        foreach ($this->shipping_providers as $provider_code => $provider) {
            foreach ($provider['services'] as $service_code => $service) {
                $cost = $this->calculateShippingCost($provider_code, $service_code, $weight_kg, $order_value);
                $estimated_delivery = $this->calculateEstimatedDelivery($service['days']);
                
                $shipping_options[] = [
                    'provider_code' => $provider_code,
                    'provider_name' => $provider['name'],
                    'provider_logo' => $provider['logo'],
                    'service_code' => $service_code,
                    'service_name' => $service['name'],
                    'estimated_days' => $service['days'],
                    'estimated_delivery' => $estimated_delivery,
                    'cost' => $cost,
                    'cost_formatted' => 'Rp ' . number_format($cost),
                    'is_free' => $cost == 0,
                    'description' => $this->getServiceDescription($provider_code, $service_code, $service['days'])
                ];
            }
        }
        
        // Sort by cost (cheapest first)
        usort($shipping_options, function($a, $b) {
            return $a['cost'] <=> $b['cost'];
        });
        
        return $shipping_options;
    }
    
    /**
     * Calculate shipping cost
     */
    private function calculateShippingCost($provider_code, $service_code, $weight_kg, $order_value = 0) {
        $provider = $this->shipping_providers[$provider_code];
        $service = $provider['services'][$service_code];
        
        // Base cost calculation
        $base_cost = $service['rate_per_kg'] * max(1, ceil($weight_kg));
        
        // Free shipping for orders above certain amount
        if ($order_value >= 100000 && in_array($service_code, ['REG', 'REGULER', 'ECO', 'EZ'])) {
            return 0; // Free shipping
        }
        
        // Same day delivery surcharge for Jakarta area only
        if ($provider_code === 'same_day') {
            $base_cost += 10000; // Additional handling fee
        }
        
        // Minimum shipping cost
        return max($base_cost, 5000);
    }
    
    /**
     * Calculate estimated delivery date
     */
    private function calculateEstimatedDelivery($days_string) {
        if ($days_string === '0') {
            return 'Today';
        }
        
        // Parse days (e.g., "2-3" or "1")
        if (strpos($days_string, '-') !== false) {
            $days_range = explode('-', $days_string);
            $min_days = (int)$days_range[0];
            $max_days = (int)$days_range[1];
            
            $min_date = date('M j', strtotime("+{$min_days} days"));
            $max_date = date('M j', strtotime("+{$max_days} days"));
            
            return "{$min_date} - {$max_date}";
        } else {
            $days = (int)$days_string;
            if ($days === 1) {
                return 'Tomorrow';
            }
            return date('M j', strtotime("+{$days} days"));
        }
    }
    
    /**
     * Get service description
     */
    private function getServiceDescription($provider_code, $service_code, $days) {
        $descriptions = [
            'jne' => [
                'REG' => 'Reliable standard delivery service',
                'YES' => 'Next day delivery guarantee',
                'OKE' => 'Economical shipping option'
            ],
            'pos' => [
                'PAKET_KILAT_KHUSUS' => 'Express delivery by Pos Indonesia',
                'EXPRESS' => 'Fast and reliable express service',
                'REGULER' => 'Standard postal service'
            ],
            'same_day' => [
                'INSTANT' => 'Ultra-fast delivery within hours',
                'SAME_DAY' => 'Delivered on the same day'
            ]
        ];
        
        return $descriptions[$provider_code][$service_code] ?? "Delivery in {$days} business days";
    }
    
    /**
     * Create shipping record
     */
    public function createShipping($order_id, $provider_code, $service_code, $cost, $recipient_data) {
        try {
            $tracking_number = $this->generateTrackingNumber($provider_code);
            
            $stmt = $this->conn->prepare("
                INSERT INTO shipping (
                    order_id, provider_code, service_code, tracking_number, 
                    shipping_cost, recipient_name, recipient_phone, recipient_address,
                    recipient_city, recipient_postal_code, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
            ");
            
            $stmt->execute([
                $order_id,
                $provider_code,
                $service_code,
                $tracking_number,
                $cost,
                $recipient_data['name'],
                $recipient_data['phone'],
                $recipient_data['address'],
                $recipient_data['city'],
                $recipient_data['postal_code']
            ]);
            
            return [
                'success' => true,
                'shipping_id' => $this->conn->lastInsertId(),
                'tracking_number' => $tracking_number
            ];
            
        } catch (PDOException $e) {
            error_log("Error creating shipping: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to create shipping record'];
        }
    }
    
    /**
     * Generate tracking number
     */
    private function generateTrackingNumber($provider_code) {
        $prefix = strtoupper(substr($provider_code, 0, 3));
        $timestamp = date('ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }
    
    /**
     * Update shipping status
     */
    public function updateShippingStatus($tracking_number, $status, $notes = null) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE shipping 
                SET status = ?, notes = ?, updated_at = NOW() 
                WHERE tracking_number = ?
            ");
            
            $result = $stmt->execute([$status, $notes, $tracking_number]);
            
            if ($result) {
                // Create tracking history
                $this->addTrackingHistory($tracking_number, $status, $notes);
                
                // Notify customer if status is important
                if (in_array($status, ['shipped', 'delivered'])) {
                    $this->notifyCustomer($tracking_number, $status);
                }
            }
            
            return $result;
            
        } catch (PDOException $e) {
            error_log("Error updating shipping status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Add tracking history
     */
    private function addTrackingHistory($tracking_number, $status, $notes) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO shipping_tracking (tracking_number, status, notes, created_at)
                VALUES (?, ?, ?, NOW())
            ");
            
            $stmt->execute([$tracking_number, $status, $notes]);
            
        } catch (PDOException $e) {
            error_log("Error adding tracking history: " . $e->getMessage());
        }
    }
    
    /**
     * Get shipping tracking info
     */
    public function getTrackingInfo($tracking_number) {
        try {
            // Get shipping info
            $stmt = $this->conn->prepare("
                SELECT s.*, o.user_id, o.order_id
                FROM shipping s
                JOIN orders o ON s.order_id = o.order_id
                WHERE s.tracking_number = ?
            ");
            $stmt->execute([$tracking_number]);
            $shipping = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$shipping) {
                return null;
            }
            
            // Get tracking history
            $stmt = $this->conn->prepare("
                SELECT * FROM shipping_tracking 
                WHERE tracking_number = ? 
                ORDER BY created_at ASC
            ");
            $stmt->execute([$tracking_number]);
            $tracking_history = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'shipping' => $shipping,
                'tracking_history' => $tracking_history,
                'provider_info' => $this->shipping_providers[$shipping['provider_code']] ?? null
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting tracking info: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Notify customer about shipping updates
     */
    private function notifyCustomer($tracking_number, $status) {
        try {
            // Get order and user info
            $stmt = $this->conn->prepare("
                SELECT o.user_id, o.order_id, s.provider_code, s.service_code
                FROM shipping s
                JOIN orders o ON s.order_id = o.order_id
                WHERE s.tracking_number = ?
            ");
            $stmt->execute([$tracking_number]);
            $info = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($info) {
                require_once 'NotificationManager.php';
                $notificationManager = new NotificationManager();
                
                $status_messages = [
                    'shipped' => 'Your order has been shipped and is on its way!',
                    'delivered' => 'Your order has been delivered successfully!'
                ];
                
                $message = $status_messages[$status] ?? "Shipping status updated: {$status}";
                
                $notificationManager->createNotification(
                    $info['user_id'],
                    'shipping',
                    "Shipping Update - Order #{$info['order_id']}",
                    $message,
                    [
                        'order_id' => $info['order_id'],
                        'tracking_number' => $tracking_number,
                        'status' => $status,
                        'action_url' => "tracking.php?number={$tracking_number}"
                    ],
                    'high'
                );
            }
            
        } catch (Exception $e) {
            error_log("Error notifying customer: " . $e->getMessage());
        }
    }
    
    /**
     * Get shipping cost estimate
     */
    public function getShippingEstimate($destination_city, $weight_kg, $order_value = 0) {
        $options = $this->getShippingOptions('Jakarta', $destination_city, $weight_kg, $order_value);
        
        return [
            'cheapest' => $options[0] ?? null,
            'fastest' => $this->getFastestOption($options),
            'free_shipping_available' => $this->hasFreeShipping($options),
            'all_options' => $options
        ];
    }
    
    /**
     * Get fastest shipping option
     */
    private function getFastestOption($options) {
        $fastest = null;
        $min_days = PHP_INT_MAX;
        
        foreach ($options as $option) {
            $days = (int)explode('-', $option['estimated_days'])[0];
            if ($days < $min_days) {
                $min_days = $days;
                $fastest = $option;
            }
        }
        
        return $fastest;
    }
    
    /**
     * Check if free shipping is available
     */
    private function hasFreeShipping($options) {
        foreach ($options as $option) {
            if ($option['is_free']) {
                return true;
            }
        }
        return false;
    }
}
?>
