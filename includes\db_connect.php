<?php
// Database configuration
$host = 'localhost';
$dbname = 'db_tewuneed';
$username = 'root';
$password = '';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $conn = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Fix for buffered queries issue
    $conn->setAttribute(PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);
    $conn->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);

    // Set autocommit to true to prevent transaction issues
    $conn->setAttribute(PDO::ATTR_AUTOCOMMIT, true);

    // Set SQL mode to be more permissive and avoid reserved word conflicts
    $conn->exec("SET sql_mode = 'ONLY_FULL_GROUP_BY,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'");

    // Ensure no hanging transactions
    try {
        $conn->rollback();
    } catch (Exception $e) {
        // No active transaction to rollback, which is good
    }

} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?>