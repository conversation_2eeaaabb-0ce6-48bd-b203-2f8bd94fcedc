<?php
/**
 * Real-time Notification Manager
 * Handles real-time notifications using Server-Sent Events (SSE)
 */

class NotificationManager {
    private $conn;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * Create a new notification
     */
    public function createNotification($user_id, $type, $title, $message, $data = null, $priority = 'normal') {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO notifications (user_id, type, title, message, data, priority, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $data_json = $data ? json_encode($data) : null;
            $stmt->execute([$user_id, $type, $title, $message, $data_json, $priority]);
            
            $notification_id = $this->conn->lastInsertId();
            
            // Trigger real-time notification
            $this->triggerRealTimeNotification($user_id, [
                'id' => $notification_id,
                'type' => $type,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'priority' => $priority,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return $notification_id;
            
        } catch (PDOException $e) {
            error_log("Error creating notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get unread notifications for a user
     */
    public function getUnreadNotifications($user_id, $limit = 10) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM notifications 
                WHERE user_id = ? AND is_read = 0 
                ORDER BY priority DESC, created_at DESC 
                LIMIT ?
            ");
            $stmt->execute([$user_id, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting unread notifications: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all notifications for a user with pagination
     */
    public function getUserNotifications($user_id, $page = 1, $limit = 20, $filter = 'all') {
        try {
            $offset = ($page - 1) * $limit;
            
            $where_clause = "WHERE user_id = ?";
            $params = [$user_id];
            
            if ($filter === 'unread') {
                $where_clause .= " AND is_read = 0";
            } elseif ($filter !== 'all') {
                $where_clause .= " AND type = ?";
                $params[] = $filter;
            }
            
            // Get total count
            $count_stmt = $this->conn->prepare("
                SELECT COUNT(*) FROM notifications $where_clause
            ");
            $count_stmt->execute($params);
            $total_count = $count_stmt->fetchColumn();
            
            // Get notifications
            $stmt = $this->conn->prepare("
                SELECT * FROM notifications 
                $where_clause
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'notifications' => $notifications,
                'total_count' => $total_count,
                'page' => $page,
                'total_pages' => ceil($total_count / $limit)
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting user notifications: " . $e->getMessage());
            return [
                'notifications' => [],
                'total_count' => 0,
                'page' => 1,
                'total_pages' => 0
            ];
        }
    }
    
    /**
     * Mark notification as read
     */
    public function markAsRead($notification_id, $user_id = null) {
        try {
            $sql = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE id = ?";
            $params = [$notification_id];
            
            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params[] = $user_id;
            }
            
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute($params);
            
        } catch (PDOException $e) {
            error_log("Error marking notification as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mark all notifications as read for a user
     */
    public function markAllAsRead($user_id) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE notifications 
                SET is_read = 1, read_at = NOW() 
                WHERE user_id = ? AND is_read = 0
            ");
            return $stmt->execute([$user_id]);
            
        } catch (PDOException $e) {
            error_log("Error marking all notifications as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get notification count by type
     */
    public function getNotificationCounts($user_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                    SUM(CASE WHEN type = 'order' THEN 1 ELSE 0 END) as order_count,
                    SUM(CASE WHEN type = 'payment' THEN 1 ELSE 0 END) as payment_count,
                    SUM(CASE WHEN type = 'system' THEN 1 ELSE 0 END) as system_count,
                    SUM(CASE WHEN type = 'promotion' THEN 1 ELSE 0 END) as promotion_count
                FROM notifications 
                WHERE user_id = ?
            ");
            $stmt->execute([$user_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting notification counts: " . $e->getMessage());
            return [
                'total' => 0,
                'unread' => 0,
                'order_count' => 0,
                'payment_count' => 0,
                'system_count' => 0,
                'promotion_count' => 0
            ];
        }
    }
    
    /**
     * Delete notification
     */
    public function deleteNotification($notification_id, $user_id = null) {
        try {
            $sql = "DELETE FROM notifications WHERE id = ?";
            $params = [$notification_id];
            
            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params[] = $user_id;
            }
            
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute($params);
            
        } catch (PDOException $e) {
            error_log("Error deleting notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Trigger real-time notification using SSE
     */
    private function triggerRealTimeNotification($user_id, $notification_data) {
        // Store notification in temporary table for SSE
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO notification_queue (user_id, notification_data, created_at)
                VALUES (?, ?, NOW())
                ON DUPLICATE KEY UPDATE 
                notification_data = VALUES(notification_data),
                created_at = VALUES(created_at)
            ");
            
            $stmt->execute([$user_id, json_encode($notification_data)]);
            
        } catch (PDOException $e) {
            error_log("Error queuing real-time notification: " . $e->getMessage());
        }
    }
    
    /**
     * Get pending notifications for SSE
     */
    public function getPendingNotifications($user_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT notification_data FROM notification_queue 
                WHERE user_id = ? 
                ORDER BY created_at ASC
            ");
            $stmt->execute([$user_id]);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Clear the queue after fetching
            if (!empty($results)) {
                $clear_stmt = $this->conn->prepare("DELETE FROM notification_queue WHERE user_id = ?");
                $clear_stmt->execute([$user_id]);
            }
            
            $notifications = [];
            foreach ($results as $result) {
                $notifications[] = json_decode($result['notification_data'], true);
            }
            
            return $notifications;
            
        } catch (PDOException $e) {
            error_log("Error getting pending notifications: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create order status notification
     */
    public function createOrderNotification($user_id, $order_id, $status, $message = null) {
        $status_messages = [
            'confirmed' => 'Your order has been confirmed and is being processed.',
            'processing' => 'Your order is being prepared for shipment.',
            'shipped' => 'Your order has been shipped and is on its way.',
            'delivered' => 'Your order has been delivered successfully.',
            'cancelled' => 'Your order has been cancelled.',
            'refunded' => 'Your order has been refunded.'
        ];
        
        $title = "Order #$order_id Update";
        $default_message = $status_messages[$status] ?? "Your order status has been updated to: $status";
        $final_message = $message ?? $default_message;
        
        $data = [
            'order_id' => $order_id,
            'status' => $status,
            'action_url' => "order_detail.php?id=$order_id"
        ];
        
        $priority = in_array($status, ['delivered', 'cancelled']) ? 'high' : 'normal';
        
        return $this->createNotification($user_id, 'order', $title, $final_message, $data, $priority);
    }
    
    /**
     * Create payment notification
     */
    public function createPaymentNotification($user_id, $order_id, $status, $amount = null, $method = null) {
        $status_messages = [
            'pending' => 'Payment is pending. Please complete your payment.',
            'confirmed' => 'Payment has been confirmed successfully.',
            'failed' => 'Payment failed. Please try again.',
            'expired' => 'Payment has expired. Please create a new order.',
            'refunded' => 'Payment has been refunded to your account.'
        ];
        
        $title = "Payment Update - Order #$order_id";
        $message = $status_messages[$status] ?? "Payment status updated: $status";
        
        if ($amount && $method) {
            $message .= " Amount: Rp " . number_format($amount) . " via $method";
        }
        
        $data = [
            'order_id' => $order_id,
            'payment_status' => $status,
            'amount' => $amount,
            'method' => $method,
            'action_url' => "order_detail.php?id=$order_id"
        ];
        
        $priority = in_array($status, ['confirmed', 'failed']) ? 'high' : 'normal';
        
        return $this->createNotification($user_id, 'payment', $title, $message, $data, $priority);
    }
    
    /**
     * Create system notification
     */
    public function createSystemNotification($user_id, $title, $message, $data = null) {
        return $this->createNotification($user_id, 'system', $title, $message, $data, 'normal');
    }
    
    /**
     * Create promotion notification
     */
    public function createPromotionNotification($user_id, $title, $message, $data = null) {
        return $this->createNotification($user_id, 'promotion', $title, $message, $data, 'low');
    }
    
    /**
     * Broadcast notification to all users
     */
    public function broadcastNotification($type, $title, $message, $data = null, $priority = 'normal') {
        try {
            // Get all active users
            $stmt = $this->conn->prepare("SELECT user_id FROM users WHERE is_active = 1");
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $success_count = 0;
            foreach ($users as $user_id) {
                if ($this->createNotification($user_id, $type, $title, $message, $data, $priority)) {
                    $success_count++;
                }
            }
            
            return $success_count;
            
        } catch (PDOException $e) {
            error_log("Error broadcasting notification: " . $e->getMessage());
            return 0;
        }
    }
}
?>
