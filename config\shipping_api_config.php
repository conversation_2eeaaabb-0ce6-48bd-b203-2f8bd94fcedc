<?php
/**
 * Shipping API Configuration
 * Configure shipping provider API credentials and settings
 */

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $env_lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($env_lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

/**
 * Shipping API Configuration Class
 */
class ShippingAPIConfig {
    
    /**
     * Get API configuration for all providers
     */
    public static function getAllConfigs() {
        return [
            'jne' => self::getJNEConfig(),
            'pos' => self::getPosConfig(),
            'tiki' => self::getTikiConfig(),
            'sicepat' => self::getSicepatConfig(),
            'jnt' => self::getJNTConfig(),
            'rajaongkir' => self::getRajaOngkirConfig()
        ];
    }
    
    /**
     * JNE API Configuration
     */
    public static function getJNEConfig() {
        return [
            'name' => 'JNE',
            'enabled' => self::getEnvBool('JNE_API_ENABLED', true),
            'base_url' => $_ENV['JNE_API_URL'] ?? 'https://apiv2.jne.co.id:10102',
            'api_key' => $_ENV['JNE_API_KEY'] ?? '',
            'username' => $_ENV['JNE_USERNAME'] ?? '',
            'timeout' => 30,
            'services' => [
                'REG' => 'Regular Service',
                'YES' => 'Yakin Esok Sampai',
                'OKE' => 'Ongkos Kirim Ekonomis',
                'CTCYES' => 'City Courier YES',
                'CTCREG' => 'City Courier REG'
            ],
            'test_mode' => self::getEnvBool('JNE_TEST_MODE', true),
            'test_credentials' => [
                'username' => 'testuser',
                'api_key' => 'testkey123'
            ]
        ];
    }
    
    /**
     * Pos Indonesia API Configuration
     */
    public static function getPosConfig() {
        return [
            'name' => 'Pos Indonesia',
            'enabled' => self::getEnvBool('POS_API_ENABLED', true),
            'base_url' => $_ENV['POS_API_URL'] ?? 'https://trackingapi.pos.co.id',
            'api_key' => $_ENV['POS_API_KEY'] ?? '',
            'timeout' => 30,
            'services' => [
                'PAKET_KILAT_KHUSUS' => 'Paket Kilat Khusus',
                'EXPRESS' => 'Pos Express',
                'REGULER' => 'Pos Reguler',
                'NEXTDAY' => 'Pos Nextday',
                'SAMEDAY' => 'Pos Sameday'
            ],
            'test_mode' => self::getEnvBool('POS_TEST_MODE', true)
        ];
    }
    
    /**
     * TIKI API Configuration
     */
    public static function getTikiConfig() {
        return [
            'name' => 'TIKI',
            'enabled' => self::getEnvBool('TIKI_API_ENABLED', true),
            'base_url' => $_ENV['TIKI_API_URL'] ?? 'https://api.tiki.id',
            'api_key' => $_ENV['TIKI_API_KEY'] ?? '',
            'timeout' => 30,
            'services' => [
                'ONS' => 'Over Night Service',
                'REG' => 'Regular Service',
                'ECO' => 'Economy Service',
                'SDS' => 'Same Day Service'
            ],
            'test_mode' => self::getEnvBool('TIKI_TEST_MODE', true)
        ];
    }
    
    /**
     * SiCepat API Configuration
     */
    public static function getSicepatConfig() {
        return [
            'name' => 'SiCepat',
            'enabled' => self::getEnvBool('SICEPAT_API_ENABLED', true),
            'base_url' => $_ENV['SICEPAT_API_URL'] ?? 'https://api.sicepat.com',
            'api_key' => $_ENV['SICEPAT_API_KEY'] ?? '',
            'timeout' => 30,
            'services' => [
                'SIUNT' => 'SiUntung',
                'BEST' => 'Best',
                'GOKIL' => 'Gokil',
                'HALU' => 'Halu'
            ],
            'test_mode' => self::getEnvBool('SICEPAT_TEST_MODE', true)
        ];
    }
    
    /**
     * J&T Express API Configuration
     */
    public static function getJNTConfig() {
        return [
            'name' => 'J&T Express',
            'enabled' => self::getEnvBool('JNT_API_ENABLED', true),
            'base_url' => $_ENV['JNT_API_URL'] ?? 'https://api.jet.co.id',
            'api_key' => $_ENV['JNT_API_KEY'] ?? '',
            'timeout' => 30,
            'services' => [
                'EZ' => 'EZ',
                'REG' => 'Regular'
            ],
            'test_mode' => self::getEnvBool('JNT_TEST_MODE', true)
        ];
    }
    
    /**
     * RajaOngkir API Configuration (Aggregator)
     */
    public static function getRajaOngkirConfig() {
        return [
            'name' => 'RajaOngkir',
            'enabled' => self::getEnvBool('RAJAONGKIR_API_ENABLED', true),
            'base_url' => $_ENV['RAJAONGKIR_API_URL'] ?? 'https://api.rajaongkir.com/starter',
            'api_key' => $_ENV['RAJAONGKIR_API_KEY'] ?? '',
            'timeout' => 30,
            'supported_couriers' => ['jne', 'pos', 'tiki'],
            'test_mode' => self::getEnvBool('RAJAONGKIR_TEST_MODE', true)
        ];
    }
    
    /**
     * Get environment variable as boolean
     */
    private static function getEnvBool($key, $default = false) {
        $value = $_ENV[$key] ?? null;
        if ($value === null) return $default;
        
        return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
    }
    
    /**
     * Validate API configuration
     */
    public static function validateConfig($provider) {
        $config = self::getAllConfigs()[$provider] ?? null;
        
        if (!$config) {
            return ['valid' => false, 'message' => 'Provider not found'];
        }
        
        if (!$config['enabled']) {
            return ['valid' => false, 'message' => 'Provider disabled'];
        }
        
        if (empty($config['api_key']) && !$config['test_mode']) {
            return ['valid' => false, 'message' => 'API key required'];
        }
        
        if (empty($config['base_url'])) {
            return ['valid' => false, 'message' => 'Base URL required'];
        }
        
        return ['valid' => true, 'message' => 'Configuration valid'];
    }
    
    /**
     * Get city list for provider
     */
    public static function getCityList($provider) {
        // This would typically fetch from the provider's API
        // For now, return common Indonesian cities
        return [
            'Jakarta' => ['id' => 'JKT', 'name' => 'Jakarta'],
            'Bandung' => ['id' => 'BDG', 'name' => 'Bandung'],
            'Surabaya' => ['id' => 'SBY', 'name' => 'Surabaya'],
            'Medan' => ['id' => 'MDN', 'name' => 'Medan'],
            'Semarang' => ['id' => 'SMG', 'name' => 'Semarang'],
            'Makassar' => ['id' => 'MKS', 'name' => 'Makassar'],
            'Palembang' => ['id' => 'PLB', 'name' => 'Palembang'],
            'Tangerang' => ['id' => 'TNG', 'name' => 'Tangerang'],
            'Bekasi' => ['id' => 'BKS', 'name' => 'Bekasi'],
            'Depok' => ['id' => 'DPK', 'name' => 'Depok']
        ];
    }
    
    /**
     * Get rate calculation settings
     */
    public static function getRateSettings() {
        return [
            'default_weight_unit' => 'kg',
            'minimum_weight' => 0.1, // 100 grams
            'maximum_weight' => 30.0, // 30 kg
            'weight_rounding' => 'up', // up, down, nearest
            'insurance_rate' => 0.002, // 0.2% of item value
            'packaging_weight' => 0.1, // 100 grams default packaging
            'free_shipping_threshold' => 100000, // Rp 100,000
            'same_day_cutoff_time' => '14:00', // 2 PM
            'next_day_cutoff_time' => '18:00', // 6 PM
            'weekend_delivery' => false,
            'holiday_delivery' => false
        ];
    }
    
    /**
     * Get fallback rates when APIs are unavailable
     */
    public static function getFallbackRates() {
        return [
            'jne' => [
                'REG' => ['rate_per_kg' => 9000, 'etd' => '2-3 days'],
                'YES' => ['rate_per_kg' => 15000, 'etd' => '1 day'],
                'OKE' => ['rate_per_kg' => 7000, 'etd' => '3-4 days']
            ],
            'pos' => [
                'REGULER' => ['rate_per_kg' => 6000, 'etd' => '3-5 days'],
                'EXPRESS' => ['rate_per_kg' => 10000, 'etd' => '1-2 days']
            ],
            'tiki' => [
                'REG' => ['rate_per_kg' => 8000, 'etd' => '2-3 days'],
                'ONS' => ['rate_per_kg' => 14000, 'etd' => '1 day'],
                'ECO' => ['rate_per_kg' => 6500, 'etd' => '4-5 days']
            ],
            'sicepat' => [
                'SIUNT' => ['rate_per_kg' => 7500, 'etd' => '2-3 days'],
                'BEST' => ['rate_per_kg' => 11000, 'etd' => '1-2 days'],
                'GOKIL' => ['rate_per_kg' => 16000, 'etd' => '1 day']
            ],
            'jnt' => [
                'EZ' => ['rate_per_kg' => 8000, 'etd' => '2-3 days'],
                'REG' => ['rate_per_kg' => 7000, 'etd' => '3-4 days']
            ]
        ];
    }
    
    /**
     * Generate sample .env file content
     */
    public static function generateEnvTemplate() {
        return "# Shipping API Configuration
# JNE Configuration
JNE_API_ENABLED=true
JNE_API_URL=https://apiv2.jne.co.id:10102
JNE_API_KEY=your_jne_api_key_here
JNE_USERNAME=your_jne_username_here
JNE_TEST_MODE=true

# Pos Indonesia Configuration
POS_API_ENABLED=true
POS_API_URL=https://trackingapi.pos.co.id
POS_API_KEY=your_pos_api_key_here
POS_TEST_MODE=true

# TIKI Configuration
TIKI_API_ENABLED=true
TIKI_API_URL=https://api.tiki.id
TIKI_API_KEY=your_tiki_api_key_here
TIKI_TEST_MODE=true

# SiCepat Configuration
SICEPAT_API_ENABLED=true
SICEPAT_API_URL=https://api.sicepat.com
SICEPAT_API_KEY=your_sicepat_api_key_here
SICEPAT_TEST_MODE=true

# J&T Express Configuration
JNT_API_ENABLED=true
JNT_API_URL=https://api.jet.co.id
JNT_API_KEY=your_jnt_api_key_here
JNT_TEST_MODE=true

# RajaOngkir Configuration (Aggregator)
RAJAONGKIR_API_ENABLED=true
RAJAONGKIR_API_URL=https://api.rajaongkir.com/starter
RAJAONGKIR_API_KEY=your_rajaongkir_api_key_here
RAJAONGKIR_TEST_MODE=true

# General Settings
SHIPPING_DEBUG=true
SHIPPING_CACHE_TTL=3600
SHIPPING_TIMEOUT=30
";
    }
}

// Create .env file if it doesn't exist
if (!file_exists(__DIR__ . '/../.env')) {
    file_put_contents(__DIR__ . '/../.env', ShippingAPIConfig::generateEnvTemplate());
    echo "Created .env template file. Please update with your actual API credentials.\n";
}
?>
