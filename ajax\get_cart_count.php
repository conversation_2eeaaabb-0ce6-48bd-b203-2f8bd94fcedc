<?php
session_start();
require_once '../includes/db_connect.php';

// Set response header to JSON
header('Content-Type: application/json');

// Default response for not logged in users
$response = [
    'success' => true,
    'cart_count' => 0
];

// Get cart count for logged in users
$user_id = $_SESSION['local_user_id'] ?? $_SESSION['user_id'] ?? $_SESSION['firebase_user_id'] ?? null;

if ($user_id) {
    try {
        // Get cart count directly using the existing connection
        $stmt = $conn->prepare("
            SELECT COALESCE(SUM(ci.quantity), 0) as cart_count
            FROM cart_items ci
            JOIN carts c ON ci.cart_id = c.cart_id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        $response['cart_count'] = (int)$result['cart_count'];
    } catch (Exception $e) {
        // Log error but still send default response
        error_log("Error getting cart count: " . $e->getMessage());
        $response['success'] = false;
        $response['message'] = 'Database error';
    }
} else {
    $response['message'] = 'Not logged in';
}

echo json_encode($response);
