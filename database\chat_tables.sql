-- Live Chat System Tables
USE db_tewuneed;

-- Create chat sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
    chat_session_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    agent_id INT NULL,
    subject VARCHAR(200) NULL,
    status ENUM('waiting', 'active', 'closed', 'transferred') DEFAULT 'waiting',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    closed_by INT NULL,
    closed_by_type ENUM('customer', 'agent', 'system') NULL,
    close_reason VARCHAR(500) NULL,
    satisfaction_rating TINYINT NULL,
    satisfaction_feedback TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    closed_at TIMESTAMP NULL,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at)
);

-- Create chat messages table
CREATE TABLE IF NOT EXISTS chat_messages (
    message_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL,
    sender_id INT NOT NULL,
    sender_type ENUM('customer', 'agent', 'system') NOT NULL,
    message TEXT NOT NULL,
    message_type ENUM('text', 'image', 'file', 'system', 'quick_reply') DEFAULT 'text',
    attachment_url VARCHAR(500) NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(session_id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_sender (sender_id, sender_type),
    INDEX idx_sent_at (sent_at),
    INDEX idx_is_read (is_read)
);

-- Create chat quick replies table for common responses
CREATE TABLE IF NOT EXISTS chat_quick_replies (
    quick_reply_id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default quick replies
INSERT INTO chat_quick_replies (category, title, message, sort_order) VALUES
('greeting', 'Welcome', 'Hello! Welcome to TeWuNeed. How can I help you today?', 1),
('greeting', 'Thank You', 'Thank you for contacting us. How may I assist you?', 2),
('order', 'Order Status', 'I can help you check your order status. Please provide your order number.', 1),
('order', 'Order Cancellation', 'I understand you want to cancel your order. Let me help you with that.', 2),
('order', 'Order Modification', 'I can help you modify your order if it hasn\'t been shipped yet.', 3),
('shipping', 'Shipping Info', 'I can provide you with shipping information and tracking details.', 1),
('shipping', 'Delivery Time', 'Standard delivery usually takes 2-3 business days within Jakarta.', 2),
('payment', 'Payment Methods', 'We accept various payment methods including credit cards, e-wallets, and bank transfers.', 1),
('payment', 'Payment Issues', 'I can help you resolve any payment-related issues. What seems to be the problem?', 2),
('product', 'Product Info', 'I can provide detailed information about our products. Which product are you interested in?', 1),
('product', 'Stock Availability', 'Let me check the stock availability for you.', 2),
('general', 'Please Wait', 'Please give me a moment to check that information for you.', 1),
('general', 'Anything Else', 'Is there anything else I can help you with today?', 2),
('closing', 'Session End', 'Thank you for contacting TeWuNeed. Have a great day!', 1),
('closing', 'Follow Up', 'If you have any other questions, feel free to contact us again.', 2);

-- Create chat departments table
CREATE TABLE IF NOT EXISTS chat_departments (
    department_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    email VARCHAR(100) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default departments
INSERT INTO chat_departments (name, description, email) VALUES
('General Support', 'General customer support and inquiries', '<EMAIL>'),
('Order Support', 'Order-related questions and issues', '<EMAIL>'),
('Technical Support', 'Technical issues and website problems', '<EMAIL>'),
('Billing Support', 'Payment and billing inquiries', '<EMAIL>');

-- Create chat agent status table
CREATE TABLE IF NOT EXISTS chat_agent_status (
    agent_id INT PRIMARY KEY,
    status ENUM('online', 'away', 'busy', 'offline') DEFAULT 'offline',
    status_message VARCHAR(200) NULL,
    max_concurrent_chats INT DEFAULT 5,
    current_active_chats INT DEFAULT 0,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_id) REFERENCES admin_users(admin_id) ON DELETE CASCADE
);

-- Create chat session transfers table
CREATE TABLE IF NOT EXISTS chat_session_transfers (
    transfer_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL,
    from_agent_id INT NULL,
    to_agent_id INT NOT NULL,
    transfer_reason VARCHAR(500) NULL,
    transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(session_id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_transferred_at (transferred_at)
);

-- Create chat satisfaction surveys table
CREATE TABLE IF NOT EXISTS chat_satisfaction_surveys (
    survey_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL,
    user_id INT NOT NULL,
    agent_id INT NULL,
    overall_rating TINYINT NOT NULL CHECK (overall_rating BETWEEN 1 AND 5),
    response_time_rating TINYINT NULL CHECK (response_time_rating BETWEEN 1 AND 5),
    helpfulness_rating TINYINT NULL CHECK (helpfulness_rating BETWEEN 1 AND 5),
    professionalism_rating TINYINT NULL CHECK (professionalism_rating BETWEEN 1 AND 5),
    feedback TEXT NULL,
    would_recommend BOOLEAN NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_overall_rating (overall_rating),
    INDEX idx_submitted_at (submitted_at)
);

-- Create chat auto responses table
CREATE TABLE IF NOT EXISTS chat_auto_responses (
    auto_response_id INT AUTO_INCREMENT PRIMARY KEY,
    trigger_keywords JSON NOT NULL,
    response_message TEXT NOT NULL,
    response_type ENUM('exact_match', 'contains', 'starts_with', 'regex') DEFAULT 'contains',
    department_id INT NULL,
    priority INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES chat_departments(department_id) ON DELETE SET NULL,
    INDEX idx_priority (priority),
    INDEX idx_is_active (is_active)
);

-- Insert default auto responses
INSERT INTO chat_auto_responses (trigger_keywords, response_message, response_type) VALUES
('["order status", "track order", "where is my order"]', 'I can help you track your order. Please provide your order number and I\'ll check the status for you.', 'contains'),
('["cancel order", "cancel my order"]', 'I understand you want to cancel your order. Please provide your order number and I\'ll assist you with the cancellation process.', 'contains'),
('["refund", "return", "money back"]', 'I can help you with returns and refunds. Please provide your order details and the reason for the return.', 'contains'),
('["payment failed", "payment error", "payment problem"]', 'I\'m sorry to hear about the payment issue. Let me help you resolve this. Can you tell me more about what happened?', 'contains'),
('["shipping cost", "delivery fee", "shipping fee"]', 'Shipping costs depend on your location and the shipping method you choose. Free shipping is available for orders above Rp 100,000.', 'contains'),
('["product availability", "in stock", "stock"]', 'I can check product availability for you. Which product are you interested in?', 'contains');

-- Create chat file uploads table
CREATE TABLE IF NOT EXISTS chat_file_uploads (
    upload_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL,
    message_id INT NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (message_id) REFERENCES chat_messages(message_id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_uploaded_at (uploaded_at)
);

-- Add admin_users table modifications for chat support
ALTER TABLE admin_users 
ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255) NULL AFTER email,
ADD COLUMN IF NOT EXISTS status ENUM('online', 'away', 'busy', 'offline') DEFAULT 'offline' AFTER is_active,
ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP NULL AFTER status;

-- Create triggers to update chat statistics
DELIMITER //

CREATE TRIGGER IF NOT EXISTS update_agent_chat_count_insert
AFTER INSERT ON chat_sessions
FOR EACH ROW
BEGIN
    IF NEW.agent_id IS NOT NULL THEN
        INSERT INTO chat_agent_status (agent_id, current_active_chats) 
        VALUES (NEW.agent_id, 1)
        ON DUPLICATE KEY UPDATE current_active_chats = current_active_chats + 1;
    END IF;
END//

CREATE TRIGGER IF NOT EXISTS update_agent_chat_count_update
AFTER UPDATE ON chat_sessions
FOR EACH ROW
BEGIN
    -- Decrease count for old agent
    IF OLD.agent_id IS NOT NULL AND OLD.status = 'active' AND NEW.status != 'active' THEN
        UPDATE chat_agent_status 
        SET current_active_chats = GREATEST(0, current_active_chats - 1)
        WHERE agent_id = OLD.agent_id;
    END IF;
    
    -- Increase count for new agent
    IF NEW.agent_id IS NOT NULL AND NEW.status = 'active' AND OLD.status != 'active' THEN
        INSERT INTO chat_agent_status (agent_id, current_active_chats) 
        VALUES (NEW.agent_id, 1)
        ON DUPLICATE KEY UPDATE current_active_chats = current_active_chats + 1;
    END IF;
END//

DELIMITER ;

-- Create indexes for better performance
CREATE INDEX idx_chat_sessions_active ON chat_sessions(status, agent_id) WHERE status = 'active';
CREATE INDEX idx_chat_messages_unread ON chat_messages(session_id, is_read, sender_type);
CREATE INDEX idx_chat_agent_status_online ON chat_agent_status(status) WHERE status = 'online';

SELECT 'Live Chat system tables created successfully!' as status;
