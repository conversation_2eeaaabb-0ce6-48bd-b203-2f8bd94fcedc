/**
 * Wishlist Management JavaScript
 */

class WishlistManager {
    constructor() {
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateWishlistCount();
    }
    
    bindEvents() {
        // Select all checkbox
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }
        
        // Individual item checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('item-checkbox')) {
                this.updateBulkActionButtons();
            }
        });
        
        // Bulk action buttons
        const removeSelectedBtn = document.getElementById('removeSelected');
        if (removeSelectedBtn) {
            removeSelectedBtn.addEventListener('click', () => {
                this.bulkRemoveItems();
            });
        }
        
        const moveToCartBtn = document.getElementById('moveToCart');
        if (moveToCartBtn) {
            moveToCartBtn.addEventListener('click', () => {
                this.bulkMoveToCart();
            });
        }
        
        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-to-cart-btn') || 
                e.target.closest('.add-to-cart-btn')) {
                const btn = e.target.classList.contains('add-to-cart-btn') ? 
                           e.target : e.target.closest('.add-to-cart-btn');
                this.addToCart(btn);
            }
        });
    }
    
    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
        this.updateBulkActionButtons();
    }
    
    updateBulkActionButtons() {
        const selectedItems = document.querySelectorAll('.item-checkbox:checked');
        const removeBtn = document.getElementById('removeSelected');
        const moveBtn = document.getElementById('moveToCart');
        
        const hasSelection = selectedItems.length > 0;
        
        if (removeBtn) {
            removeBtn.disabled = !hasSelection;
            removeBtn.textContent = hasSelection ? 
                `Remove Selected (${selectedItems.length})` : 'Remove Selected';
        }
        
        if (moveBtn) {
            moveBtn.disabled = !hasSelection;
            moveBtn.textContent = hasSelection ? 
                `Move to Cart (${selectedItems.length})` : 'Move to Cart';
        }
        
        // Update select all checkbox state
        const selectAllCheckbox = document.getElementById('selectAll');
        const allCheckboxes = document.querySelectorAll('.item-checkbox');
        
        if (selectAllCheckbox && allCheckboxes.length > 0) {
            selectAllCheckbox.checked = selectedItems.length === allCheckboxes.length;
            selectAllCheckbox.indeterminate = selectedItems.length > 0 && 
                                            selectedItems.length < allCheckboxes.length;
        }
    }
    
    async addToWishlist(productId, notes = '', priority = 'medium') {
        try {
            const formData = new FormData();
            formData.append('action', 'add');
            formData.append('product_id', productId);
            formData.append('notes', notes);
            formData.append('priority', priority);
            
            const response = await fetch('ajax/wishlist_actions.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showMessage('Product added to wishlist!', 'success');
                this.updateWishlistCount(data.wishlist_count);
                this.updateWishlistButton(productId, true);
            } else {
                this.showMessage(data.message || 'Failed to add to wishlist', 'error');
            }
            
            return data;
        } catch (error) {
            console.error('Error adding to wishlist:', error);
            this.showMessage('Error adding to wishlist', 'error');
            return { success: false };
        }
    }
    
    async removeFromWishlist(productId) {
        if (!confirm('Remove this item from your wishlist?')) {
            return;
        }
        
        try {
            const formData = new FormData();
            formData.append('action', 'remove');
            formData.append('product_id', productId);
            
            const response = await fetch('ajax/wishlist_actions.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showMessage('Product removed from wishlist', 'success');
                this.updateWishlistCount(data.wishlist_count);
                this.removeWishlistItem(productId);
                this.updateWishlistButton(productId, false);
            } else {
                this.showMessage(data.message || 'Failed to remove from wishlist', 'error');
            }
            
            return data;
        } catch (error) {
            console.error('Error removing from wishlist:', error);
            this.showMessage('Error removing from wishlist', 'error');
            return { success: false };
        }
    }
    
    async bulkRemoveItems() {
        const selectedItems = Array.from(document.querySelectorAll('.item-checkbox:checked'))
                                  .map(checkbox => checkbox.value);
        
        if (selectedItems.length === 0) {
            this.showMessage('No items selected', 'warning');
            return;
        }
        
        if (!confirm(`Remove ${selectedItems.length} items from your wishlist?`)) {
            return;
        }
        
        try {
            const formData = new FormData();
            formData.append('action', 'bulk_remove');
            formData.append('product_ids', JSON.stringify(selectedItems));
            
            const response = await fetch('ajax/wishlist_actions.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showMessage(data.message, 'success');
                this.updateWishlistCount(data.wishlist_count);
                
                // Remove items from DOM
                selectedItems.forEach(productId => {
                    this.removeWishlistItem(productId);
                });
                
                this.updateBulkActionButtons();
            } else {
                this.showMessage(data.message || 'Failed to remove items', 'error');
            }
        } catch (error) {
            console.error('Error removing items:', error);
            this.showMessage('Error removing items', 'error');
        }
    }
    
    async bulkMoveToCart() {
        const selectedItems = Array.from(document.querySelectorAll('.item-checkbox:checked'))
                                  .map(checkbox => checkbox.value);
        
        if (selectedItems.length === 0) {
            this.showMessage('No items selected', 'warning');
            return;
        }
        
        // For now, just show a message since cart integration needs to be implemented
        this.showMessage('Move to cart feature coming soon!', 'info');
    }
    
    async addToCart(button) {
        const productId = button.dataset.productId;
        const productName = button.dataset.productName;
        
        // Store original button content
        const originalContent = button.innerHTML;
        
        // Show loading state
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Adding...';
        button.disabled = true;
        
        try {
            // Use existing add to cart functionality
            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('quantity', 1);
            
            const response = await fetch('ajax/add_to_cart.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showMessage(`${productName} added to cart!`, 'success');
                // Optionally remove from wishlist after adding to cart
                // this.removeFromWishlist(productId);
            } else {
                this.showMessage(data.message || 'Failed to add to cart', 'error');
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            this.showMessage('Error adding to cart', 'error');
        } finally {
            // Restore button
            button.innerHTML = originalContent;
            button.disabled = false;
        }
    }
    
    removeWishlistItem(productId) {
        const itemCard = document.querySelector(`[data-product-id="${productId}"]`);
        if (itemCard) {
            itemCard.style.transition = 'all 0.3s ease';
            itemCard.style.transform = 'scale(0.8)';
            itemCard.style.opacity = '0';
            
            setTimeout(() => {
                itemCard.remove();
                
                // Check if wishlist is now empty
                const remainingItems = document.querySelectorAll('#wishlistItems .wishlist-card');
                if (remainingItems.length === 0) {
                    location.reload(); // Reload to show empty state
                }
            }, 300);
        }
    }
    
    updateWishlistButton(productId, inWishlist) {
        // Update wishlist buttons on product pages
        const wishlistBtns = document.querySelectorAll(`[data-product-id="${productId}"].wishlist-btn`);
        wishlistBtns.forEach(btn => {
            if (inWishlist) {
                btn.classList.add('active');
                btn.innerHTML = '<i class="fas fa-heart"></i>';
                btn.title = 'Remove from wishlist';
            } else {
                btn.classList.remove('active');
                btn.innerHTML = '<i class="far fa-heart"></i>';
                btn.title = 'Add to wishlist';
            }
        });
    }
    
    async updateWishlistCount(count = null) {
        if (count === null) {
            try {
                const response = await fetch('ajax/wishlist_actions.php?action=get_count');
                const data = await response.json();
                count = data.success ? data.count : 0;
            } catch (error) {
                console.error('Error getting wishlist count:', error);
                return;
            }
        }
        
        // Update wishlist count in header/navbar
        const countElements = document.querySelectorAll('.wishlist-count');
        countElements.forEach(element => {
            element.textContent = count;
            element.style.display = count > 0 ? 'inline' : 'none';
        });
    }
    
    showMessage(message, type = 'info') {
        // Use existing notification system if available
        if (typeof showAlert === 'function') {
            showAlert(type === 'error' ? 'danger' : type, message);
        } else {
            // Fallback to simple alert
            alert(message);
        }
    }
}

// Global functions for use in HTML
window.removeFromWishlist = function(productId) {
    if (window.wishlistManager) {
        window.wishlistManager.removeFromWishlist(productId);
    }
};

window.addToWishlist = function(productId, notes = '', priority = 'medium') {
    if (window.wishlistManager) {
        return window.wishlistManager.addToWishlist(productId, notes, priority);
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.wishlistManager = new WishlistManager();
});

// Export for use in other scripts
window.WishlistManager = WishlistManager;
