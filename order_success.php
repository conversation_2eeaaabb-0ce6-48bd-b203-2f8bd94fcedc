<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$page = 'order_success';
$page_title = '<PERSON>esan<PERSON> Be<PERSON>hasil';

require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

$user_id = $_SESSION['user_id'];
$order_id = $_GET['order_id'] ?? null;

if (!$order_id) {
    header('Location: index.php');
    exit();
}

// Get order details
$stmt = $conn->prepare("
    SELECT o.*, 
           COALESCE(o.order_number, CONCAT('ORD-', LPAD(o.order_id, 6, '0'))) as display_order_number
    FROM orders o 
    WHERE o.order_id = ? AND o.user_id = ?
");
$stmt->execute([$order_id, $user_id]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$order) {
    header('Location: index.php');
    exit();
}

// Get order items
$stmt = $conn->prepare("
    SELECT oi.*, p.name, p.image
    FROM order_items oi
    JOIN products p ON oi.product_id = p.product_id
    WHERE oi.order_id = ?
");
$stmt->execute([$order_id]);
$order_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Create welcome notification for new orders
createSystemNotification($user_id, 'Terima Kasih!', "Terima kasih telah berbelanja di TeWuNeed. Pesanan #{$order['display_order_number']} akan segera diproses.", 'medium');

include 'includes/header.php';
?>

<style>
.success-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    text-align: center;
}

.success-icon {
    font-size: 5rem;
    color: #10b981;
    margin-bottom: 20px;
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.success-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.order-summary {
    background: #f8fafc;
    border-radius: 12px;
    padding: 25px;
    margin: 20px 0;
    text-align: left;
}

.order-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e5e7eb;
}

.order-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    margin-right: 15px;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
}

.item-price {
    color: #6b7280;
    font-size: 0.9rem;
}

.payment-info {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border-left: 4px solid #3b82f6;
}

.next-steps {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: left;
}

.step-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.step-number {
    background: #3b82f6;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
}

.step-desc {
    color: #6b7280;
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.btn-primary-custom {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
    text-decoration: none;
}

.btn-secondary-custom {
    background: white;
    color: #6b7280;
    border: 2px solid #e5e7eb;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-secondary-custom:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    text-decoration: none;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .order-item {
        flex-direction: column;
        text-align: center;
    }
    
    .item-image {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
</style>

<div class="success-container">
    <div class="success-card">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        
        <h1 class="h2 mb-3">Pesanan Berhasil Dibuat!</h1>
        <p class="text-muted mb-4">
            Terima kasih telah berbelanja di TeWuNeed. Pesanan Anda telah berhasil dibuat dan akan segera diproses.
        </p>
        
        <div class="payment-info">
            <h4 class="mb-3">
                <i class="fas fa-receipt me-2"></i>Detail Pesanan
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <strong>Nomor Pesanan:</strong><br>
                    <span class="text-primary">#<?php echo $order['display_order_number']; ?></span>
                </div>
                <div class="col-md-6">
                    <strong>Total Pembayaran:</strong><br>
                    <span class="text-success h5">Rp <?php echo number_format($order['total_amount'], 0, ',', '.'); ?></span>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <strong>Metode Pembayaran:</strong><br>
                    <?php
                    $payment_methods = [
                        'bank_transfer' => 'Transfer Bank',
                        'e_wallet' => 'E-Wallet',
                        'credit_card' => 'Kartu Kredit',
                        'cod' => 'Bayar di Tempat'
                    ];
                    echo $payment_methods[$order['payment_method']] ?? ucfirst(str_replace('_', ' ', $order['payment_method']));
                    ?>
                </div>
                <div class="col-md-6">
                    <strong>Status:</strong><br>
                    <span class="badge bg-warning">Menunggu Pembayaran</span>
                </div>
            </div>
        </div>
        
        <div class="order-summary">
            <h5 class="mb-3">
                <i class="fas fa-shopping-bag me-2"></i>Item Pesanan
            </h5>
            
            <?php foreach ($order_items as $item): ?>
                <div class="order-item">
                    <img src="<?php echo !empty($item['image']) ? 'uploads/' . $item['image'] : 'Images/default-product.jpg'; ?>" 
                         alt="<?php echo htmlspecialchars($item['name']); ?>" class="item-image">
                    <div class="item-details">
                        <div class="item-name"><?php echo htmlspecialchars($item['name']); ?></div>
                        <div class="item-price">
                            <?php echo $item['quantity']; ?> × Rp <?php echo number_format($item['unit_price'], 0, ',', '.'); ?>
                        </div>
                    </div>
                    <div class="item-total">
                        <strong>Rp <?php echo number_format($item['total_price'], 0, ',', '.'); ?></strong>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="next-steps">
        <h4 class="mb-4">
            <i class="fas fa-list-ol me-2"></i>Langkah Selanjutnya
        </h4>
        
        <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
                <div class="step-title">Cek Notifikasi</div>
                <div class="step-desc">Pantau status pesanan Anda melalui halaman notifikasi</div>
            </div>
        </div>
        
        <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
                <div class="step-title">Lakukan Pembayaran</div>
                <div class="step-desc">Ikuti instruksi pembayaran yang telah dikirim ke notifikasi Anda</div>
            </div>
        </div>
        
        <div class="step-item">
            <div class="step-number">3</div>
            <div class="step-content">
                <div class="step-title">Tunggu Konfirmasi</div>
                <div class="step-desc">Pesanan akan diproses setelah pembayaran dikonfirmasi</div>
            </div>
        </div>
        
        <div class="step-item">
            <div class="step-number">4</div>
            <div class="step-content">
                <div class="step-title">Terima Pesanan</div>
                <div class="step-desc">Pesanan akan dikirim ke alamat yang Anda berikan</div>
            </div>
        </div>
    </div>
    
    <div class="action-buttons">
        <a href="notifications.php" class="btn-primary-custom">
            <i class="fas fa-bell me-2"></i>Lihat Notifikasi
        </a>
        <a href="products_public.php" class="btn-secondary-custom">
            <i class="fas fa-shopping-bag me-2"></i>Lanjut Belanja
        </a>
        <a href="index.php" class="btn-secondary-custom">
            <i class="fas fa-home me-2"></i>Kembali ke Beranda
        </a>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
