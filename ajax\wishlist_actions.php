<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/WishlistManager.php';
require_once '../includes/firebase_auth.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => 'Please login to manage your wishlist'
    ]);
    exit;
}

$user_id = $_SESSION['user_id'] ?? $_SESSION['local_user_id'] ?? null;

if (!$user_id) {
    echo json_encode([
        'success' => false,
        'message' => 'User ID not found'
    ]);
    exit;
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';
$wishlistManager = new WishlistManager();

try {
    switch ($action) {
        case 'add':
            $product_id = (int)($_POST['product_id'] ?? 0);
            $notes = $_POST['notes'] ?? '';
            $priority = $_POST['priority'] ?? 'medium';
            
            if (!$product_id) {
                throw new Exception('Product ID is required');
            }
            
            $result = $wishlistManager->addToWishlist($user_id, $product_id, $notes, $priority);
            
            if ($result['success']) {
                $result['wishlist_count'] = $wishlistManager->getWishlistCount($user_id);
            }
            
            echo json_encode($result);
            break;
            
        case 'remove':
            $product_id = (int)($_POST['product_id'] ?? $_GET['product_id'] ?? 0);
            
            if (!$product_id) {
                throw new Exception('Product ID is required');
            }
            
            $result = $wishlistManager->removeFromWishlist($user_id, $product_id);
            
            if ($result['success']) {
                $result['wishlist_count'] = $wishlistManager->getWishlistCount($user_id);
            }
            
            echo json_encode($result);
            break;
            
        case 'check':
            $product_id = (int)($_GET['product_id'] ?? 0);
            
            if (!$product_id) {
                throw new Exception('Product ID is required');
            }
            
            $is_in_wishlist = $wishlistManager->isInWishlist($user_id, $product_id);
            
            echo json_encode([
                'success' => true,
                'in_wishlist' => $is_in_wishlist
            ]);
            break;
            
        case 'get_list':
            $collection_id = $_GET['collection_id'] ?? null;
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 12);
            $offset = ($page - 1) * $limit;
            
            $items = $wishlistManager->getUserWishlist($user_id, $collection_id, $limit, $offset);
            $total_count = $wishlistManager->getWishlistCount($user_id);
            
            echo json_encode([
                'success' => true,
                'items' => $items,
                'total_count' => $total_count,
                'page' => $page,
                'total_pages' => ceil($total_count / $limit)
            ]);
            break;
            
        case 'get_count':
            $count = $wishlistManager->getWishlistCount($user_id);
            
            echo json_encode([
                'success' => true,
                'count' => $count
            ]);
            break;
            
        case 'create_collection':
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $is_public = isset($_POST['is_public']) ? (bool)$_POST['is_public'] : false;
            
            if (empty($name)) {
                throw new Exception('Collection name is required');
            }
            
            $result = $wishlistManager->createCollection($user_id, $name, $description, $is_public);
            echo json_encode($result);
            break;
            
        case 'get_collections':
            $collections = $wishlistManager->getUserCollections($user_id);
            
            echo json_encode([
                'success' => true,
                'collections' => $collections
            ]);
            break;
            
        case 'move_to_cart':
            // This would integrate with your existing cart system
            // For now, just return success
            echo json_encode([
                'success' => true,
                'message' => 'Feature coming soon'
            ]);
            break;
            
        case 'bulk_remove':
            $product_ids = $_POST['product_ids'] ?? [];
            
            if (!is_array($product_ids) || empty($product_ids)) {
                throw new Exception('No products selected');
            }
            
            $removed_count = 0;
            foreach ($product_ids as $product_id) {
                $result = $wishlistManager->removeFromWishlist($user_id, (int)$product_id);
                if ($result['success']) {
                    $removed_count++;
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => "$removed_count items removed from wishlist",
                'wishlist_count' => $wishlistManager->getWishlistCount($user_id)
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
