<?php
/**
 * First Time Notification System Cleanup
 * This script will clean up the notification system and prepare it for the enhanced features
 */

session_start();
require_once 'includes/db_connect.php';
require_once 'includes/enhanced_notification_functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$cleanup_results = [];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification System Cleanup - TeWuNeed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .cleanup-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .cleanup-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="display-5 mb-3">
                        <i class="fas fa-broom text-primary"></i>
                        Notification System Cleanup
                    </h1>
                    <p class="lead text-muted">
                        Let's clean up and optimize your notification system for the best experience!
                    </p>
                </div>

                <!-- Cleanup Steps -->
                <div class="row">
                    <!-- Step 1: Check Current Status -->
                    <div class="col-md-6 mb-4">
                        <div class="card cleanup-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="step-number bg-primary me-3">1</div>
                                    <h5 class="mb-0">Check Current Status</h5>
                                </div>
                                
                                <?php
                                try {
                                    $stats = getNotificationStats($conn, $user_id);
                                    $cleanup_results['current_stats'] = $stats;
                                    ?>
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-chart-bar me-2"></i>Current Statistics:</h6>
                                        <ul class="mb-0">
                                            <li><strong>Total:</strong> <?php echo $stats['total']; ?> notifications</li>
                                            <li><strong>Unread:</strong> <?php echo $stats['unread']; ?> notifications</li>
                                            <li><strong>Order notifications:</strong> <?php echo $stats['order_notifications']; ?></li>
                                            <li><strong>General notifications:</strong> <?php echo $stats['general_notifications']; ?></li>
                                        </ul>
                                    </div>
                                    <div class="text-success">
                                        <i class="fas fa-check-circle me-2"></i>Status check completed
                                    </div>
                                    <?php
                                } catch (Exception $e) {
                                    echo '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>Error checking status: ' . $e->getMessage() . '</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Create Welcome Notification -->
                    <div class="col-md-6 mb-4">
                        <div class="card cleanup-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="step-number bg-success me-3">2</div>
                                    <h5 class="mb-0">Create Welcome Message</h5>
                                </div>
                                
                                <?php
                                try {
                                    $welcome_created = createWelcomeNotification($conn, $user_id);
                                    $cleanup_results['welcome_created'] = $welcome_created;
                                    
                                    if ($welcome_created) {
                                        echo '<div class="alert alert-success">';
                                        echo '<i class="fas fa-heart me-2"></i>Welcome notification created successfully!';
                                        echo '</div>';
                                        echo '<div class="text-success">';
                                        echo '<i class="fas fa-check-circle me-2"></i>Welcome message ready';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="alert alert-info">';
                                        echo '<i class="fas fa-info-circle me-2"></i>Welcome notification already exists';
                                        echo '</div>';
                                        echo '<div class="text-success">';
                                        echo '<i class="fas fa-check-circle me-2"></i>Welcome message verified';
                                        echo '</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>Error creating welcome: ' . $e->getMessage() . '</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Clean Old Notifications -->
                    <div class="col-md-6 mb-4">
                        <div class="card cleanup-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="step-number bg-warning me-3">3</div>
                                    <h5 class="mb-0">Clean Old Notifications</h5>
                                </div>
                                
                                <?php
                                try {
                                    $cleaned_count = cleanupOldNotifications($conn, 30);
                                    $cleanup_results['cleaned_count'] = $cleaned_count;
                                    
                                    if ($cleaned_count > 0) {
                                        echo '<div class="alert alert-warning">';
                                        echo '<i class="fas fa-broom me-2"></i>Cleaned up ' . $cleaned_count . ' old notifications';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="alert alert-info">';
                                        echo '<i class="fas fa-info-circle me-2"></i>No old notifications to clean';
                                        echo '</div>';
                                    }
                                    
                                    echo '<div class="text-success">';
                                    echo '<i class="fas fa-check-circle me-2"></i>Cleanup completed';
                                    echo '</div>';
                                } catch (Exception $e) {
                                    echo '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>Error during cleanup: ' . $e->getMessage() . '</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Final Status -->
                    <div class="col-md-6 mb-4">
                        <div class="card cleanup-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="step-number bg-info me-3">4</div>
                                    <h5 class="mb-0">Final Status</h5>
                                </div>
                                
                                <?php
                                try {
                                    $final_stats = getNotificationStats($conn, $user_id);
                                    $cleanup_results['final_stats'] = $final_stats;
                                    ?>
                                    <div class="alert alert-success">
                                        <h6><i class="fas fa-chart-line me-2"></i>Updated Statistics:</h6>
                                        <ul class="mb-0">
                                            <li><strong>Total:</strong> <?php echo $final_stats['total']; ?> notifications</li>
                                            <li><strong>Unread:</strong> <?php echo $final_stats['unread']; ?> notifications</li>
                                            <li><strong>Today:</strong> <?php echo $final_stats['today']; ?> notifications</li>
                                        </ul>
                                    </div>
                                    <div class="text-success">
                                        <i class="fas fa-check-circle me-2"></i>System optimized
                                    </div>
                                    <?php
                                } catch (Exception $e) {
                                    echo '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>Error getting final status: ' . $e->getMessage() . '</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary -->
                <div class="card cleanup-card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>Cleanup Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>✅ Completed Tasks:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>System status checked</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Welcome notification prepared</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Old notifications cleaned</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Enhanced features activated</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>🚀 New Features Available:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-star text-warning me-2"></i>Mark all as read</li>
                                    <li><i class="fas fa-star text-warning me-2"></i>Individual notification actions</li>
                                    <li><i class="fas fa-star text-warning me-2"></i>Real-time updates</li>
                                    <li><i class="fas fa-star text-warning me-2"></i>Automatic cleanup</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center">
                    <a href="user_notifications.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-bell me-2"></i>View Enhanced Notifications
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                </div>

                <!-- Progress Indicator -->
                <div class="mt-4">
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 100%"></div>
                    </div>
                    <p class="text-center mt-2 text-success">
                        <i class="fas fa-check-circle me-1"></i>
                        <strong>Cleanup completed successfully!</strong>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-redirect after 10 seconds
        setTimeout(function() {
            const redirect = confirm('Cleanup completed! Would you like to view your enhanced notifications now?');
            if (redirect) {
                window.location.href = 'user_notifications.php';
            }
        }, 5000);

        // Add some animation effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.cleanup-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
