<?php
/**
 * Test Product Detail Page
 * Quick test to verify all functionality is working
 */

session_start();
require_once 'includes/db_connect.php';

// For testing, let's use a sample product ID
$test_product_id = 1;

// Check if we have any products
try {
    $stmt = $conn->prepare("SELECT product_id FROM products LIMIT 1");
    $stmt->execute();
    $first_product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($first_product) {
        $test_product_id = $first_product['product_id'];
    }
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
    exit;
}

// Redirect to actual product detail page
header("Location: product-detail.php?id=" . $test_product_id);
exit;
?>
