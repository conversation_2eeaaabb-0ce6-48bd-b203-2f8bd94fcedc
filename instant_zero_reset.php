<?php
/**
 * Instant Zero Reset - Make All Notifications = 0
 * This will immediately reset all notification counts to zero
 */

session_start();
require_once 'includes/db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    die("Please login first. <a href='login.php'>Login here</a>");
}

$user_id = $_SESSION['user_id'];

echo "<h1>🔄 Instant Zero Reset</h1>";
echo "<p>User ID: <strong>{$user_id}</strong></p>";

$results = [];
$total_affected = 0;

try {
    echo "<h3>📊 Before Reset:</h3>";
    
    // Check current counts
    $stmt = $conn->prepare("SELECT COUNT(*) as total, SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread FROM order_notifications WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $order_before = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Order notifications: {$order_before['total']} total, {$order_before['unread']} unread</p>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) as total, SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread FROM notifications WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $general_before = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>General notifications: {$general_before['total']} total, {$general_before['unread']} unread</p>";
    
    echo "<hr>";
    echo "<h3>🗑️ Resetting to Zero...</h3>";
    
    // Method 1: Delete all notifications (makes everything 0)
    $stmt = $conn->prepare("DELETE FROM order_notifications WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $deleted1 = $stmt->rowCount();
    echo "<p>✅ Deleted {$deleted1} order notifications</p>";
    $total_affected += $deleted1;
    
    $stmt = $conn->prepare("DELETE FROM notifications WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $deleted2 = $stmt->rowCount();
    echo "<p>✅ Deleted {$deleted2} general notifications</p>";
    $total_affected += $deleted2;
    
    // Clear session notifications
    $session_keys = ['error', 'error_message', 'success_message', 'notification', 'flash_message', 'cart_error', 'stock_error'];
    $cleared_sessions = 0;
    foreach ($session_keys as $key) {
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
            $cleared_sessions++;
        }
    }
    echo "<p>✅ Cleared {$cleared_sessions} session notifications</p>";
    
    // Clear notification cookies
    $cookie_keys = ['notification', 'error_message', 'success_message', 'cart_notification'];
    $cleared_cookies = 0;
    foreach ($cookie_keys as $key) {
        if (isset($_COOKIE[$key])) {
            setcookie($key, '', time() - 3600, '/');
            $cleared_cookies++;
        }
    }
    echo "<p>✅ Cleared {$cleared_cookies} notification cookies</p>";
    
    echo "<hr>";
    echo "<h3>📊 After Reset:</h3>";
    
    // Check final counts
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM order_notifications WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $order_after = $stmt->fetchColumn();
    echo "<p>Order notifications: <strong>{$order_after}</strong></p>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM notifications WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $general_after = $stmt->fetchColumn();
    echo "<p>General notifications: <strong>{$general_after}</strong></p>";
    
    echo "<hr>";
    echo "<div style='background: lightgreen; padding: 20px; border-radius: 10px; text-align: center;'>";
    echo "<h2>🎉 SUCCESS!</h2>";
    echo "<h1 style='font-size: 4rem; color: green; margin: 20px 0;'>0</h1>";
    echo "<p><strong>All notifications have been reset to ZERO!</strong></p>";
    echo "<p>Total items processed: <strong>{$total_affected}</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: lightcoral; padding: 20px; border-radius: 10px;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 Quick Actions:</h3>";
echo "<a href='user_notifications.php' style='background: blue; color: white; padding: 10px 20px; text-decoration: none; margin: 5px; border-radius: 5px;'>📱 Check Notifications</a>";
echo "<a href='index.php' style='background: green; color: white; padding: 10px 20px; text-decoration: none; margin: 5px; border-radius: 5px;'>🏠 Home</a>";
echo "<a href='instant_zero_reset.php' style='background: orange; color: white; padding: 10px 20px; text-decoration: none; margin: 5px; border-radius: 5px;'>🔄 Run Again</a>";

echo "<hr>";
echo "<h3>🧪 Test Header Badge:</h3>";
echo "<button onclick='testHeaderUpdate()' style='background: purple; color: white; padding: 15px; border: none; border-radius: 5px; cursor: pointer;'>🔄 Update Header Badge</button>";

echo "<script>";
echo "function testHeaderUpdate() {";
echo "  const badge = document.getElementById('headerNotificationBadge');";
echo "  if (badge) {";
echo "    badge.style.display = 'none';";
echo "    badge.textContent = '0';";
echo "    alert('✅ Header badge updated! Should show 0 or be hidden.');";
echo "  } else {";
echo "    alert('ℹ️ No header badge found. You might not be on a page with the header.');";
echo "  }";
echo "}";

echo "// Auto-hide badge after 2 seconds";
echo "setTimeout(function() {";
echo "  const badge = document.getElementById('headerNotificationBadge');";
echo "  if (badge) {";
echo "    badge.style.display = 'none';";
echo "    console.log('Header badge hidden automatically');";
echo "  }";
echo "}, 2000);";
echo "</script>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }";
echo "h1, h2, h3 { color: #333; }";
echo "p { margin: 10px 0; }";
echo "hr { margin: 20px 0; border: 1px solid #ddd; }";
echo "</style>";
?>
