<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Functionality Test - TeWuNeed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">🛒 Cart Functionality Test</h1>
                
                <!-- Test Status -->
                <div id="test-status" class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Ready to test cart functionality...
                </div>

                <!-- Test Product Cards -->
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card product-card" data-product-id="1">
                            <img src="uploads/1.jpg" class="card-img-top" alt="Test Product 1" style="height: 200px; object-fit: cover;">
                            <div class="card-body">
                                <h5 class="card-title">Test Product 1</h5>
                                <p class="card-text">This is a test product for cart functionality.</p>
                                <p class="h5 text-primary">Rp 50,000</p>
                                
                                <!-- Quantity Controls -->
                                <div class="mb-3">
                                    <label class="form-label">Quantity:</label>
                                    <div class="input-group" style="max-width: 150px;">
                                        <button class="btn btn-outline-secondary quantity-minus" type="button">-</button>
                                        <input type="number" class="form-control text-center quantity-input" value="1" min="1" max="10">
                                        <button class="btn btn-outline-secondary quantity-plus" type="button">+</button>
                                    </div>
                                </div>
                                
                                <button class="btn btn-primary add-to-cart-btn" data-product-id="1">
                                    <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card product-card" data-product-id="2">
                            <img src="uploads/2.jpg" class="card-img-top" alt="Test Product 2" style="height: 200px; object-fit: cover;">
                            <div class="card-body">
                                <h5 class="card-title">Test Product 2</h5>
                                <p class="card-text">Another test product for cart functionality.</p>
                                <p class="h5 text-primary">Rp 75,000</p>
                                
                                <!-- Quantity Controls -->
                                <div class="mb-3">
                                    <label class="form-label">Quantity:</label>
                                    <div class="input-group" style="max-width: 150px;">
                                        <button class="btn btn-outline-secondary quantity-minus" type="button">-</button>
                                        <input type="number" class="form-control text-center quantity-input" value="1" min="1" max="10">
                                        <button class="btn btn-outline-secondary quantity-plus" type="button">+</button>
                                    </div>
                                </div>
                                
                                <button class="btn btn-primary add-to-cart-btn" data-product-id="2">
                                    <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card product-card" data-product-id="3">
                            <img src="uploads/3.jpg" class="card-img-top" alt="Test Product 3" style="height: 200px; object-fit: cover;">
                            <div class="card-body">
                                <h5 class="card-title">Test Product 3</h5>
                                <p class="card-text">Third test product for cart functionality.</p>
                                <p class="h5 text-primary">Rp 100,000</p>
                                
                                <!-- Quantity Controls -->
                                <div class="mb-3">
                                    <label class="form-label">Quantity:</label>
                                    <div class="input-group" style="max-width: 150px;">
                                        <button class="btn btn-outline-secondary quantity-minus" type="button">-</button>
                                        <input type="number" class="form-control text-center quantity-input" value="1" min="1" max="10">
                                        <button class="btn btn-outline-secondary quantity-plus" type="button">+</button>
                                    </div>
                                </div>
                                
                                <button class="btn btn-primary add-to-cart-btn" data-product-id="3">
                                    <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Controls -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Manual Tests:</h6>
                                <button class="btn btn-info me-2 mb-2" onclick="testCartCount()">
                                    <i class="fas fa-calculator me-1"></i>Test Cart Count
                                </button>
                                <button class="btn btn-warning me-2 mb-2" onclick="testAddToCart(1)">
                                    <i class="fas fa-plus me-1"></i>Test Add Product 1
                                </button>
                                <button class="btn btn-success me-2 mb-2" onclick="runAllTests()">
                                    <i class="fas fa-play me-1"></i>Run All Tests
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>Current Cart Count:</h6>
                                <div class="alert alert-light">
                                    <span id="cart-count-display" class="h4">0</span> items
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-clipboard-list me-2"></i>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <p class="text-muted">No tests run yet...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load Cart Fix JavaScript -->
    <script src="assets/js/cart-fix.js"></script>
    
    <script>
        // Test functions
        function updateTestStatus(message, type = 'info') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.className = `alert alert-${type}`;
            statusDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'danger' ? 'times' : 'info'}-circle me-2"></i>${message}`;
        }

        function addTestResult(test, result, details = '') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const icon = result === 'PASS' ? 'check text-success' : 'times text-danger';
            
            if (resultsDiv.innerHTML.includes('No tests run yet')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.innerHTML += `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-${icon} me-2"></i><strong>${test}</strong></span>
                        <small class="text-muted">${timestamp}</small>
                    </div>
                    <div class="text-muted small">${details}</div>
                </div>
            `;
        }

        async function testCartCount() {
            updateTestStatus('Testing cart count...', 'info');
            
            try {
                const response = await fetch('ajax/get_cart_count.php');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('cart-count-display').textContent = data.cart_count;
                    addTestResult('Cart Count Test', 'PASS', `Cart count: ${data.cart_count}`);
                    updateTestStatus('Cart count test passed!', 'success');
                } else {
                    addTestResult('Cart Count Test', 'FAIL', `Error: ${data.message || 'Unknown error'}`);
                    updateTestStatus('Cart count test failed!', 'danger');
                }
            } catch (error) {
                addTestResult('Cart Count Test', 'FAIL', `Network error: ${error.message}`);
                updateTestStatus('Cart count test failed!', 'danger');
            }
        }

        async function testAddToCart(productId) {
            updateTestStatus(`Testing add to cart for product ${productId}...`, 'info');
            
            try {
                const formData = new FormData();
                formData.append('product_id', productId);
                formData.append('quantity', 1);

                const response = await fetch('ajax/add_to_cart.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (data.success) {
                    addTestResult(`Add to Cart Test (Product ${productId})`, 'PASS', 
                        `Product added successfully. Cart count: ${data.cart_count}`);
                    updateTestStatus('Add to cart test passed!', 'success');
                    
                    // Update cart count display
                    document.getElementById('cart-count-display').textContent = data.cart_count;
                } else {
                    addTestResult(`Add to Cart Test (Product ${productId})`, 'FAIL', 
                        `Error: ${data.message || 'Unknown error'}`);
                    updateTestStatus('Add to cart test failed!', 'danger');
                }
            } catch (error) {
                addTestResult(`Add to Cart Test (Product ${productId})`, 'FAIL', 
                    `Network error: ${error.message}`);
                updateTestStatus('Add to cart test failed!', 'danger');
            }
        }

        async function runAllTests() {
            updateTestStatus('Running all tests...', 'info');
            
            // Clear previous results
            document.getElementById('test-results').innerHTML = '<p class="text-muted">Running tests...</p>';
            
            // Test cart count
            await testCartCount();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Test add to cart for each product
            for (let i = 1; i <= 3; i++) {
                await testAddToCart(i);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // Final cart count test
            await testCartCount();
            
            updateTestStatus('All tests completed!', 'success');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateTestStatus('Cart functionality test page loaded. Ready to test!', 'success');
            
            // Test cart count on load
            setTimeout(testCartCount, 1000);
        });
    </script>
</body>
</html>
