/**
 * Performance Optimizer for TeWuNeed
 * Handles lazy loading, image optimization, and performance monitoring
 */

class PerformanceOptimizer {
    constructor() {
        this.imageObserver = null;
        this.performanceMetrics = {};
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.optimizeImages();
        this.setupPerformanceMonitoring();
        this.optimizeScrolling();
        this.setupRequestOptimization();
    }

    /**
     * Setup lazy loading for images
     */
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.imageObserver.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });

            // Observe all lazy images
            document.querySelectorAll('img[data-src]').forEach(img => {
                this.imageObserver.observe(img);
            });
        } else {
            // Fallback for older browsers
            this.loadAllImages();
        }
    }

    /**
     * Load individual image
     */
    loadImage(img) {
        const src = img.dataset.src;
        if (!src) return;

        // Create new image to preload
        const imageLoader = new Image();
        
        imageLoader.onload = () => {
            // Fade in effect
            img.style.opacity = '0';
            img.src = src;
            img.dataset.loaded = 'true';
            
            requestAnimationFrame(() => {
                img.style.transition = 'opacity 0.3s ease';
                img.style.opacity = '1';
            });
            
            // Remove placeholder
            const placeholder = img.parentElement.querySelector('.image-placeholder');
            if (placeholder) {
                placeholder.remove();
            }
        };

        imageLoader.onerror = () => {
            // Handle error - show default image
            img.src = 'assets/img/product-default.jpg';
            img.dataset.loaded = 'error';
        };

        imageLoader.src = src;
    }

    /**
     * Load all images (fallback)
     */
    loadAllImages() {
        document.querySelectorAll('img[data-src]').forEach(img => {
            this.loadImage(img);
        });
    }

    /**
     * Optimize existing images
     */
    optimizeImages() {
        document.querySelectorAll('img').forEach(img => {
            // Add loading attribute for native lazy loading
            if (!img.hasAttribute('loading')) {
                img.loading = 'lazy';
            }
            
            // Add decoding attribute
            if (!img.hasAttribute('decoding')) {
                img.decoding = 'async';
            }
        });
    }

    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        // Monitor page load performance
        window.addEventListener('load', () => {
            if ('performance' in window) {
                const perfData = performance.getEntriesByType('navigation')[0];
                
                this.performanceMetrics = {
                    loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                    domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                    firstPaint: this.getFirstPaint(),
                    resourceCount: performance.getEntriesByType('resource').length
                };

                console.log('Performance Metrics:', this.performanceMetrics);
                
                // Send to analytics if available
                if (typeof trackEvent === 'function') {
                    trackEvent('performance', 'page_load', 'load_time', this.performanceMetrics.loadTime);
                }
            }
        });
    }

    /**
     * Get First Paint timing
     */
    getFirstPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
        return firstPaint ? firstPaint.startTime : 0;
    }

    /**
     * Optimize scrolling performance
     */
    optimizeScrolling() {
        let ticking = false;

        const optimizedScrollHandler = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    // Handle scroll events here
                    this.handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        };

        // Use passive listeners for better performance
        window.addEventListener('scroll', optimizedScrollHandler, { passive: true });
    }

    /**
     * Handle scroll events
     */
    handleScroll() {
        // Implement scroll-based optimizations
        const scrollTop = window.pageYOffset;
        
        // Hide/show elements based on scroll position
        this.optimizeVisibleElements(scrollTop);
    }

    /**
     * Optimize visible elements
     */
    optimizeVisibleElements(scrollTop) {
        const windowHeight = window.innerHeight;
        
        document.querySelectorAll('.product-card').forEach(card => {
            const rect = card.getBoundingClientRect();
            const isVisible = rect.top < windowHeight && rect.bottom > 0;
            
            if (isVisible && !card.dataset.optimized) {
                // Optimize visible cards
                this.optimizeProductCard(card);
                card.dataset.optimized = 'true';
            }
        });
    }

    /**
     * Optimize individual product card
     */
    optimizeProductCard(card) {
        // Add GPU acceleration
        card.style.transform = 'translateZ(0)';
        card.style.backfaceVisibility = 'hidden';
        
        // Optimize images within card
        const img = card.querySelector('img');
        if (img && img.dataset.src && !img.dataset.loaded) {
            this.loadImage(img);
        }
    }

    /**
     * Setup request optimization
     */
    setupRequestOptimization() {
        this.requestQueue = [];
        this.isProcessing = false;
        this.requestCache = new Map();
    }

    /**
     * Optimized fetch with caching and queuing
     */
    async optimizedFetch(url, options = {}) {
        // Check cache first
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        if (this.requestCache.has(cacheKey)) {
            const cached = this.requestCache.get(cacheKey);
            if (Date.now() - cached.timestamp < 300000) { // 5 minutes cache
                return cached.data;
            }
        }

        try {
            const response = await fetch(url, {
                ...options,
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            
            // Cache successful responses
            this.requestCache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            console.error('Optimized fetch error:', error);
            throw error;
        }
    }

    /**
     * Debounced function utility
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttled function utility
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Optimize DOM operations
     */
    batchDOMUpdates(updates) {
        requestAnimationFrame(() => {
            updates.forEach(update => update());
        });
    }

    /**
     * Memory cleanup
     */
    cleanup() {
        if (this.imageObserver) {
            this.imageObserver.disconnect();
        }
        
        // Clear caches
        this.requestCache.clear();
        this.requestQueue = [];
    }

    /**
     * Get performance report
     */
    getPerformanceReport() {
        return {
            metrics: this.performanceMetrics,
            cacheSize: this.requestCache.size,
            memoryUsage: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1048576),
                total: Math.round(performance.memory.totalJSHeapSize / 1048576),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576)
            } : null
        };
    }
}

// Initialize performance optimizer
const performanceOptimizer = new PerformanceOptimizer();

// Global utilities
window.optimizedFetch = (url, options) => performanceOptimizer.optimizedFetch(url, options);
window.debounce = (func, wait) => performanceOptimizer.debounce(func, wait);
window.throttle = (func, limit) => performanceOptimizer.throttle(func, limit);

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    performanceOptimizer.cleanup();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
}
