/**
 * Advanced Search & Filter System for Products Page
 */

class AdvancedSearch {
    constructor() {
        this.searchInput = document.querySelector('input[name="search"]');
        this.suggestionsContainer = null;
        this.debounceTimer = null;
        this.currentFocus = -1;
        
        this.init();
    }
    
    init() {
        if (this.searchInput) {
            this.createSuggestionsContainer();
            this.bindEvents();
        }
        
        this.initPriceRangeSliders();
        this.initQuickFilters();
        this.initFilterPresets();
    }
    
    createSuggestionsContainer() {
        this.suggestionsContainer = document.createElement('div');
        this.suggestionsContainer.className = 'search-suggestions';
        this.suggestionsContainer.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        `;
        
        // Make search input container relative
        const inputGroup = this.searchInput.closest('.input-group');
        if (inputGroup) {
            inputGroup.style.position = 'relative';
            inputGroup.appendChild(this.suggestionsContainer);
        }
    }
    
    bindEvents() {
        // Search input events
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });
        
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyNavigation(e);
        });
        
        this.searchInput.addEventListener('focus', () => {
            if (this.searchInput.value.length >= 2) {
                this.showSuggestions();
            }
        });
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.input-group')) {
                this.hideSuggestions();
            }
        });
    }
    
    handleSearchInput(query) {
        clearTimeout(this.debounceTimer);
        
        if (query.length < 2) {
            this.hideSuggestions();
            return;
        }
        
        this.debounceTimer = setTimeout(() => {
            this.fetchSuggestions(query);
        }, 300);
    }
    
    async fetchSuggestions(query) {
        try {
            const response = await fetch(`ajax/search_suggestions.php?q=${encodeURIComponent(query)}&limit=8`);
            const data = await response.json();
            
            if (data.suggestions && data.suggestions.length > 0) {
                this.renderSuggestions(data.suggestions);
                this.showSuggestions();
            } else {
                this.hideSuggestions();
            }
        } catch (error) {
            console.error('Error fetching suggestions:', error);
            this.hideSuggestions();
        }
    }
    
    renderSuggestions(suggestions) {
        this.suggestionsContainer.innerHTML = '';
        this.currentFocus = -1;
        
        suggestions.forEach((suggestion, index) => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.style.cssText = `
                padding: 12px 16px;
                border-bottom: 1px solid #f0f0f0;
                cursor: pointer;
                display: flex;
                align-items: center;
                transition: background-color 0.2s;
            `;
            
            if (suggestion.type === 'search') {
                item.innerHTML = `
                    <i class="fas fa-search text-primary me-3"></i>
                    <span>${suggestion.title}</span>
                `;
            } else if (suggestion.type === 'product') {
                item.innerHTML = `
                    <img src="${suggestion.image}" alt="${suggestion.title}" 
                         style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; margin-right: 12px;">
                    <div class="flex-grow-1">
                        <div class="fw-bold">${suggestion.title}</div>
                        <div class="text-muted small">
                            ${suggestion.category} • ${suggestion.price}
                            ${suggestion.rating > 0 ? `• ⭐ ${suggestion.rating}` : ''}
                        </div>
                    </div>
                `;
            } else if (suggestion.type === 'category') {
                item.innerHTML = `
                    <i class="fas fa-folder text-warning me-3"></i>
                    <span>Kategori: <strong>${suggestion.title}</strong></span>
                `;
            }
            
            item.addEventListener('click', () => {
                window.location.href = suggestion.url;
            });
            
            item.addEventListener('mouseenter', () => {
                this.setActiveSuggestion(index);
            });
            
            this.suggestionsContainer.appendChild(item);
        });
    }
    
    handleKeyNavigation(e) {
        const suggestions = this.suggestionsContainer.querySelectorAll('.suggestion-item');
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            this.currentFocus = Math.min(this.currentFocus + 1, suggestions.length - 1);
            this.setActiveSuggestion(this.currentFocus);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            this.currentFocus = Math.max(this.currentFocus - 1, -1);
            this.setActiveSuggestion(this.currentFocus);
        } else if (e.key === 'Enter' && this.currentFocus >= 0) {
            e.preventDefault();
            suggestions[this.currentFocus].click();
        } else if (e.key === 'Escape') {
            this.hideSuggestions();
        }
    }
    
    setActiveSuggestion(index) {
        const suggestions = this.suggestionsContainer.querySelectorAll('.suggestion-item');
        
        suggestions.forEach((item, i) => {
            if (i === index) {
                item.style.backgroundColor = '#f8f9fa';
                this.currentFocus = i;
            } else {
                item.style.backgroundColor = '';
            }
        });
    }
    
    showSuggestions() {
        this.suggestionsContainer.style.display = 'block';
    }
    
    hideSuggestions() {
        this.suggestionsContainer.style.display = 'none';
        this.currentFocus = -1;
    }
    
    initPriceRangeSliders() {
        const minPriceInput = document.querySelector('input[name="min_price"]');
        const maxPriceInput = document.querySelector('input[name="max_price"]');
        
        if (minPriceInput && maxPriceInput) {
            // Add price formatting
            [minPriceInput, maxPriceInput].forEach(input => {
                input.addEventListener('blur', (e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value)) {
                        e.target.value = value;
                    }
                });
            });
            
            // Add price validation
            minPriceInput.addEventListener('input', () => {
                const minVal = parseInt(minPriceInput.value) || 0;
                const maxVal = parseInt(maxPriceInput.value) || Infinity;
                
                if (minVal > maxVal && maxPriceInput.value) {
                    maxPriceInput.value = minVal;
                }
            });
        }
    }
    
    initQuickFilters() {
        // Add quick filter buttons functionality
        const quickFilterBtns = document.querySelectorAll('.btn-group .btn');
        
        quickFilterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Toggle active state
                btn.classList.toggle('active');
                
                // Navigate to filtered URL
                setTimeout(() => {
                    window.location.href = btn.href;
                }, 100);
            });
        });
    }
    
    initFilterPresets() {
        // Add filter preset functionality
        const presetButtons = [
            { name: 'Terlaris', params: { sort: 'popularity' } },
            { name: 'Termurah', params: { sort: 'price_low' } },
            { name: 'Rating Tinggi', params: { min_rating: '4', sort: 'rating' } },
            { name: 'Terbaru', params: { sort: 'newest' } }
        ];
        
        // You can add preset buttons to the UI if needed
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdvancedSearch();
});

// Export for use in other scripts
window.AdvancedSearch = AdvancedSearch;
