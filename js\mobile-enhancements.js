/**
 * Mobile Enhancement JavaScript for TeWuNeed
 * Touch-friendly interactions and mobile-specific features
 */

class MobileEnhancements {
    constructor() {
        this.isMobile = this.detectMobile();
        this.isTouch = this.detectTouch();
        
        if (this.isMobile || this.isTouch) {
            this.init();
        }
    }
    
    detectMobile() {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    detectTouch() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
    
    init() {
        this.setupTouchFeedback();
        this.setupSwipeGestures();
        this.setupMobileNavigation();
        this.setupMobileSearch();
        this.setupMobileFilters();
        this.setupMobileCart();
        this.setupMobileModals();
        this.preventZoom();
        this.setupPullToRefresh();
    }
    
    setupTouchFeedback() {
        // Add touch feedback to interactive elements
        const touchElements = document.querySelectorAll('.btn, .card, .nav-link, .dropdown-item');
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', (e) => {
                element.style.transform = 'scale(0.98)';
                element.style.transition = 'transform 0.1s ease';
            });
            
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = '';
                }, 100);
            });
            
            element.addEventListener('touchcancel', (e) => {
                element.style.transform = '';
            });
        });
    }
    
    setupSwipeGestures() {
        // Add swipe gestures for product cards and wishlist items
        const swipeableCards = document.querySelectorAll('.product-card, .wishlist-card, .cart-item');
        
        swipeableCards.forEach(card => {
            let startX = 0;
            let startY = 0;
            let currentX = 0;
            let currentY = 0;
            let isDragging = false;
            
            card.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
                isDragging = true;
            });
            
            card.addEventListener('touchmove', (e) => {
                if (!isDragging) return;
                
                currentX = e.touches[0].clientX;
                currentY = e.touches[0].clientY;
                
                const deltaX = currentX - startX;
                const deltaY = currentY - startY;
                
                // Only handle horizontal swipes
                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                    e.preventDefault();
                    
                    // Add visual feedback
                    card.style.transform = `translateX(${deltaX * 0.3}px)`;
                    card.style.transition = 'none';
                }
            });
            
            card.addEventListener('touchend', (e) => {
                if (!isDragging) return;
                isDragging = false;
                
                const deltaX = currentX - startX;
                
                // Reset position
                card.style.transform = '';
                card.style.transition = 'transform 0.3s ease';
                
                // Handle swipe actions
                if (Math.abs(deltaX) > 100) {
                    if (deltaX > 0) {
                        // Swipe right - add to wishlist/cart
                        this.handleSwipeRight(card);
                    } else {
                        // Swipe left - remove/delete
                        this.handleSwipeLeft(card);
                    }
                }
            });
        });
    }
    
    handleSwipeRight(card) {
        const productId = card.dataset.productId;
        
        if (card.classList.contains('wishlist-card')) {
            // Add to cart from wishlist
            const addToCartBtn = card.querySelector('.add-to-cart-btn');
            if (addToCartBtn && !addToCartBtn.disabled) {
                addToCartBtn.click();
            }
        } else if (card.classList.contains('product-card')) {
            // Add to wishlist
            const wishlistBtn = card.querySelector('.wishlist-btn');
            if (wishlistBtn) {
                wishlistBtn.click();
            }
        }
    }
    
    handleSwipeLeft(card) {
        const productId = card.dataset.productId;
        
        if (card.classList.contains('wishlist-card')) {
            // Remove from wishlist
            if (typeof removeFromWishlist === 'function') {
                removeFromWishlist(productId);
            }
        } else if (card.classList.contains('cart-item')) {
            // Remove from cart
            const removeBtn = card.querySelector('.remove-btn, .btn-remove');
            if (removeBtn) {
                removeBtn.click();
            }
        }
    }
    
    setupMobileNavigation() {
        // Improve mobile navigation
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');
        
        if (navbarToggler && navbarCollapse) {
            // Close navbar when clicking outside
            document.addEventListener('click', (e) => {
                if (!navbarToggler.contains(e.target) && 
                    !navbarCollapse.contains(e.target) && 
                    navbarCollapse.classList.contains('show')) {
                    navbarToggler.click();
                }
            });
            
            // Close navbar when clicking on nav links
            const navLinks = navbarCollapse.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    if (navbarCollapse.classList.contains('show')) {
                        navbarToggler.click();
                    }
                });
            });
        }
    }
    
    setupMobileSearch() {
        // Improve search experience on mobile
        const searchInput = document.querySelector('input[name="search"]');
        
        if (searchInput) {
            // Prevent zoom on focus for iOS
            searchInput.addEventListener('focus', () => {
                if (this.isIOS()) {
                    document.querySelector('meta[name="viewport"]').setAttribute(
                        'content', 
                        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
                    );
                }
            });
            
            searchInput.addEventListener('blur', () => {
                if (this.isIOS()) {
                    document.querySelector('meta[name="viewport"]').setAttribute(
                        'content', 
                        'width=device-width, initial-scale=1.0'
                    );
                }
            });
        }
    }
    
    setupMobileFilters() {
        // Improve filter experience on mobile
        const advancedFilters = document.getElementById('advancedFilters');
        
        if (advancedFilters) {
            // Auto-collapse filters after applying on mobile
            const applyBtn = advancedFilters.querySelector('button[type="submit"]');
            if (applyBtn) {
                applyBtn.addEventListener('click', () => {
                    setTimeout(() => {
                        const collapseElement = bootstrap.Collapse.getInstance(advancedFilters);
                        if (collapseElement) {
                            collapseElement.hide();
                        }
                    }, 500);
                });
            }
        }
        
        // Make dropdowns more touch-friendly
        const dropdowns = document.querySelectorAll('.dropdown-menu');
        dropdowns.forEach(dropdown => {
            dropdown.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        });
    }
    
    setupMobileCart() {
        // Improve cart experience on mobile
        const quantityInputs = document.querySelectorAll('input[type="number"]');
        
        quantityInputs.forEach(input => {
            // Add touch-friendly quantity controls
            const wrapper = document.createElement('div');
            wrapper.className = 'quantity-controls d-flex align-items-center';
            
            const decreaseBtn = document.createElement('button');
            decreaseBtn.type = 'button';
            decreaseBtn.className = 'btn btn-outline-secondary btn-sm';
            decreaseBtn.innerHTML = '<i class="fas fa-minus"></i>';
            
            const increaseBtn = document.createElement('button');
            increaseBtn.type = 'button';
            increaseBtn.className = 'btn btn-outline-secondary btn-sm';
            increaseBtn.innerHTML = '<i class="fas fa-plus"></i>';
            
            input.parentNode.insertBefore(wrapper, input);
            wrapper.appendChild(decreaseBtn);
            wrapper.appendChild(input);
            wrapper.appendChild(increaseBtn);
            
            decreaseBtn.addEventListener('click', () => {
                const currentValue = parseInt(input.value) || 1;
                if (currentValue > 1) {
                    input.value = currentValue - 1;
                    input.dispatchEvent(new Event('change'));
                }
            });
            
            increaseBtn.addEventListener('click', () => {
                const currentValue = parseInt(input.value) || 1;
                const maxValue = parseInt(input.max) || 999;
                if (currentValue < maxValue) {
                    input.value = currentValue + 1;
                    input.dispatchEvent(new Event('change'));
                }
            });
        });
    }
    
    setupMobileModals() {
        // Improve modal experience on mobile
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            modal.addEventListener('shown.bs.modal', () => {
                // Prevent body scroll when modal is open
                document.body.style.overflow = 'hidden';
                
                // Focus first input in modal
                const firstInput = modal.querySelector('input, textarea, select');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 300);
                }
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                // Restore body scroll
                document.body.style.overflow = '';
            });
        });
    }
    
    preventZoom() {
        // Prevent accidental zoom on double tap
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (e) => {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    }
    
    setupPullToRefresh() {
        // Simple pull to refresh implementation
        let startY = 0;
        let currentY = 0;
        let pullDistance = 0;
        const threshold = 100;
        
        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
            }
        });
        
        document.addEventListener('touchmove', (e) => {
            if (window.scrollY === 0 && startY > 0) {
                currentY = e.touches[0].clientY;
                pullDistance = currentY - startY;
                
                if (pullDistance > 0) {
                    // Add visual feedback for pull to refresh
                    document.body.style.transform = `translateY(${Math.min(pullDistance * 0.3, 30)}px)`;
                    document.body.style.transition = 'none';
                }
            }
        });
        
        document.addEventListener('touchend', (e) => {
            if (pullDistance > threshold) {
                // Trigger refresh
                location.reload();
            }
            
            // Reset
            document.body.style.transform = '';
            document.body.style.transition = 'transform 0.3s ease';
            startY = 0;
            pullDistance = 0;
        });
    }
    
    isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }
    
    // Utility method to show mobile-friendly notifications
    showMobileNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} mobile-notification`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            max-width: 90%;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-50%) translateY(-20px)';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Initialize mobile enhancements when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mobileEnhancements = new MobileEnhancements();
});

// Export for use in other scripts
window.MobileEnhancements = MobileEnhancements;
