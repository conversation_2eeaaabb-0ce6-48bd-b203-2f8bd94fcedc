-- Analytics System Tables
USE db_tewuneed;

-- Create analytics sessions table
CREATE TABLE IF NOT EXISTS analytics_sessions (
    session_id VARCHAR(100) PRIMARY KEY,
    user_id INT NULL,
    referrer_domain VARCHAR(255) NULL,
    landing_page VARCHAR(500) NULL,
    device_type ENUM('desktop', 'mobile', 'tablet') NULL,
    browser VARCHAR(100) NULL,
    operating_system VARCHAR(100) NULL,
    screen_resolution VARCHAR(20) NULL,
    country VARCHAR(100) NULL,
    city VARCHAR(100) NULL,
    ip_address VARCHAR(45) NULL,
    session_duration INT DEFAULT 0,
    page_views INT DEFAULT 0,
    is_bounce BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_referrer (referrer_domain),
    INDEX idx_device_type (device_type)
);

-- Create analytics page views table
CREATE TABLE IF NOT EXISTS analytics_page_views (
    page_view_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    user_id INT NULL,
    page_url VARCHAR(500) NOT NULL,
    page_title VARCHAR(200) NULL,
    referrer_url VARCHAR(500) NULL,
    time_on_page INT DEFAULT 0,
    user_agent TEXT NULL,
    ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analytics_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_page_url (page_url),
    INDEX idx_created_at (created_at)
);

-- Create analytics events table
CREATE TABLE IF NOT EXISTS analytics_events (
    event_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    user_id INT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_category VARCHAR(100) NULL,
    event_action VARCHAR(100) NULL,
    event_label VARCHAR(200) NULL,
    event_value DECIMAL(10,2) NULL,
    page_url VARCHAR(500) NULL,
    custom_data JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analytics_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_event_category (event_category),
    INDEX idx_created_at (created_at)
);

-- Create analytics conversions table
CREATE TABLE IF NOT EXISTS analytics_conversions (
    conversion_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    user_id INT NOT NULL,
    conversion_type ENUM('purchase', 'signup', 'newsletter', 'download', 'contact') NOT NULL,
    conversion_value DECIMAL(15,2) NULL,
    order_id VARCHAR(50) NULL,
    product_id INT NULL,
    attribution_source VARCHAR(100) NULL,
    attribution_medium VARCHAR(100) NULL,
    attribution_campaign VARCHAR(100) NULL,
    time_to_conversion INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analytics_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE SET NULL,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE SET NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_conversion_type (conversion_type),
    INDEX idx_order_id (order_id),
    INDEX idx_created_at (created_at)
);

-- Create analytics product views table
CREATE TABLE IF NOT EXISTS analytics_product_views (
    view_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    user_id INT NULL,
    product_id INT NOT NULL,
    category_id INT NULL,
    view_source VARCHAR(100) NULL,
    time_spent INT DEFAULT 0,
    scrolled_percentage DECIMAL(5,2) DEFAULT 0,
    images_viewed INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analytics_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE SET NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_category_id (category_id),
    INDEX idx_created_at (created_at)
);

-- Create analytics search queries table
CREATE TABLE IF NOT EXISTS analytics_search_queries (
    search_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    user_id INT NULL,
    search_query VARCHAR(500) NOT NULL,
    results_count INT DEFAULT 0,
    clicked_result_position INT NULL,
    clicked_product_id INT NULL,
    no_results BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analytics_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (clicked_product_id) REFERENCES products(product_id) ON DELETE SET NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_search_query (search_query),
    INDEX idx_created_at (created_at)
);

-- Create analytics cart events table
CREATE TABLE IF NOT EXISTS analytics_cart_events (
    cart_event_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    user_id INT NULL,
    event_type ENUM('add', 'remove', 'update', 'view', 'abandon') NOT NULL,
    product_id INT NOT NULL,
    quantity INT DEFAULT 1,
    price DECIMAL(15,2) NULL,
    cart_total DECIMAL(15,2) NULL,
    cart_items_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analytics_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_product_id (product_id),
    INDEX idx_created_at (created_at)
);

-- Create analytics daily summaries table for faster reporting
CREATE TABLE IF NOT EXISTS analytics_daily_summaries (
    summary_date DATE PRIMARY KEY,
    total_sessions INT DEFAULT 0,
    unique_visitors INT DEFAULT 0,
    total_page_views INT DEFAULT 0,
    bounce_rate DECIMAL(5,2) DEFAULT 0,
    avg_session_duration DECIMAL(8,2) DEFAULT 0,
    total_orders INT DEFAULT 0,
    total_revenue DECIMAL(15,2) DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0,
    new_users INT DEFAULT 0,
    returning_users INT DEFAULT 0,
    mobile_sessions INT DEFAULT 0,
    desktop_sessions INT DEFAULT 0,
    tablet_sessions INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_summary_date (summary_date)
);

-- Create analytics utm tracking table
CREATE TABLE IF NOT EXISTS analytics_utm_tracking (
    utm_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    utm_source VARCHAR(100) NULL,
    utm_medium VARCHAR(100) NULL,
    utm_campaign VARCHAR(100) NULL,
    utm_term VARCHAR(100) NULL,
    utm_content VARCHAR(100) NULL,
    landing_page VARCHAR(500) NULL,
    conversions INT DEFAULT 0,
    conversion_value DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analytics_sessions(session_id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_utm_source (utm_source),
    INDEX idx_utm_campaign (utm_campaign),
    INDEX idx_created_at (created_at)
);

-- Create analytics heatmap data table
CREATE TABLE IF NOT EXISTS analytics_heatmap_data (
    heatmap_id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    page_url VARCHAR(500) NOT NULL,
    element_selector VARCHAR(200) NULL,
    click_x INT NULL,
    click_y INT NULL,
    scroll_depth DECIMAL(5,2) NULL,
    viewport_width INT NULL,
    viewport_height INT NULL,
    event_type ENUM('click', 'scroll', 'hover', 'focus') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analytics_sessions(session_id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_page_url (page_url),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at)
);

-- Create stored procedure to update daily summaries
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS UpdateDailySummaries(IN target_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    INSERT INTO analytics_daily_summaries (
        summary_date,
        total_sessions,
        unique_visitors,
        total_page_views,
        bounce_rate,
        avg_session_duration,
        total_orders,
        total_revenue,
        conversion_rate,
        new_users,
        returning_users,
        mobile_sessions,
        desktop_sessions,
        tablet_sessions
    )
    SELECT 
        target_date,
        COUNT(DISTINCT s.session_id) as total_sessions,
        COUNT(DISTINCT s.session_id) as unique_visitors,
        COALESCE(pv.total_page_views, 0) as total_page_views,
        AVG(CASE WHEN s.is_bounce THEN 100 ELSE 0 END) as bounce_rate,
        AVG(s.session_duration) as avg_session_duration,
        COALESCE(o.total_orders, 0) as total_orders,
        COALESCE(o.total_revenue, 0) as total_revenue,
        CASE 
            WHEN COUNT(DISTINCT s.session_id) > 0 
            THEN (COALESCE(o.total_orders, 0) / COUNT(DISTINCT s.session_id)) * 100 
            ELSE 0 
        END as conversion_rate,
        COALESCE(u.new_users, 0) as new_users,
        COALESCE(u.returning_users, 0) as returning_users,
        SUM(CASE WHEN s.device_type = 'mobile' THEN 1 ELSE 0 END) as mobile_sessions,
        SUM(CASE WHEN s.device_type = 'desktop' THEN 1 ELSE 0 END) as desktop_sessions,
        SUM(CASE WHEN s.device_type = 'tablet' THEN 1 ELSE 0 END) as tablet_sessions
    FROM analytics_sessions s
    LEFT JOIN (
        SELECT COUNT(*) as total_page_views
        FROM analytics_page_views 
        WHERE DATE(created_at) = target_date
    ) pv ON 1=1
    LEFT JOIN (
        SELECT 
            COUNT(*) as total_orders,
            SUM(total_amount) as total_revenue
        FROM orders 
        WHERE DATE(created_at) = target_date
    ) o ON 1=1
    LEFT JOIN (
        SELECT 
            SUM(CASE WHEN DATE(created_at) = target_date THEN 1 ELSE 0 END) as new_users,
            COUNT(*) - SUM(CASE WHEN DATE(created_at) = target_date THEN 1 ELSE 0 END) as returning_users
        FROM users
    ) u ON 1=1
    WHERE DATE(s.created_at) = target_date
    ON DUPLICATE KEY UPDATE
        total_sessions = VALUES(total_sessions),
        unique_visitors = VALUES(unique_visitors),
        total_page_views = VALUES(total_page_views),
        bounce_rate = VALUES(bounce_rate),
        avg_session_duration = VALUES(avg_session_duration),
        total_orders = VALUES(total_orders),
        total_revenue = VALUES(total_revenue),
        conversion_rate = VALUES(conversion_rate),
        new_users = VALUES(new_users),
        returning_users = VALUES(returning_users),
        mobile_sessions = VALUES(mobile_sessions),
        desktop_sessions = VALUES(desktop_sessions),
        tablet_sessions = VALUES(tablet_sessions),
        updated_at = NOW();

    COMMIT;
END//

DELIMITER ;

-- Create event to automatically update daily summaries
CREATE EVENT IF NOT EXISTS update_daily_analytics
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '01:00:00')
DO
  CALL UpdateDailySummaries(CURRENT_DATE - INTERVAL 1 DAY);

-- Create indexes for better performance
CREATE INDEX idx_analytics_sessions_date_device ON analytics_sessions(created_at, device_type);
CREATE INDEX idx_analytics_page_views_date_url ON analytics_page_views(created_at, page_url);
CREATE INDEX idx_analytics_events_date_type ON analytics_events(created_at, event_type);
CREATE INDEX idx_analytics_conversions_date_type ON analytics_conversions(created_at, conversion_type);

-- Insert sample analytics data for testing
INSERT INTO analytics_sessions (session_id, user_id, referrer_domain, device_type, browser, created_at) VALUES
('sess_001', 1, 'google.com', 'desktop', 'Chrome', NOW() - INTERVAL 1 DAY),
('sess_002', 2, 'facebook.com', 'mobile', 'Safari', NOW() - INTERVAL 1 DAY),
('sess_003', NULL, 'direct', 'desktop', 'Firefox', NOW() - INTERVAL 2 DAYS),
('sess_004', 3, 'instagram.com', 'mobile', 'Chrome', NOW() - INTERVAL 3 DAYS);

INSERT INTO analytics_page_views (session_id, user_id, page_url, page_title, created_at) VALUES
('sess_001', 1, '/index.php', 'Home Page', NOW() - INTERVAL 1 DAY),
('sess_001', 1, '/Products.php', 'Products', NOW() - INTERVAL 1 DAY),
('sess_002', 2, '/product-detail.php?id=1', 'Product Detail', NOW() - INTERVAL 1 DAY),
('sess_003', NULL, '/index.php', 'Home Page', NOW() - INTERVAL 2 DAYS);

SELECT 'Analytics system tables created successfully!' as status;
