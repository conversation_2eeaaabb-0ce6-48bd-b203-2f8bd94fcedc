-- Cart Performance Optimization
-- Optimize database for faster cart operations

USE db_tewuneed;

-- Add indexes for faster cart operations
CREATE INDEX IF NOT EXISTS idx_carts_user_id ON carts(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_id ON cart_items(cart_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_product_id ON cart_items(product_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_product ON cart_items(cart_id, product_id);
CREATE INDEX IF NOT EXISTS idx_products_active_stock ON products(is_active, stock);
CREATE INDEX IF NOT EXISTS idx_products_id_active ON products(product_id, is_active);

-- Add unique constraint for cart_items to prevent duplicates
ALTER TABLE cart_items ADD CONSTRAINT unique_cart_product UNIQUE (cart_id, product_id);

-- Optimize cart_items table structure
ALTER TABLE cart_items 
ADD COLUMN IF NOT EXISTS price DECIMAL(15,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS subtotal DECIMAL(15,2) GENERATED ALWAYS AS (quantity * price) STORED;

-- Create optimized view for cart summary
CREATE OR REPLACE VIEW cart_summary AS
SELECT 
    c.cart_id,
    c.user_id,
    COUNT(ci.cart_item_id) as item_count,
    SUM(ci.quantity) as total_quantity,
    SUM(ci.quantity * p.price) as total_amount,
    c.created_at,
    c.updated_at
FROM carts c
LEFT JOIN cart_items ci ON c.cart_id = ci.cart_id
LEFT JOIN products p ON ci.product_id = p.product_id
WHERE p.is_active = 1
GROUP BY c.cart_id, c.user_id, c.created_at, c.updated_at;

-- Create stored procedure for fast cart operations
DELIMITER //

CREATE OR REPLACE PROCEDURE AddToCartFast(
    IN p_user_id INT,
    IN p_product_id INT,
    IN p_quantity INT
)
BEGIN
    DECLARE v_cart_id INT;
    DECLARE v_stock INT;
    DECLARE v_price DECIMAL(15,2);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Check product availability and get price
    SELECT stock, price INTO v_stock, v_price
    FROM products 
    WHERE product_id = p_product_id AND is_active = 1;

    IF v_stock IS NULL THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Product not available';
    END IF;

    IF v_stock < p_quantity THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = CONCAT('Insufficient stock. Available: ', v_stock);
    END IF;

    -- Get or create cart
    SELECT cart_id INTO v_cart_id
    FROM carts 
    WHERE user_id = p_user_id;

    IF v_cart_id IS NULL THEN
        INSERT INTO carts (user_id, created_at) VALUES (p_user_id, NOW());
        SET v_cart_id = LAST_INSERT_ID();
    END IF;

    -- Add or update cart item
    INSERT INTO cart_items (cart_id, product_id, quantity, price, added_at)
    VALUES (v_cart_id, p_product_id, p_quantity, v_price, NOW())
    ON DUPLICATE KEY UPDATE 
        quantity = quantity + VALUES(quantity),
        added_at = NOW();

    COMMIT;

    -- Return cart summary
    SELECT 
        v_cart_id as cart_id,
        SUM(quantity) as cart_count,
        SUM(quantity * price) as cart_total
    FROM cart_items 
    WHERE cart_id = v_cart_id;

END//

DELIMITER ;

-- Create function to get cart count quickly
DELIMITER //

CREATE OR REPLACE FUNCTION GetCartCount(p_user_id INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    SELECT COALESCE(SUM(ci.quantity), 0) INTO v_count
    FROM carts c
    JOIN cart_items ci ON c.cart_id = ci.cart_id
    WHERE c.user_id = p_user_id;
    
    RETURN v_count;
END//

DELIMITER ;

-- Create trigger to update cart timestamp
DELIMITER //

CREATE OR REPLACE TRIGGER cart_items_update_timestamp
AFTER INSERT ON cart_items
FOR EACH ROW
BEGIN
    UPDATE carts 
    SET updated_at = NOW() 
    WHERE cart_id = NEW.cart_id;
END//

CREATE OR REPLACE TRIGGER cart_items_update_timestamp_upd
AFTER UPDATE ON cart_items
FOR EACH ROW
BEGIN
    UPDATE carts 
    SET updated_at = NOW() 
    WHERE cart_id = NEW.cart_id;
END//

DELIMITER ;

-- Optimize table storage engines and settings
ALTER TABLE carts ENGINE=InnoDB;
ALTER TABLE cart_items ENGINE=InnoDB;
ALTER TABLE products ENGINE=InnoDB;

-- Add table partitioning for large datasets (optional)
-- ALTER TABLE cart_items PARTITION BY HASH(cart_id) PARTITIONS 4;

-- Create materialized view for frequently accessed cart data
CREATE TABLE IF NOT EXISTS cart_cache (
    user_id INT PRIMARY KEY,
    cart_count INT DEFAULT 0,
    cart_total DECIMAL(15,2) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_last_updated (last_updated)
);

-- Procedure to refresh cart cache
DELIMITER //

CREATE OR REPLACE PROCEDURE RefreshCartCache(IN p_user_id INT)
BEGIN
    INSERT INTO cart_cache (user_id, cart_count, cart_total)
    SELECT 
        c.user_id,
        COALESCE(SUM(ci.quantity), 0) as cart_count,
        COALESCE(SUM(ci.quantity * p.price), 0) as cart_total
    FROM carts c
    LEFT JOIN cart_items ci ON c.cart_id = ci.cart_id
    LEFT JOIN products p ON ci.product_id = p.product_id AND p.is_active = 1
    WHERE c.user_id = p_user_id
    GROUP BY c.user_id
    ON DUPLICATE KEY UPDATE
        cart_count = VALUES(cart_count),
        cart_total = VALUES(cart_total),
        last_updated = NOW();
END//

DELIMITER ;

-- Create event to clean up old carts
CREATE EVENT IF NOT EXISTS cleanup_old_carts
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    -- Delete empty carts older than 30 days
    DELETE c FROM carts c
    LEFT JOIN cart_items ci ON c.cart_id = ci.cart_id
    WHERE ci.cart_id IS NULL 
    AND c.created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Clean up cart cache for inactive users
    DELETE FROM cart_cache 
    WHERE last_updated < DATE_SUB(NOW(), INTERVAL 7 DAY);
END;

-- Performance monitoring queries
-- Use these to monitor cart performance

-- Query to check slow cart operations
CREATE OR REPLACE VIEW slow_cart_operations AS
SELECT 
    'cart_items_per_user' as metric,
    user_id,
    COUNT(*) as count
FROM carts c
JOIN cart_items ci ON c.cart_id = ci.cart_id
GROUP BY user_id
HAVING COUNT(*) > 50
UNION ALL
SELECT 
    'large_cart_quantities' as metric,
    c.user_id,
    SUM(ci.quantity) as count
FROM carts c
JOIN cart_items ci ON c.cart_id = ci.cart_id
GROUP BY c.user_id
HAVING SUM(ci.quantity) > 100;

-- Query to analyze cart performance
CREATE OR REPLACE VIEW cart_performance_stats AS
SELECT 
    'total_carts' as metric,
    COUNT(*) as value,
    NULL as details
FROM carts
UNION ALL
SELECT 
    'total_cart_items' as metric,
    COUNT(*) as value,
    NULL as details
FROM cart_items
UNION ALL
SELECT 
    'avg_items_per_cart' as metric,
    ROUND(AVG(item_count), 2) as value,
    NULL as details
FROM (
    SELECT COUNT(*) as item_count
    FROM cart_items
    GROUP BY cart_id
) as cart_stats
UNION ALL
SELECT 
    'carts_with_items' as metric,
    COUNT(DISTINCT cart_id) as value,
    NULL as details
FROM cart_items;

-- Test the optimization
SELECT 'Cart performance optimization completed successfully!' as status;

-- Show current cart statistics
SELECT * FROM cart_performance_stats;
