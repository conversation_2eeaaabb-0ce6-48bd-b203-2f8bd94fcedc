<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/SearchManager.php';

header('Content-Type: application/json');

if (!isset($_GET['q']) || empty(trim($_GET['q']))) {
    echo json_encode(['suggestions' => []]);
    exit;
}

$query = trim($_GET['q']);
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

try {
    // Search in products and categories
    $stmt = $conn->prepare("
        (SELECT 
            p.NAME as title,
            'product' as type,
            p.product_id as id,
            p.price,
            p.image,
            c.NAME as category_name,
            COALESCE(prs.average_rating, 0) as rating
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        LEFT JOIN product_rating_summary prs ON p.product_id = prs.product_id
        WHERE p.is_active = 1 AND (p.NAME LIKE ? OR p.description LIKE ?)
        ORDER BY p.NAME ASC
        LIMIT ?)
        
        UNION ALL
        
        (SELECT 
            c.NAME as title,
            'category' as type,
            c.category_id as id,
            NULL as price,
            c.image,
            NULL as category_name,
            NULL as rating
        FROM categories c
        WHERE c.NAME LIKE ?
        ORDER BY c.NAME ASC
        LIMIT 3)
    ");
    
    $searchManager = new SearchManager();
    $results = $searchManager->getSearchSuggestions($query, $limit);
    
    $suggestions = [];
    foreach ($results as $result) {
        if ($result['type'] === 'product') {
            $suggestions[] = [
                'title' => $result['title'],
                'type' => 'product',
                'id' => $result['id'],
                'price' => $result['price'] ? 'Rp ' . number_format($result['price']) : '',
                'image' => $result['image'] ? 'uploads/' . $result['image'] : 'assets/img/product-default.jpg',
                'category' => $result['category_name'],
                'rating' => round($result['rating'], 1),
                'url' => 'product-detail.php?id=' . $result['id']
            ];
        } else {
            $suggestions[] = [
                'title' => $result['title'],
                'type' => 'category',
                'id' => $result['id'],
                'url' => 'Products.php?category=' . $result['id']
            ];
        }
    }
    
    // Add search query suggestion
    if (!empty($suggestions)) {
        array_unshift($suggestions, [
            'title' => 'Cari "' . htmlspecialchars($query) . '"',
            'type' => 'search',
            'url' => 'Products.php?search=' . urlencode($query)
        ]);
    }
    
    echo json_encode(['suggestions' => $suggestions]);
    
} catch (Exception $e) {
    error_log("Search suggestions error: " . $e->getMessage());
    echo json_encode(['suggestions' => []]);
}
?>
