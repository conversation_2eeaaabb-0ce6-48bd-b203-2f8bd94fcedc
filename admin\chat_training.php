<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = 'Live Chat Training Center';
require_once 'includes/admin_header.php';
?>

<style>
.training-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: transform 0.2s ease;
}

.training-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.training-section {
    margin-bottom: 3rem;
}

.training-step {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
}

.training-step h5 {
    color: #007bff;
    margin-bottom: 1rem;
}

.quick-reply-demo {
    background: #e3f2fd;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.chat-demo {
    background: #f5f5f5;
    border-radius: 12px;
    padding: 1.5rem;
    max-height: 400px;
    overflow-y: auto;
}

.demo-message {
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 18px;
    max-width: 70%;
}

.demo-message.customer {
    background: #e3f2fd;
    margin-left: auto;
    text-align: right;
}

.demo-message.agent {
    background: #e8f5e8;
    margin-right: auto;
}

.demo-message.system {
    background: #fff3e0;
    text-align: center;
    margin: 0 auto;
    font-style: italic;
    font-size: 0.9rem;
}

.training-checklist {
    list-style: none;
    padding: 0;
}

.training-checklist li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.training-checklist li:before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
    margin-right: 0.5rem;
}

.kpi-card {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.kpi-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.kpi-label {
    font-size: 0.9rem;
    opacity: 0.9;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-graduation-cap me-3"></i>Live Chat Training Center</h1>
                <div class="btn-group">
                    <a href="chat_dashboard.php" class="btn btn-primary">
                        <i class="fas fa-comments me-1"></i>Go to Chat Dashboard
                    </a>
                    <a href="chat_settings.php" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-1"></i>Chat Settings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Overview -->
    <div class="row">
        <div class="col-md-8">
            <div class="training-card">
                <h2><i class="fas fa-info-circle me-2 text-primary"></i>Training Overview</h2>
                <p class="lead">Welcome to the TeWuNeed Live Chat Training Center. This comprehensive guide will help you master our customer support chat system.</p>
                
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="kpi-card">
                            <div class="kpi-value">5</div>
                            <div class="kpi-label">Training Modules</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="kpi-card">
                            <div class="kpi-value">30</div>
                            <div class="kpi-label">Minutes to Complete</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="kpi-card">
                            <div class="kpi-value">24/7</div>
                            <div class="kpi-label">Support Coverage</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="training-card">
                <h5><i class="fas fa-tasks me-2"></i>Training Checklist</h5>
                <ul class="training-checklist">
                    <li>System Overview & Navigation</li>
                    <li>Chat Interface Basics</li>
                    <li>Quick Replies & Templates</li>
                    <li>Customer Service Best Practices</li>
                    <li>Escalation Procedures</li>
                    <li>Performance Metrics</li>
                    <li>Troubleshooting Common Issues</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Module 1: System Overview -->
    <div class="training-section">
        <div class="training-card">
            <h3><i class="fas fa-desktop me-2 text-primary"></i>Module 1: System Overview & Navigation</h3>
            
            <div class="training-step">
                <h5>1.1 Accessing the Chat Dashboard</h5>
                <p>Navigate to <strong>Admin Panel → Chat Dashboard</strong> to access the live chat interface.</p>
                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>Tip:</strong> Bookmark the chat dashboard for quick access during your shift.
                </div>
            </div>
            
            <div class="training-step">
                <h5>1.2 Dashboard Components</h5>
                <ul>
                    <li><strong>Active Chats:</strong> Shows all ongoing conversations</li>
                    <li><strong>Waiting Queue:</strong> Customers waiting for agent assignment</li>
                    <li><strong>Chat History:</strong> Previous conversations and their status</li>
                    <li><strong>Agent Status:</strong> Your availability and current workload</li>
                    <li><strong>Quick Stats:</strong> Real-time performance metrics</li>
                </ul>
            </div>
            
            <div class="training-step">
                <h5>1.3 Setting Your Status</h5>
                <p>Always update your status when starting/ending your shift:</p>
                <ul>
                    <li><span class="badge bg-success">Online</span> - Available for new chats</li>
                    <li><span class="badge bg-warning">Away</span> - Temporarily unavailable</li>
                    <li><span class="badge bg-danger">Busy</span> - At maximum chat capacity</li>
                    <li><span class="badge bg-secondary">Offline</span> - Not available for chats</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Module 2: Chat Interface -->
    <div class="training-section">
        <div class="training-card">
            <h3><i class="fas fa-comments me-2 text-primary"></i>Module 2: Chat Interface Basics</h3>
            
            <div class="training-step">
                <h5>2.1 Accepting New Chats</h5>
                <p>When a new chat request arrives:</p>
                <ol>
                    <li>You'll receive a notification sound and visual alert</li>
                    <li>Customer information will appear in the waiting queue</li>
                    <li>Click <strong>"Accept Chat"</strong> to start the conversation</li>
                    <li>The system will automatically send a greeting message</li>
                </ol>
            </div>
            
            <div class="training-step">
                <h5>2.2 Chat Window Layout</h5>
                <div class="row">
                    <div class="col-md-6">
                        <ul>
                            <li><strong>Customer Info Panel:</strong> Name, email, order history</li>
                            <li><strong>Message Area:</strong> Conversation thread</li>
                            <li><strong>Quick Replies:</strong> Pre-written responses</li>
                            <li><strong>File Upload:</strong> Send images or documents</li>
                            <li><strong>Chat Actions:</strong> Transfer, close, escalate</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <div class="chat-demo">
                            <div class="demo-message system">Customer John Doe has joined the chat</div>
                            <div class="demo-message customer">Hi, I need help with my order</div>
                            <div class="demo-message agent">Hello John! I'm happy to help you with your order. Could you please provide your order number?</div>
                            <div class="demo-message customer">It's ORDER123456</div>
                            <div class="demo-message agent">Thank you! Let me check that for you right away.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Module 3: Quick Replies -->
    <div class="training-section">
        <div class="training-card">
            <h3><i class="fas fa-bolt me-2 text-primary"></i>Module 3: Quick Replies & Templates</h3>
            
            <div class="training-step">
                <h5>3.1 Using Quick Replies</h5>
                <p>Quick replies help you respond faster and maintain consistency:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Greeting Templates:</h6>
                        <div class="quick-reply-demo">
                            <strong>Welcome:</strong> "Hello! Welcome to TeWuNeed. How can I help you today?"
                        </div>
                        <div class="quick-reply-demo">
                            <strong>Thank You:</strong> "Thank you for contacting us. How may I assist you?"
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Order Support Templates:</h6>
                        <div class="quick-reply-demo">
                            <strong>Order Status:</strong> "I can help you check your order status. Please provide your order number."
                        </div>
                        <div class="quick-reply-demo">
                            <strong>Order Cancellation:</strong> "I understand you want to cancel your order. Let me help you with that."
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="training-step">
                <h5>3.2 Customizing Responses</h5>
                <p>Always personalize quick replies with:</p>
                <ul>
                    <li>Customer's name when available</li>
                    <li>Specific order or product details</li>
                    <li>Relevant timeframes or next steps</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Module 4: Best Practices -->
    <div class="training-section">
        <div class="training-card">
            <h3><i class="fas fa-star me-2 text-primary"></i>Module 4: Customer Service Best Practices</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="training-step">
                        <h5>4.1 Communication Guidelines</h5>
                        <ul>
                            <li><strong>Be Professional:</strong> Use proper grammar and spelling</li>
                            <li><strong>Be Empathetic:</strong> Acknowledge customer concerns</li>
                            <li><strong>Be Clear:</strong> Provide specific, actionable information</li>
                            <li><strong>Be Prompt:</strong> Respond within 30 seconds</li>
                            <li><strong>Be Solution-Focused:</strong> Always offer next steps</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="training-step">
                        <h5>4.2 Response Time Targets</h5>
                        <ul>
                            <li><strong>First Response:</strong> Within 30 seconds</li>
                            <li><strong>Follow-up:</strong> Within 1 minute</li>
                            <li><strong>Research Time:</strong> Inform customer if you need more than 2 minutes</li>
                            <li><strong>Maximum Chat Duration:</strong> 15 minutes (escalate if longer)</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="training-step">
                <h5>4.3 Handling Difficult Situations</h5>
                <div class="row">
                    <div class="col-md-4">
                        <h6>Angry Customers:</h6>
                        <ul>
                            <li>Stay calm and professional</li>
                            <li>Acknowledge their frustration</li>
                            <li>Focus on solutions</li>
                            <li>Escalate if necessary</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>Complex Issues:</h6>
                        <ul>
                            <li>Ask clarifying questions</li>
                            <li>Break down the problem</li>
                            <li>Provide step-by-step solutions</li>
                            <li>Follow up to ensure resolution</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>Technical Problems:</h6>
                        <ul>
                            <li>Gather system information</li>
                            <li>Try basic troubleshooting</li>
                            <li>Escalate to technical team</li>
                            <li>Keep customer informed</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Module 5: Performance Metrics -->
    <div class="training-section">
        <div class="training-card">
            <h3><i class="fas fa-chart-line me-2 text-primary"></i>Module 5: Performance Metrics & KPIs</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="training-step">
                        <h5>5.1 Key Performance Indicators</h5>
                        <ul>
                            <li><strong>Response Time:</strong> Average time to first response</li>
                            <li><strong>Resolution Rate:</strong> Percentage of issues resolved in first contact</li>
                            <li><strong>Customer Satisfaction:</strong> Average rating from post-chat surveys</li>
                            <li><strong>Chat Volume:</strong> Number of chats handled per hour</li>
                            <li><strong>Escalation Rate:</strong> Percentage of chats escalated to supervisors</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="training-step">
                        <h5>5.2 Performance Targets</h5>
                        <ul>
                            <li><strong>Response Time:</strong> < 30 seconds</li>
                            <li><strong>Resolution Rate:</strong> > 85%</li>
                            <li><strong>Customer Satisfaction:</strong> > 4.5/5</li>
                            <li><strong>Chat Volume:</strong> 8-12 chats/hour</li>
                            <li><strong>Escalation Rate:</strong> < 10%</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Reference -->
    <div class="training-section">
        <div class="training-card">
            <h3><i class="fas fa-bookmark me-2 text-primary"></i>Quick Reference Guide</h3>
            
            <div class="row">
                <div class="col-md-4">
                    <h5>Common Shortcuts</h5>
                    <ul>
                        <li><kbd>Ctrl + Enter</kbd> - Send message</li>
                        <li><kbd>Ctrl + /</kbd> - Open quick replies</li>
                        <li><kbd>Ctrl + T</kbd> - Transfer chat</li>
                        <li><kbd>Ctrl + E</kbd> - Escalate chat</li>
                        <li><kbd>Ctrl + Q</kbd> - Close chat</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Emergency Contacts</h5>
                    <ul>
                        <li><strong>Supervisor:</strong> ext. 101</li>
                        <li><strong>Technical Support:</strong> ext. 102</li>
                        <li><strong>Billing Department:</strong> ext. 103</li>
                        <li><strong>Shipping Department:</strong> ext. 104</li>
                        <li><strong>Management:</strong> ext. 100</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Useful Links</h5>
                    <ul>
                        <li><a href="order_management.php">Order Management</a></li>
                        <li><a href="customer_database.php">Customer Database</a></li>
                        <li><a href="product_catalog.php">Product Catalog</a></li>
                        <li><a href="shipping_tracking.php">Shipping Tracking</a></li>
                        <li><a href="knowledge_base.php">Knowledge Base</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Completion -->
    <div class="training-section">
        <div class="training-card text-center">
            <h3><i class="fas fa-trophy me-2 text-warning"></i>Training Completion</h3>
            <p class="lead">Congratulations! You've completed the Live Chat Training Program.</p>
            
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>Next Steps</h5>
                        <ol class="text-start">
                            <li>Take the knowledge assessment quiz</li>
                            <li>Complete a supervised practice session</li>
                            <li>Get certified by your supervisor</li>
                            <li>Start handling live customer chats</li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <a href="chat_assessment.php" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-clipboard-check me-2"></i>Take Assessment
                </a>
                <a href="chat_dashboard.php" class="btn btn-success btn-lg">
                    <i class="fas fa-comments me-2"></i>Start Chatting
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Training progress tracking
document.addEventListener('DOMContentLoaded', function() {
    // Track training module completion
    const trainingSteps = document.querySelectorAll('.training-step');
    let completedSteps = 0;
    
    trainingSteps.forEach((step, index) => {
        step.addEventListener('click', function() {
            if (!this.classList.contains('completed')) {
                this.classList.add('completed');
                this.style.borderLeftColor = '#28a745';
                completedSteps++;
                
                // Update progress
                updateTrainingProgress();
            }
        });
    });
    
    function updateTrainingProgress() {
        const progress = (completedSteps / trainingSteps.length) * 100;
        console.log(`Training Progress: ${progress.toFixed(1)}%`);
        
        // You could send this to the server to track progress
        if (progress === 100) {
            showCompletionMessage();
        }
    }
    
    function showCompletionMessage() {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            <strong>Training Complete!</strong> You've reviewed all modules.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }
});
</script>

<?php require_once 'includes/admin_footer.php'; ?>
