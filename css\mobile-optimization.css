/**
 * Mobile Optimization CSS for TeWuNeed
 * Responsive design improvements for mobile devices
 */

/* Base Mobile Styles */
@media (max-width: 768px) {
    /* Typography */
    body {
        font-size: 14px;
        line-height: 1.5;
    }
    
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.6rem; }
    h3 { font-size: 1.4rem; }
    h4 { font-size: 1.2rem; }
    h5 { font-size: 1.1rem; }
    h6 { font-size: 1rem; }
    
    /* Container adjustments */
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    /* Header/Navbar Mobile */
    .navbar-brand {
        font-size: 1.2rem !important;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
    }
    
    .navbar-toggler {
        border: none;
        padding: 0.25rem 0.5rem;
    }
    
    .navbar-collapse {
        margin-top: 1rem;
        border-top: 1px solid rgba(255,255,255,0.1);
        padding-top: 1rem;
    }
    
    /* Hero sections mobile */
    .hero-section,
    .wishlist-hero {
        padding: 40px 0 !important;
    }
    
    .hero-section h1,
    .wishlist-hero h1 {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }
    
    .hero-section .lead,
    .wishlist-hero .lead {
        font-size: 1rem;
    }
    
    /* Product Cards Mobile */
    .product-card {
        margin-bottom: 1.5rem;
        border-radius: 12px;
    }
    
    .product-card .card-img-top {
        height: 180px;
    }
    
    .product-card .card-body {
        padding: 1rem;
    }
    
    .product-card .card-title {
        font-size: 0.95rem;
        line-height: 1.3;
        margin-bottom: 0.5rem;
    }
    
    .product-card .price {
        font-size: 1.1rem;
    }
    
    /* Wishlist Cards Mobile */
    .wishlist-card {
        margin-bottom: 1.5rem;
    }
    
    .wishlist-item-img {
        height: 160px;
    }
    
    /* Filter Section Mobile */
    .filter-section {
        margin-bottom: 2rem;
    }
    
    .search-box {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 0.75rem;
    }
    
    .btn-search {
        padding: 0.75rem 1rem;
        white-space: nowrap;
    }
    
    /* Advanced Filters Mobile */
    #advancedFilters .card-body {
        padding: 1rem;
    }
    
    #advancedFilters .row > div {
        margin-bottom: 1rem;
    }
    
    #advancedFilters input,
    #advancedFilters select {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    /* Sort and Filter Dropdowns */
    .dropdown-menu {
        font-size: 0.9rem;
        max-height: 300px;
        overflow-y: auto;
    }
    
    .dropdown-item {
        padding: 0.75rem 1rem;
    }
    
    /* Buttons Mobile */
    .btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        border-radius: 8px;
    }
    
    .btn-sm {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }
    
    .btn-lg {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
    
    /* Touch-friendly elements */
    .wishlist-btn,
    .remove-btn {
        min-width: 44px;
        min-height: 44px;
        touch-action: manipulation;
    }
    
    .item-checkbox {
        transform: scale(1.2);
        margin: 0.5rem;
    }
    
    /* Product Detail Mobile */
    .product-detail-img {
        height: 250px;
        object-fit: cover;
        border-radius: 12px;
    }
    
    .product-info {
        padding: 1rem 0;
    }
    
    .product-price {
        font-size: 1.5rem;
        margin: 1rem 0;
    }
    
    .quantity-controls {
        margin: 1rem 0;
    }
    
    .quantity-controls .btn {
        min-width: 44px;
        min-height: 44px;
    }
    
    /* Cart Mobile */
    .cart-item {
        padding: 1rem;
        border-radius: 12px;
        margin-bottom: 1rem;
    }
    
    .cart-item-img {
        width: 80px;
        height: 80px;
        border-radius: 8px;
    }
    
    .cart-summary {
        position: sticky;
        bottom: 0;
        background: white;
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        margin: 0 -15px;
    }
    
    /* Forms Mobile */
    .form-control,
    .form-select {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 0.75rem;
        border-radius: 8px;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    /* Modal Mobile */
    .modal-dialog {
        margin: 1rem;
    }
    
    .modal-content {
        border-radius: 12px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
    
    /* Pagination Mobile */
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .page-link {
        padding: 0.5rem 0.75rem;
        margin: 0.125rem;
        border-radius: 6px;
    }
    
    /* Tables Mobile */
    .table-responsive {
        border-radius: 8px;
    }
    
    .table {
        font-size: 0.85rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
        vertical-align: middle;
    }
    
    /* Alerts Mobile */
    .alert {
        border-radius: 8px;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
    }
    
    /* Badges Mobile */
    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
        border-radius: 6px;
    }
    
    /* Search Suggestions Mobile */
    .search-suggestions {
        border-radius: 0 0 12px 12px !important;
        max-height: 300px !important;
    }
    
    .suggestion-item {
        padding: 1rem !important;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .suggestion-item img {
        width: 35px !important;
        height: 35px !important;
        margin-right: 0.75rem !important;
    }
    
    /* Collection Tabs Mobile */
    .collection-tabs {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .collection-tabs .nav-link {
        margin-right: 0;
        margin-bottom: 0.5rem;
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
    }
    
    /* Empty States Mobile */
    .empty-wishlist,
    .empty-cart {
        padding: 3rem 1rem;
    }
    
    .empty-wishlist i,
    .empty-cart i {
        font-size: 3rem !important;
    }
    
    /* Footer Mobile */
    .footer {
        padding: 2rem 0;
    }
    
    .footer .row > div {
        margin-bottom: 1.5rem;
    }
    
    /* Utility Classes Mobile */
    .d-mobile-none {
        display: none !important;
    }
    
    .d-mobile-block {
        display: block !important;
    }
    
    .d-mobile-flex {
        display: flex !important;
    }
    
    .text-mobile-center {
        text-align: center !important;
    }
    
    .mb-mobile-3 {
        margin-bottom: 1rem !important;
    }
    
    .p-mobile-2 {
        padding: 0.5rem !important;
    }
}

/* Extra Small Devices (phones, less than 576px) */
@media (max-width: 575.98px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .hero-section,
    .wishlist-hero {
        padding: 30px 0 !important;
    }
    
    .product-card .card-img-top {
        height: 150px;
    }
    
    .wishlist-item-img {
        height: 140px;
    }
    
    .btn {
        font-size: 0.85rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .search-box {
        font-size: 16px;
    }
    
    /* Stack filter buttons vertically on very small screens */
    .filter-section .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .filter-section .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: 8px !important;
    }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section,
    .wishlist-hero {
        padding: 20px 0 !important;
    }
    
    .product-card .card-img-top {
        height: 120px;
    }
    
    .wishlist-item-img {
        height: 120px;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .product-card:hover,
    .wishlist-card:hover {
        transform: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .product-card:hover .card-img-top,
    .wishlist-card:hover .wishlist-item-img {
        transform: none;
    }
    
    /* Increase touch targets */
    .btn,
    .nav-link,
    .dropdown-item,
    .page-link {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Add active states for better feedback */
    .btn:active {
        transform: scale(0.98);
    }
    
    .product-card:active {
        transform: scale(0.98);
    }
}
