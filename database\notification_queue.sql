-- Create notification queue table for real-time notifications
USE db_tewuneed;

-- Create notification_queue table for SSE
CREATE TABLE IF NOT EXISTS notification_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notification_data JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_created (user_id, created_at)
);

-- Update notifications table if needed
ALTER TABLE notifications 
ADD COLUMN IF NOT EXISTS priority ENUM('low', 'normal', 'high') DEFAULT 'normal' AFTER data,
ADD COLUMN IF NOT EXISTS read_at TIMESTAMP NULL AFTER is_read,
ADD INDEX IF NOT EXISTS idx_user_unread (user_id, is_read),
ADD INDEX IF NOT EXISTS idx_user_type (user_id, type),
ADD INDEX IF NOT EXISTS idx_priority (priority);

SELECT 'Notification queue table created successfully!' as status;
