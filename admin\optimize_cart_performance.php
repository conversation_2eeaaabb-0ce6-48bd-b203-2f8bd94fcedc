<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = 'Cart Performance Optimization';
require_once 'includes/admin_header.php';

// Handle optimization request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'run_optimization':
            $result = runCartOptimization();
            break;
        case 'test_performance':
            $result = testCartPerformance();
            break;
        case 'clear_cache':
            $result = clearCartCache();
            break;
    }
}

function runCartOptimization() {
    global $conn;
    
    try {
        // Read and execute optimization SQL
        $sql = file_get_contents('../database/optimize_cart_performance.sql');
        
        // Split SQL into individual statements
        $statements = explode(';', $sql);
        $executed = 0;
        $errors = [];
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(--|\/\*)/', $statement)) {
                try {
                    $conn->exec($statement);
                    $executed++;
                } catch (PDOException $e) {
                    $errors[] = "Error executing statement: " . $e->getMessage();
                }
            }
        }
        
        return [
            'success' => true,
            'message' => "Optimization completed. Executed {$executed} statements.",
            'errors' => $errors
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Optimization failed: ' . $e->getMessage()
        ];
    }
}

function testCartPerformance() {
    global $conn;
    
    $results = [];
    
    try {
        // Test 1: Add to cart performance
        $start_time = microtime(true);
        
        for ($i = 0; $i < 10; $i++) {
            $stmt = $conn->prepare("CALL AddToCartFast(1, 1, 1)");
            $stmt->execute();
        }
        
        $add_to_cart_time = microtime(true) - $start_time;
        $results['add_to_cart'] = [
            'time' => round($add_to_cart_time * 1000, 2) . 'ms',
            'avg_per_operation' => round(($add_to_cart_time / 10) * 1000, 2) . 'ms'
        ];
        
        // Test 2: Cart count retrieval
        $start_time = microtime(true);
        
        for ($i = 0; $i < 100; $i++) {
            $stmt = $conn->prepare("SELECT GetCartCount(1)");
            $stmt->execute();
        }
        
        $cart_count_time = microtime(true) - $start_time;
        $results['cart_count'] = [
            'time' => round($cart_count_time * 1000, 2) . 'ms',
            'avg_per_operation' => round(($cart_count_time / 100) * 1000, 2) . 'ms'
        ];
        
        // Test 3: Cart summary query
        $start_time = microtime(true);
        
        $stmt = $conn->prepare("SELECT * FROM cart_summary WHERE user_id = 1");
        $stmt->execute();
        
        $cart_summary_time = microtime(true) - $start_time;
        $results['cart_summary'] = [
            'time' => round($cart_summary_time * 1000, 2) . 'ms'
        ];
        
        // Test 4: Database indexes effectiveness
        $stmt = $conn->prepare("
            EXPLAIN SELECT ci.* FROM cart_items ci 
            JOIN carts c ON ci.cart_id = c.cart_id 
            WHERE c.user_id = 1
        ");
        $stmt->execute();
        $explain_result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $results['index_usage'] = $explain_result;
        
        return [
            'success' => true,
            'results' => $results
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Performance test failed: ' . $e->getMessage()
        ];
    }
}

function clearCartCache() {
    global $conn;
    
    try {
        $stmt = $conn->prepare("DELETE FROM cart_cache");
        $stmt->execute();
        
        return [
            'success' => true,
            'message' => 'Cart cache cleared successfully'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to clear cache: ' . $e->getMessage()
        ];
    }
}

// Get current performance stats
try {
    $stmt = $conn->prepare("SELECT * FROM cart_performance_stats");
    $stmt->execute();
    $performance_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $performance_stats = [];
}
?>

<style>
.optimization-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.performance-metric {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #007bff;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.test-result {
    background: #e8f5e8;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.test-result.warning {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

.test-result.error {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-tachometer-alt me-3"></i>Cart Performance Optimization</h1>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="runOptimization()">
                        <i class="fas fa-rocket me-1"></i>Run Optimization
                    </button>
                    <button class="btn btn-info" onclick="testPerformance()">
                        <i class="fas fa-stopwatch me-1"></i>Test Performance
                    </button>
                    <button class="btn btn-warning" onclick="clearCache()">
                        <i class="fas fa-trash me-1"></i>Clear Cache
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($result)): ?>
    <div class="alert alert-<?php echo $result['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show">
        <?php echo htmlspecialchars($result['message']); ?>
        <?php if (isset($result['errors']) && !empty($result['errors'])): ?>
            <ul class="mt-2 mb-0">
                <?php foreach ($result['errors'] as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- Current Performance Stats -->
    <div class="row">
        <div class="col-12">
            <div class="optimization-card">
                <h3><i class="fas fa-chart-bar me-2"></i>Current Performance Statistics</h3>
                
                <?php if (!empty($performance_stats)): ?>
                <div class="row">
                    <?php foreach ($performance_stats as $stat): ?>
                    <div class="col-md-3">
                        <div class="performance-metric">
                            <div class="metric-value"><?php echo number_format($stat['value']); ?></div>
                            <div class="metric-label"><?php echo ucwords(str_replace('_', ' ', $stat['metric'])); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <p class="text-muted">No performance statistics available. Run optimization first.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Optimization Actions -->
    <div class="row">
        <div class="col-md-6">
            <div class="optimization-card">
                <h4><i class="fas fa-tools me-2"></i>Database Optimization</h4>
                <p>Optimize database structure and add performance indexes for faster cart operations.</p>
                
                <div class="list-group">
                    <div class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Add database indexes for cart tables
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Create optimized stored procedures
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Add cart summary views
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Implement cart caching system
                    </div>
                </div>
                
                <form method="POST" class="mt-3">
                    <input type="hidden" name="action" value="run_optimization">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-rocket me-1"></i>Run Database Optimization
                    </button>
                </form>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="optimization-card">
                <h4><i class="fas fa-stopwatch me-2"></i>Performance Testing</h4>
                <p>Test cart operations performance and identify bottlenecks.</p>
                
                <div class="list-group">
                    <div class="list-group-item">
                        <i class="fas fa-clock text-info me-2"></i>
                        Add to cart operation speed
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-clock text-info me-2"></i>
                        Cart count retrieval speed
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-clock text-info me-2"></i>
                        Cart summary query performance
                    </div>
                    <div class="list-group-item">
                        <i class="fas fa-clock text-info me-2"></i>
                        Database index effectiveness
                    </div>
                </div>
                
                <form method="POST" class="mt-3">
                    <input type="hidden" name="action" value="test_performance">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-stopwatch me-1"></i>Run Performance Test
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Performance Test Results -->
    <?php if (isset($result) && $result['success'] && isset($result['results'])): ?>
    <div class="row">
        <div class="col-12">
            <div class="optimization-card">
                <h4><i class="fas fa-chart-line me-2"></i>Performance Test Results</h4>
                
                <?php foreach ($result['results'] as $test => $data): ?>
                    <?php if ($test !== 'index_usage'): ?>
                    <div class="test-result">
                        <h6><?php echo ucwords(str_replace('_', ' ', $test)); ?></h6>
                        <?php if (is_array($data)): ?>
                            <?php foreach ($data as $key => $value): ?>
                                <div><strong><?php echo ucwords(str_replace('_', ' ', $key)); ?>:</strong> <?php echo $value; ?></div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div><?php echo $data; ?></div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                <?php endforeach; ?>
                
                <?php if (isset($result['results']['index_usage'])): ?>
                <div class="test-result">
                    <h6>Database Index Usage</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Table</th>
                                    <th>Type</th>
                                    <th>Key</th>
                                    <th>Rows</th>
                                    <th>Extra</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($result['results']['index_usage'] as $row): ?>
                                <tr>
                                    <td><?php echo $row['table']; ?></td>
                                    <td><?php echo $row['type']; ?></td>
                                    <td><?php echo $row['key'] ?? 'N/A'; ?></td>
                                    <td><?php echo $row['rows']; ?></td>
                                    <td><?php echo $row['Extra']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recommendations -->
    <div class="row">
        <div class="col-12">
            <div class="optimization-card">
                <h4><i class="fas fa-lightbulb me-2"></i>Performance Recommendations</h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Frontend Optimizations</h6>
                        <ul>
                            <li>Use optimistic UI updates</li>
                            <li>Implement request debouncing</li>
                            <li>Cache cart count in localStorage</li>
                            <li>Use fetch API instead of jQuery</li>
                            <li>Minimize DOM manipulations</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Backend Optimizations</h6>
                        <ul>
                            <li>Use prepared statements</li>
                            <li>Implement database connection pooling</li>
                            <li>Add Redis for session caching</li>
                            <li>Use stored procedures for complex operations</li>
                            <li>Implement proper database indexing</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function runOptimization() {
    if (confirm('This will optimize the database structure. Continue?')) {
        document.querySelector('form [name="action"][value="run_optimization"]').closest('form').submit();
    }
}

function testPerformance() {
    document.querySelector('form [name="action"][value="test_performance"]').closest('form').submit();
}

function clearCache() {
    if (confirm('This will clear all cart cache data. Continue?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type="hidden" name="action" value="clear_cache">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php require_once 'includes/admin_footer.php'; ?>
