/**
 * Real-time Notifications Client
 * Handles Server-Sent Events for real-time notifications
 */

class RealTimeNotifications {
    constructor() {
        this.eventSource = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.isConnected = false;
        this.notificationQueue = [];
        
        this.init();
    }
    
    init() {
        // Check if user is logged in
        if (!this.isUserLoggedIn()) {
            console.log('User not logged in, skipping real-time notifications');
            return;
        }
        
        this.connect();
        this.setupNotificationUI();
        this.bindEvents();
    }
    
    isUserLoggedIn() {
        // Check if user session exists (you can customize this check)
        return document.body.dataset.userId || 
               document.querySelector('meta[name="user-id"]') ||
               localStorage.getItem('user_logged_in') === 'true';
    }
    
    connect() {
        if (this.eventSource) {
            this.eventSource.close();
        }
        
        console.log('Connecting to notification stream...');
        
        this.eventSource = new EventSource('sse/notifications.php');
        
        // Connection opened
        this.eventSource.addEventListener('open', (e) => {
            console.log('Connected to notification stream');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.reconnectDelay = 1000;
            this.showConnectionStatus('connected');
        });
        
        // Connection confirmed
        this.eventSource.addEventListener('connected', (e) => {
            const data = JSON.parse(e.data);
            console.log('Notification stream confirmed:', data);
        });
        
        // New notification received
        this.eventSource.addEventListener('notification', (e) => {
            const data = JSON.parse(e.data);
            this.handleNewNotification(data.notification);
        });
        
        // Notification count update
        this.eventSource.addEventListener('count_update', (e) => {
            const data = JSON.parse(e.data);
            this.updateNotificationCounts(data.counts);
        });
        
        // Heartbeat
        this.eventSource.addEventListener('heartbeat', (e) => {
            // console.log('Heartbeat received');
        });
        
        // Error handling
        this.eventSource.addEventListener('error', (e) => {
            console.error('SSE Error:', e);
            this.isConnected = false;
            this.showConnectionStatus('error');
            this.handleReconnect();
        });
        
        // Connection error
        this.eventSource.onerror = (e) => {
            console.error('SSE Connection Error:', e);
            this.isConnected = false;
            this.showConnectionStatus('disconnected');
            this.handleReconnect();
        };
    }
    
    handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
            this.showConnectionStatus('failed');
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        setTimeout(() => {
            this.connect();
        }, this.reconnectDelay);
        
        // Exponential backoff
        this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
    }
    
    handleNewNotification(notification) {
        console.log('New notification received:', notification);
        
        // Add to queue
        this.notificationQueue.push(notification);
        
        // Show notification
        this.showNotification(notification);
        
        // Update UI
        this.updateNotificationUI();
        
        // Play sound if enabled
        this.playNotificationSound(notification);
        
        // Show browser notification if permission granted
        this.showBrowserNotification(notification);
    }
    
    showNotification(notification) {
        // Create notification element
        const notificationEl = this.createNotificationElement(notification);
        
        // Add to notification container
        const container = this.getNotificationContainer();
        container.insertBefore(notificationEl, container.firstChild);
        
        // Auto-remove after delay for non-important notifications
        if (notification.priority !== 'high') {
            setTimeout(() => {
                this.removeNotificationElement(notificationEl);
            }, 5000);
        }
        
        // Trigger animation
        setTimeout(() => {
            notificationEl.classList.add('show');
        }, 100);
    }
    
    createNotificationElement(notification) {
        const div = document.createElement('div');
        div.className = `notification-item notification-${notification.type} priority-${notification.priority}`;
        div.dataset.notificationId = notification.id;
        
        const iconClass = this.getNotificationIcon(notification.type);
        const timeAgo = this.formatTimeAgo(new Date(notification.created_at));
        
        div.innerHTML = `
            <div class="notification-content">
                <div class="notification-header">
                    <i class="${iconClass} notification-icon"></i>
                    <span class="notification-title">${this.escapeHtml(notification.title)}</span>
                    <button class="notification-close" onclick="this.closest('.notification-item').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="notification-body">
                    <p class="notification-message">${this.escapeHtml(notification.message)}</p>
                    <small class="notification-time">${timeAgo}</small>
                </div>
                ${notification.data && notification.data.action_url ? 
                    `<div class="notification-actions">
                        <a href="${notification.data.action_url}" class="btn btn-sm btn-primary">View Details</a>
                    </div>` : ''
                }
            </div>
        `;
        
        return div;
    }
    
    getNotificationIcon(type) {
        const icons = {
            'order': 'fas fa-shopping-bag text-primary',
            'payment': 'fas fa-credit-card text-success',
            'system': 'fas fa-cog text-info',
            'promotion': 'fas fa-tag text-warning',
            'default': 'fas fa-bell text-secondary'
        };
        
        return icons[type] || icons.default;
    }
    
    updateNotificationCounts(counts) {
        // Update notification badges in header
        const countElements = document.querySelectorAll('.notification-count');
        countElements.forEach(element => {
            element.textContent = counts.unread;
            element.style.display = counts.unread > 0 ? 'inline' : 'none';
        });
        
        // Update page title if there are unread notifications
        if (counts.unread > 0) {
            document.title = `(${counts.unread}) ${document.title.replace(/^\(\d+\)\s/, '')}`;
        } else {
            document.title = document.title.replace(/^\(\d+\)\s/, '');
        }
    }
    
    setupNotificationUI() {
        // Create notification container if it doesn't exist
        if (!document.getElementById('notificationContainer')) {
            const container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        
        // Add notification styles
        this.addNotificationStyles();
        
        // Request browser notification permission
        this.requestNotificationPermission();
    }
    
    getNotificationContainer() {
        return document.getElementById('notificationContainer');
    }
    
    addNotificationStyles() {
        if (document.getElementById('notificationStyles')) {
            return;
        }
        
        const style = document.createElement('style');
        style.id = 'notificationStyles';
        style.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
                pointer-events: none;
            }
            
            .notification-item {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                margin-bottom: 10px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                pointer-events: auto;
                border-left: 4px solid #007bff;
            }
            
            .notification-item.show {
                opacity: 1;
                transform: translateX(0);
            }
            
            .notification-item.notification-order {
                border-left-color: #007bff;
            }
            
            .notification-item.notification-payment {
                border-left-color: #28a745;
            }
            
            .notification-item.notification-system {
                border-left-color: #17a2b8;
            }
            
            .notification-item.notification-promotion {
                border-left-color: #ffc107;
            }
            
            .notification-item.priority-high {
                border-left-width: 6px;
                box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            }
            
            .notification-content {
                padding: 15px;
            }
            
            .notification-header {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
            }
            
            .notification-icon {
                margin-right: 8px;
                font-size: 1.1rem;
            }
            
            .notification-title {
                font-weight: 600;
                flex: 1;
                font-size: 0.9rem;
            }
            
            .notification-close {
                background: none;
                border: none;
                color: #6c757d;
                cursor: pointer;
                padding: 2px;
                border-radius: 3px;
            }
            
            .notification-close:hover {
                background: #f8f9fa;
                color: #495057;
            }
            
            .notification-message {
                margin: 0;
                font-size: 0.85rem;
                color: #6c757d;
                line-height: 1.4;
            }
            
            .notification-time {
                color: #adb5bd;
                font-size: 0.75rem;
            }
            
            .notification-actions {
                margin-top: 10px;
                padding-top: 10px;
                border-top: 1px solid #f8f9fa;
            }
            
            .connection-status {
                position: fixed;
                bottom: 20px;
                right: 20px;
                padding: 8px 12px;
                border-radius: 20px;
                font-size: 0.8rem;
                z-index: 10000;
                transition: all 0.3s ease;
            }
            
            .connection-status.connected {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            
            .connection-status.disconnected {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            
            .connection-status.error {
                background: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
            
            @media (max-width: 768px) {
                .notification-container {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                    max-width: none;
                }
                
                .connection-status {
                    bottom: 10px;
                    right: 10px;
                }
            }
        `;
        
        document.head.appendChild(style);
    }
    
    showConnectionStatus(status) {
        let statusEl = document.getElementById('connectionStatus');
        
        if (!statusEl) {
            statusEl = document.createElement('div');
            statusEl.id = 'connectionStatus';
            statusEl.className = 'connection-status';
            document.body.appendChild(statusEl);
        }
        
        statusEl.className = `connection-status ${status}`;
        
        const messages = {
            'connected': '<i class="fas fa-wifi me-1"></i>Connected',
            'disconnected': '<i class="fas fa-wifi-slash me-1"></i>Disconnected',
            'error': '<i class="fas fa-exclamation-triangle me-1"></i>Connection Error',
            'failed': '<i class="fas fa-times-circle me-1"></i>Connection Failed'
        };
        
        statusEl.innerHTML = messages[status] || status;
        
        // Auto-hide connected status after 3 seconds
        if (status === 'connected') {
            setTimeout(() => {
                statusEl.style.opacity = '0';
                setTimeout(() => {
                    if (statusEl.parentNode) {
                        statusEl.parentNode.removeChild(statusEl);
                    }
                }, 300);
            }, 3000);
        }
    }
    
    playNotificationSound(notification) {
        // Only play sound for high priority notifications
        if (notification.priority === 'high') {
            try {
                const audio = new Audio('assets/sounds/notification.mp3');
                audio.volume = 0.3;
                audio.play().catch(e => {
                    // Ignore audio play errors (user interaction required)
                });
            } catch (e) {
                // Ignore audio errors
            }
        }
    }
    
    showBrowserNotification(notification) {
        if (Notification.permission === 'granted') {
            const browserNotification = new Notification(notification.title, {
                body: notification.message,
                icon: 'assets/img/logo-small.png',
                tag: `notification-${notification.id}`,
                requireInteraction: notification.priority === 'high'
            });
            
            browserNotification.onclick = () => {
                window.focus();
                if (notification.data && notification.data.action_url) {
                    window.location.href = notification.data.action_url;
                }
                browserNotification.close();
            };
            
            // Auto-close after 5 seconds for non-high priority
            if (notification.priority !== 'high') {
                setTimeout(() => {
                    browserNotification.close();
                }, 5000);
            }
        }
    }
    
    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }
    
    updateNotificationUI() {
        // Update any notification-related UI elements
        const event = new CustomEvent('notificationReceived', {
            detail: { queue: this.notificationQueue }
        });
        document.dispatchEvent(event);
    }
    
    bindEvents() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Page is hidden, reduce check frequency
            } else {
                // Page is visible, normal frequency
                if (!this.isConnected) {
                    this.connect();
                }
            }
        });
        
        // Handle window focus
        window.addEventListener('focus', () => {
            if (!this.isConnected) {
                this.connect();
            }
        });
        
        // Handle before unload
        window.addEventListener('beforeunload', () => {
            if (this.eventSource) {
                this.eventSource.close();
            }
        });
    }
    
    removeNotificationElement(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 300);
    }
    
    formatTimeAgo(date) {
        const now = new Date();
        const diff = now - date;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (seconds < 60) return 'Just now';
        if (minutes < 60) return `${minutes}m ago`;
        if (hours < 24) return `${hours}h ago`;
        return date.toLocaleDateString();
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isConnected = false;
    }
}

// Initialize real-time notifications when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.realTimeNotifications = new RealTimeNotifications();
});

// Export for use in other scripts
window.RealTimeNotifications = RealTimeNotifications;
