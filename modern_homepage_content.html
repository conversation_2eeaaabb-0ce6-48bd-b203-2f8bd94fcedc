<!-- Modern TeWuNeed Homepage Content -->

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">
                        Welcome to <span class="brand-highlight">TeWuNeed</span>
                    </h1>
                    <p class="hero-subtitle">
                        Discover amazing products at unbeatable prices. Your one-stop destination for all your shopping needs.
                    </p>
                    
                    <!-- Search Bar -->
                    <div class="hero-search mb-4">
                        <form action="products.php" method="GET" class="search-form">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control search-input" 
                                       placeholder="Search for products..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                                <button class="btn search-btn" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Hero Stats -->
                    <div class="hero-stats">
                        <div class="hero-stat">
                            <span class="hero-stat-number"><?php echo count($featuredProducts); ?>+</span>
                            <span class="hero-stat-label">Products</span>
                        </div>
                        <div class="hero-stat">
                            <span class="hero-stat-number">1000+</span>
                            <span class="hero-stat-label">Happy Customers</span>
                        </div>
                        <div class="hero-stat">
                            <span class="hero-stat-number">24/7</span>
                            <span class="hero-stat-label">Support</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                         alt="Shopping" class="img-fluid rounded-4 shadow-lg">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section py-5">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">Shop by Category</h2>
            <p class="section-subtitle">Explore our wide range of product categories</p>
        </div>
        
        <div class="row g-4">
            <?php
            // Get categories
            try {
                $stmt = $conn->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY name ASC LIMIT 6");
                $categories = $stmt->fetchAll();
                
                $category_icons = [
                    'Electronics' => 'fas fa-laptop',
                    'Fashion' => 'fas fa-tshirt',
                    'Home & Garden' => 'fas fa-home',
                    'Sports' => 'fas fa-dumbbell',
                    'Books' => 'fas fa-book',
                    'Health' => 'fas fa-heartbeat'
                ];
                
                foreach ($categories as $index => $category):
                    $icon = $category_icons[$category['name']] ?? 'fas fa-tag';
            ?>
            <div class="col-lg-2 col-md-4 col-6">
                <a href="products.php?category=<?php echo $category['category_id']; ?>" class="category-card">
                    <div class="category-icon">
                        <i class="<?php echo $icon; ?>"></i>
                    </div>
                    <h6 class="category-name"><?php echo htmlspecialchars($category['name']); ?></h6>
                </a>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="featured-products-section py-5 bg-light">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">Featured Products</h2>
            <p class="section-subtitle">Discover our handpicked selection of amazing products</p>
        </div>
        
        <div class="row g-4">
            <?php if (!empty($featuredProducts)): ?>
                <?php foreach (array_slice($featuredProducts, 0, 8) as $product): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="product-card">
                        <div class="product-image-container">
                            <?php if ($product['stock'] <= 0): ?>
                                <div class="product-badge bg-secondary">Out of Stock</div>
                            <?php elseif ($product['is_featured']): ?>
                                <div class="product-badge bg-warning">Featured</div>
                            <?php endif; ?>
                            
                            <button class="product-wishlist">
                                <i class="far fa-heart"></i>
                            </button>
                            
                            <img src="<?php echo !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'Images/default-product.jpg'; ?>" 
                                 alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                 class="product-img">
                        </div>
                        
                        <div class="product-info">
                            <div class="product-category"><?php echo htmlspecialchars($product['category_name'] ?? 'General'); ?></div>
                            <h6 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h6>
                            
                            <div class="product-rating">
                                <div class="rating-stars">
                                    <?php
                                    $rating = round($product['rating']);
                                    for ($i = 1; $i <= 5; $i++) {
                                        echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                    }
                                    ?>
                                </div>
                                <span class="rating-text">(<?php echo $product['review_count']; ?>)</span>
                            </div>
                            
                            <div class="product-price">
                                Rp <?php echo number_format($product['price'], 0, ',', '.'); ?>
                            </div>
                            
                            <div class="product-actions">
                                <?php if ($product['stock'] > 0): ?>
                                    <button class="btn btn-add-to-cart" onclick="addToCart(<?php echo $product['product_id']; ?>)">
                                        <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                    </button>
                                    <button class="btn btn-quick-view" onclick="quickView(<?php echo $product['product_id']; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                <?php else: ?>
                                    <button class="btn btn-secondary" disabled>
                                        <i class="fas fa-times me-2"></i>Out of Stock
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center">
                    <div class="empty-state">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No products available</h5>
                        <p class="text-muted">Check back later for new products!</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="text-center mt-5">
            <a href="products.php" class="btn btn-primary btn-lg">
                <i class="fas fa-th-large me-2"></i>View All Products
            </a>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">Why Choose TeWuNeed?</h2>
            <p class="section-subtitle">We provide the best shopping experience for our customers</p>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h5 class="feature-title">Fast Delivery</h5>
                    <p class="feature-description">Quick and reliable delivery to your doorstep</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h5 class="feature-title">Secure Payment</h5>
                    <p class="feature-description">Your payment information is safe and secure</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h5 class="feature-title">24/7 Support</h5>
                    <p class="feature-description">Round-the-clock customer support</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <h5 class="feature-title">Easy Returns</h5>
                    <p class="feature-description">Hassle-free return and exchange policy</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h3 class="newsletter-title">Stay Updated!</h3>
                <p class="newsletter-subtitle">Subscribe to our newsletter for the latest deals and updates</p>
            </div>
            <div class="col-lg-6">
                <form class="newsletter-form">
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="Enter your email address">
                        <button class="btn btn-light" type="submit">
                            <i class="fas fa-paper-plane me-2"></i>Subscribe
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
