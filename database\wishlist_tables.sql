-- Wishlist Tables for TeWuNeed E-commerce
-- Run this SQL to add wishlist functionality

USE db_tewuneed;

-- Create wishlist table
CREATE TABLE IF NOT EXISTS wishlist (
    wishlist_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT NULL COMMENT 'Optional notes from user about why they want this product',
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium' COMMENT 'User priority for this item',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_product (user_id, product_id) COMMENT 'Prevent duplicate wishlist items'
);

-- Create wishlist collections table (for organizing wishlist items)
CREATE TABLE IF NOT EXISTS wishlist_collections (
    collection_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    collection_name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE COMMENT 'Whether this collection can be shared',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Create junction table for wishlist items in collections
CREATE TABLE IF NOT EXISTS wishlist_collection_items (
    collection_item_id INT AUTO_INCREMENT PRIMARY KEY,
    collection_id INT NOT NULL,
    wishlist_id INT NOT NULL,
    added_to_collection_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (collection_id) REFERENCES wishlist_collections(collection_id) ON DELETE CASCADE,
    FOREIGN KEY (wishlist_id) REFERENCES wishlist(wishlist_id) ON DELETE CASCADE,
    UNIQUE KEY unique_collection_wishlist (collection_id, wishlist_id)
);

-- Create default collection for each existing user
INSERT IGNORE INTO wishlist_collections (user_id, collection_name, description, is_public)
SELECT user_id, 'My Favorites', 'Default wishlist collection', FALSE
FROM users;

-- Add indexes for better performance
CREATE INDEX idx_wishlist_user_id ON wishlist(user_id);
CREATE INDEX idx_wishlist_product_id ON wishlist(product_id);
CREATE INDEX idx_wishlist_added_at ON wishlist(added_at);
CREATE INDEX idx_wishlist_collections_user_id ON wishlist_collections(user_id);

-- Add trigger to create default collection for new users
DELIMITER //
CREATE TRIGGER IF NOT EXISTS create_default_wishlist_collection
    AFTER INSERT ON users
    FOR EACH ROW
BEGIN
    INSERT INTO wishlist_collections (user_id, collection_name, description, is_public)
    VALUES (NEW.user_id, 'My Favorites', 'Default wishlist collection', FALSE);
END//
DELIMITER ;

-- Sample data for testing (optional)
-- INSERT INTO wishlist (user_id, product_id, notes, priority) VALUES
-- (1, 1, 'Need this for my kitchen', 'high'),
-- (1, 5, 'Looks interesting', 'medium'),
-- (2, 3, 'Birthday gift idea', 'high');

SELECT 'Wishlist tables created successfully!' as status;
