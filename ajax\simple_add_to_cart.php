<?php
/**
 * Simple Add to Cart - No Complex Dependencies
 */

session_start();
require_once '../includes/db_connect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in JSON response

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id']) && !isset($_SESSION['firebase_user_id'])) {
        throw new Exception('Please login to add items to cart');
    }

    // Get user ID with proper fallback
    $user_id = $_SESSION['local_user_id'] ?? $_SESSION['user_id'] ?? $_SESSION['firebase_user_id'];

    if (!$user_id) {
        throw new Exception('User ID not found in session');
    }
    
    // Validate inputs
    if (!isset($_POST['product_id']) || !is_numeric($_POST['product_id'])) {
        throw new Exception('Invalid product ID');
    }
    
    $product_id = (int)$_POST['product_id'];
    $quantity = isset($_POST['quantity']) && is_numeric($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
    
    if ($quantity <= 0) {
        throw new Exception('Quantity must be greater than zero');
    }
    
    // Start transaction
    $conn->beginTransaction();
    
    // Check if product exists and get details
    $stmt = $conn->prepare("SELECT product_id, COALESCE(name, NAME) as name, price, stock FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        throw new Exception('Product not found');
    }
    
    // Simple stock check
    if ($product['stock'] < $quantity) {
        throw new Exception('Insufficient stock. Available: ' . $product['stock']);
    }
    
    // Get or create user's cart
    $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$cart) {
        // Create new cart
        $stmt = $conn->prepare("INSERT INTO carts (user_id, created_at) VALUES (?, NOW())");
        $stmt->execute([$user_id]);
        $cart_id = $conn->lastInsertId();
    } else {
        $cart_id = $cart['cart_id'];
    }
    
    // Check if product already in cart
    $stmt = $conn->prepare("SELECT cart_item_id, quantity FROM cart_items WHERE cart_id = ? AND product_id = ?");
    $stmt->execute([$cart_id, $product_id]);
    $existing_item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing_item) {
        // Update existing item
        $new_quantity = $existing_item['quantity'] + $quantity;
        
        // Check total stock
        if ($product['stock'] < $new_quantity) {
            throw new Exception('Insufficient stock for total quantity. Available: ' . $product['stock'] . ', In cart: ' . $existing_item['quantity']);
        }
        
        $stmt = $conn->prepare("UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE cart_item_id = ?");
        $stmt->execute([$new_quantity, $existing_item['cart_item_id']]);
        
        $final_quantity = $new_quantity;
        $message = 'Cart updated successfully';
    } else {
        // Add new item
        $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity, added_at) VALUES (?, ?, ?, NOW())");
        $stmt->execute([$cart_id, $product_id, $quantity]);
        
        $final_quantity = $quantity;
        $message = 'Product added to cart successfully';
    }
    
    // Get updated cart count
    $stmt = $conn->prepare("SELECT SUM(quantity) as total_items FROM cart_items WHERE cart_id = ?");
    $stmt->execute([$cart_id]);
    $cart_count = $stmt->fetchColumn() ?: 0;
    
    // Commit transaction
    $conn->commit();
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => $message,
        'cart_count' => $cart_count,
        'product_id' => $product_id,
        'product_name' => $product['name'],
        'quantity' => $final_quantity,
        'available_stock' => $product['stock']
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
