/**
 * TeWuNeed Modern Homepage JavaScript
 * Enhanced functionality for the modern homepage design
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize modern homepage features
    initializeModernFeatures();
    
    // Initialize animations
    initializeAnimations();
    
    // Initialize interactive elements
    initializeInteractiveElements();
    
});

/**
 * Initialize modern homepage features
 */
function initializeModernFeatures() {
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Search functionality enhancement
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.closest('form').submit();
            }
        });
        
        // Add search suggestions (placeholder for future enhancement)
        searchInput.addEventListener('input', function() {
            // Future: Add search suggestions dropdown
        });
    }
    
    // Product card hover effects
    document.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Category card hover effects
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.category-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.category-icon');
            if (icon) {
                icon.style.transform = 'scale(1)';
            }
        });
    });
}

/**
 * Initialize scroll animations
 */
function initializeAnimations() {
    
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.product-card, .category-card, .feature-card').forEach(el => {
        observer.observe(el);
    });
    
    // Add CSS for fade-in animation
    if (!document.querySelector('#modern-animations-css')) {
        const style = document.createElement('style');
        style.id = 'modern-animations-css';
        style.textContent = `
            .animate-fade-in {
                animation: fadeInUp 0.6s ease forwards;
            }
            
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .product-card, .category-card, .feature-card {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.3s ease;
            }
            
            .product-card.animate-fade-in,
            .category-card.animate-fade-in,
            .feature-card.animate-fade-in {
                opacity: 1;
                transform: translateY(0);
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Initialize interactive elements
 */
function initializeInteractiveElements() {
    
    // Wishlist functionality
    document.querySelectorAll('.product-wishlist').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const icon = this.querySelector('i');
            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                this.style.background = '#ff6b6b';
                this.style.color = 'white';
                showNotification('Added to wishlist!', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                this.style.background = 'rgba(255,255,255,0.9)';
                this.style.color = '#2d3436';
                showNotification('Removed from wishlist!', 'info');
            }
        });
    });
    
    // Enhanced product rating display
    document.querySelectorAll('.rating-stars').forEach(rating => {
        const stars = rating.querySelectorAll('i');
        stars.forEach((star, index) => {
            star.addEventListener('mouseenter', function() {
                // Highlight stars on hover (for future rating functionality)
                for (let i = 0; i <= index; i++) {
                    stars[i].style.color = '#ffd43b';
                }
            });
            
            star.addEventListener('mouseleave', function() {
                // Reset star colors
                stars.forEach(s => {
                    s.style.color = s.classList.contains('fas') ? '#ffd43b' : '#e9ecef';
                });
            });
        });
    });
    
    // Newsletter form enhancement
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        const emailInput = newsletterForm.querySelector('input[type="email"]');
        const submitBtn = newsletterForm.querySelector('button[type="submit"]');
        
        emailInput.addEventListener('input', function() {
            const isValid = this.checkValidity();
            submitBtn.disabled = !isValid;
            submitBtn.style.opacity = isValid ? '1' : '0.6';
        });
    }
    
    // Parallax effect for hero section
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroSection = document.querySelector('.hero-section');
        
        if (heroSection) {
            const rate = scrolled * -0.5;
            heroSection.style.transform = `translateY(${rate}px)`;
        }
    });
}

/**
 * Enhanced notification system
 */
function showNotification(message, type = 'info', duration = 3000) {
    // Remove existing notifications
    document.querySelectorAll('.notification-toast').forEach(n => n.remove());
    
    const notification = document.createElement('div');
    notification.className = `alert alert-${getAlertClass(type)} notification-toast`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        border: none;
        border-radius: 12px;
    `;
    
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${getIcon(type)} me-2"></i>
            <span class="flex-grow-1">${message}</span>
            <button type="button" class="btn-close" onclick="this.closest('.notification-toast').remove()"></button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}

function getAlertClass(type) {
    switch(type) {
        case 'success': return 'success';
        case 'error': return 'danger';
        case 'warning': return 'warning';
        default: return 'info';
    }
}

function getIcon(type) {
    switch(type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

/**
 * Loading states for buttons
 */
function setButtonLoading(button, loading = true) {
    if (loading) {
        button.disabled = true;
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
    } else {
        button.disabled = false;
        button.innerHTML = button.dataset.originalText || button.innerHTML;
    }
}

/**
 * Enhanced cart functionality
 */
function addToCartEnhanced(productId, productName, quantity = 1) {
    const button = event.target.closest('button');
    setButtonLoading(button, true);
    
    // Simulate API call
    setTimeout(() => {
        setButtonLoading(button, false);
        showNotification(`${productName} added to cart!`, 'success');
        
        // Update cart count with animation
        updateCartCountAnimated();
    }, 1000);
}

function updateCartCountAnimated() {
    const cartBadge = document.querySelector('.cart-count');
    if (cartBadge) {
        cartBadge.style.transform = 'scale(1.3)';
        setTimeout(() => {
            cartBadge.style.transform = 'scale(1)';
        }, 200);
    }
}

// Add modern CSS animations
const modernAnimationsCSS = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .notification-toast {
        animation: slideInRight 0.3s ease;
    }
    
    .btn {
        transition: all 0.3s ease;
    }
    
    .btn:hover {
        transform: translateY(-2px);
    }
    
    .product-card {
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }
    
    .category-icon {
        transition: transform 0.3s ease;
    }
    
    .hero-section {
        transition: transform 0.1s ease-out;
    }
`;

// Inject CSS
if (!document.querySelector('#modern-homepage-css')) {
    const style = document.createElement('style');
    style.id = 'modern-homepage-css';
    style.textContent = modernAnimationsCSS;
    document.head.appendChild(style);
}

// Export functions for global use
window.showNotification = showNotification;
window.addToCartEnhanced = addToCartEnhanced;
window.setButtonLoading = setButtonLoading;
