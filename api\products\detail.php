<?php
header('Content-Type: application/json');
require_once '../../config/database.php';
require_once '../../config/response.php';
require_once '../../config/database_error.php';

try {
    // Validate input
    if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
        handleValidationError('Product ID is required and must be numeric');
    }
    
    $productId = (int)$_GET['id'];
    
    // Get database connection
    $conn = getConnection();
    validateDatabaseConnection($conn);
    
    // Get product details with features and specifications
    $stmt = $conn->prepare("
        SELECT
            p.*,
            c.NAME as category_name,
            c.slug as category_slug,
            GROUP_CONCAT(DISTINCT pf.feature_text ORDER BY pf.display_order ASC SEPARATOR '||') as features_list,
            GROUP_CONCAT(DISTINCT CONCAT(ps.spec_name, ': ', ps.spec_value) ORDER BY ps.display_order ASC SEPARATOR '||') as specs_list
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        LEFT JOIN product_features pf ON p.product_id = pf.product_id
        LEFT JOIN product_specifications ps ON p.product_id = ps.product_id
        WHERE p.product_id = ?
        GROUP BY p.product_id
    ");
    
    $stmt->execute([$productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        handleValidationError('Product not found');
    }
    
    // Format product data
    $formattedProduct = [
        'id' => $product['product_id'],
        'sku' => $product['sku'],
        'name' => $product['NAME'],
        'brand' => $product['brand'],
        'category' => [
            'name' => $product['category_name'],
            'slug' => $product['category_slug']
        ],
        'description' => $product['description'],
        'features' => $product['features_list'] ? explode('||', $product['features_list']) : [],
        'specifications' => $product['specs_list'] ? explode('||', $product['specs_list']) : [],
        'price' => [
            'value' => (float)$product['price'],
            'formatted' => 'Rp ' . number_format($product['price'], 0, ',', '.')
        ],
        'rating' => [
            'value' => (float)$product['rating'],
            'count' => (int)$product['rating_count']
        ],
        'stock' => [
            'value' => (int)$product['stock'],
            'unit' => $product['unit']
        ],
        'weight' => [
            'value' => (float)$product['weight'],
            'unit' => 'gram'
        ],
        'image' => $product['image'],
        'is_active' => (bool)$product['is_active'],
        'created_at' => $product['created_at'],
        'updated_at' => $product['updated_at']
    ];
    
    // Get product images
    $stmt = $conn->prepare("
        SELECT image_url, is_primary
        FROM product_images
        WHERE product_id = ?
        ORDER BY is_primary DESC, image_id ASC
    ");
    
    $stmt->execute([$productId]);
    $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedProduct['images'] = $images;
    
    sendSuccessResponse($formattedProduct);
    
} catch (PDOException $e) {
    handleDatabaseError($e);
} catch (Exception $e) {
    handleValidationError($e->getMessage());
} 