<?php
/**
 * Payment Manager Class
 * Mengelola semua operasi pembayaran untuk e-commerce Tewuneed
 */

require_once __DIR__ . '/../config/payment_config.php';
require_once __DIR__ . '/db_connect.php';

class PaymentManager {
    private $conn;
    private $config;

    public function __construct() {
        global $conn;
        $this->conn = $conn;
        $this->config = include __DIR__ . '/../config/payment_config.php';
    }

    /**
     * Get all active payment methods
     */
    public function getActivePaymentMethods($amount = 0, $region = 'other') {
        try {
            $sql = "SELECT * FROM payment_methods WHERE is_active = 1";

            // Filter by amount if provided
            if ($amount > 0) {
                $sql .= " AND min_amount <= :amount AND max_amount >= :amount";
            }

            $sql .= " ORDER BY sort_order ASC";

            $stmt = $this->conn->prepare($sql);
            if ($amount > 0) {
                $stmt->bindParam(':amount', $amount);
            }
            $stmt->execute();

            $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Filter by regional availability
            $available_methods = [];
            foreach ($methods as $method) {
                if ($this->isMethodAvailableInRegion($method['type'], $region)) {
                    // Calculate fee for this method
                    $method['calculated_fee'] = $this->calculateFee($method, $amount);
                    $method['total_amount'] = $amount + $method['calculated_fee'];
                    $method['fee_display'] = $this->formatFeeDisplay($method);
                    $available_methods[] = $method;
                }
            }

            return $available_methods;
        } catch (PDOException $e) {
            error_log("Error getting payment methods: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get payment methods grouped by category
     */
    public function getPaymentMethodsByCategory($amount = 0, $region = 'jakarta') {
        try {
            $stmt = $this->conn->prepare("
                SELECT pm.*, pc.name as category_name, pc.icon as category_icon, pc.sort_order as category_order,
                       COALESCE(pmr.is_available, 1) as region_available
                FROM payment_methods pm
                LEFT JOIN payment_categories pc ON pm.category_id = pc.category_id
                LEFT JOIN payment_method_regions pmr ON pm.method_id = pmr.method_id AND pmr.region = ?
                WHERE pm.is_active = 1
                AND pm.min_amount <= ?
                AND pm.max_amount >= ?
                AND COALESCE(pmr.is_available, 1) = 1
                ORDER BY pc.sort_order ASC, pm.name ASC
            ");
            $stmt->execute([$region, $amount, $amount]);

            $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Group by category
            $grouped_methods = [];
            foreach ($methods as $method) {
                $category = $method['category_name'] ?? 'Other';

                // Calculate fee for this method
                $method['calculated_fee'] = $this->calculateFee($method, $amount);
                $method['total_amount'] = $amount + $method['calculated_fee'];
                $method['fee_display'] = $this->formatFeeDisplay($method);

                if (!isset($grouped_methods[$category])) {
                    $grouped_methods[$category] = [
                        'category_name' => $category,
                        'category_icon' => $method['category_icon'],
                        'category_order' => $method['category_order'] ?? 999,
                        'methods' => []
                    ];
                }

                $grouped_methods[$category]['methods'][] = $method;
            }

            // Sort categories by order
            uasort($grouped_methods, function($a, $b) {
                return $a['category_order'] <=> $b['category_order'];
            });

            return $grouped_methods;
        } catch (PDOException $e) {
            error_log("Error getting payment methods by category: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get payment method by code
     */
    public function getPaymentMethodByCode($code) {
        try {
            $stmt = $this->conn->prepare("
                SELECT pm.*, pc.name as category_name
                FROM payment_methods pm
                LEFT JOIN payment_categories pc ON pm.category_id = pc.category_id
                WHERE pm.code = ? AND pm.is_active = 1
            ");
            $stmt->execute([$code]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting payment method by code: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create payment transaction
     */
    public function createPaymentTransaction($order_id, $user_id, $payment_method_code, $amount, $additional_data = []) {
        try {
            // Get payment method details
            $stmt = $this->conn->prepare("SELECT * FROM payment_methods WHERE code = ? AND is_active = 1");
            $stmt->execute([$payment_method_code]);
            $payment_method = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$payment_method) {
                throw new Exception("Payment method not found or inactive");
            }

            // Validate amount
            if ($amount < $payment_method['min_amount'] || $amount > $payment_method['max_amount']) {
                throw new Exception("Amount is outside allowed range for this payment method");
            }

            // Calculate fee
            $fee_amount = $this->calculateFee($payment_method, $amount);
            $total_amount = $amount + $fee_amount;

            // Generate unique transaction ID
            $transaction_id = $this->generateTransactionId();

            // Calculate expiry time
            $expired_at = date('Y-m-d H:i:s', strtotime('+' . $this->config['payment_config']['transaction_timeout'] . ' hours'));

            // Insert transaction record
            $stmt = $this->conn->prepare("
                INSERT INTO payment_transactions (
                    transaction_id, order_id, user_id, payment_method_id,
                    amount, fee_amount, total_amount, status, expired_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?)
            ");

            $stmt->execute([
                $transaction_id,
                $order_id,
                $user_id,
                $payment_method['id'],
                $amount,
                $fee_amount,
                $total_amount,
                $expired_at
            ]);

            // Log the transaction creation
            $this->logPaymentActivity($transaction_id, 'created', 'Payment transaction created', $additional_data);

            // Process payment based on method type
            $payment_response = $this->processPayment($transaction_id, $payment_method, $total_amount, $additional_data);

            return [
                'success' => true,
                'transaction_id' => $transaction_id,
                'payment_response' => $payment_response
            ];

        } catch (Exception $e) {
            error_log("Error creating payment transaction: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process payment based on method type
     */
    private function processPayment($transaction_id, $payment_method, $amount, $additional_data) {
        switch ($payment_method['type']) {
            case 'ewallet':
                return $this->processEwalletPayment($transaction_id, $payment_method, $amount, $additional_data);

            case 'va':
                return $this->processVirtualAccountPayment($transaction_id, $payment_method, $amount, $additional_data);

            case 'card':
                return $this->processCardPayment($transaction_id, $payment_method, $amount, $additional_data);

            case 'bank_transfer':
                return $this->processBankTransferPayment($transaction_id, $payment_method, $amount, $additional_data);

            case 'cod':
                return $this->processCODPayment($transaction_id, $payment_method, $amount, $additional_data);

            case 'installment':
                return $this->processInstallmentPayment($transaction_id, $payment_method, $amount, $additional_data);

            case 'paylater':
                return $this->processPaylaterPayment($transaction_id, $payment_method, $amount, $additional_data);

            default:
                throw new Exception("Unsupported payment method type");
        }
    }

    /**
     * Process E-wallet payment (GoPay, OVO, DANA, etc.)
     */
    private function processEwalletPayment($transaction_id, $payment_method, $amount, $additional_data) {
        if ($payment_method['provider'] === 'midtrans') {
            return $this->processMidtransEwallet($transaction_id, $payment_method, $amount, $additional_data);
        }

        // Add other providers here
        throw new Exception("E-wallet provider not supported");
    }

    /**
     * Process Virtual Account payment
     */
    private function processVirtualAccountPayment($transaction_id, $payment_method, $amount, $additional_data) {
        if ($payment_method['provider'] === 'midtrans') {
            return $this->processMidtransVA($transaction_id, $payment_method, $amount, $additional_data);
        }

        throw new Exception("Virtual Account provider not supported");
    }

    /**
     * Process Card payment
     */
    private function processCardPayment($transaction_id, $payment_method, $amount, $additional_data) {
        if ($payment_method['provider'] === 'midtrans') {
            return $this->processMidtransCard($transaction_id, $payment_method, $amount, $additional_data);
        }

        throw new Exception("Card payment provider not supported");
    }

    /**
     * Process Bank Transfer payment
     */
    private function processBankTransferPayment($transaction_id, $payment_method, $amount, $additional_data) {
        // For manual bank transfer, just return bank account details
        return [
            'type' => 'bank_transfer',
            'bank_accounts' => $this->config['bank_accounts'],
            'amount' => $amount,
            'transaction_id' => $transaction_id,
            'payment_method' => $payment_method['code'],
            'instructions' => 'Transfer ke salah satu rekening bank di atas dengan nominal yang tepat',
            'additional_info' => $additional_data
        ];
    }

    /**
     * Process COD payment
     */
    private function processCODPayment($transaction_id, $payment_method, $amount, $additional_data) {
        // For COD, just confirm the order
        return [
            'type' => 'cod',
            'amount' => $amount,
            'transaction_id' => $transaction_id,
            'payment_method' => $payment_method['code'],
            'instructions' => 'Pembayaran akan dilakukan saat barang diterima',
            'additional_info' => $additional_data
        ];
    }

    /**
     * Calculate payment fee
     */
    private function calculateFee($payment_method, $amount) {
        $fee = 0;

        // Handle new fee structure
        if (isset($payment_method['fee_type'])) {
            switch ($payment_method['fee_type']) {
                case 'fixed':
                    $fee = $payment_method['fee_amount'] ?? 0;
                    break;

                case 'percentage':
                    $fee = $amount * (($payment_method['fee_percentage'] ?? 0) / 100);
                    break;

                case 'mixed':
                    $fee = ($payment_method['fee_amount'] ?? 0) +
                           ($amount * (($payment_method['fee_percentage'] ?? 0) / 100));
                    break;
            }
        } else {
            // Legacy fee structure
            if ($payment_method['fee_fixed'] > 0) {
                $fee += $payment_method['fee_fixed'];
            }

            if ($payment_method['fee_percentage'] > 0) {
                $fee += $amount * $payment_method['fee_percentage'] / 100;
            }
        }

        return round($fee);
    }

    /**
     * Format fee display for UI
     */
    private function formatFeeDisplay($method) {
        if (($method['calculated_fee'] ?? 0) == 0) {
            return 'Gratis';
        }

        $fee = $method['calculated_fee'];
        $display = 'Rp ' . number_format($fee);

        if (isset($method['fee_type'])) {
            switch ($method['fee_type']) {
                case 'percentage':
                    if (($method['fee_percentage'] ?? 0) > 0) {
                        $display .= ' (' . $method['fee_percentage'] . '%)';
                    }
                    break;

                case 'mixed':
                    $parts = [];
                    if (($method['fee_amount'] ?? 0) > 0) {
                        $parts[] = 'Rp ' . number_format($method['fee_amount']);
                    }
                    if (($method['fee_percentage'] ?? 0) > 0) {
                        $parts[] = $method['fee_percentage'] . '%';
                    }
                    if (!empty($parts)) {
                        $display .= ' (' . implode(' + ', $parts) . ')';
                    }
                    break;
            }
        }

        return $display;
    }

    /**
     * Check if payment method is available in region
     */
    private function isMethodAvailableInRegion($method_type, $region) {
        $availability = $this->config['payment_config']['regional_availability'];

        if (isset($availability[$region])) {
            return in_array('all', $availability[$region]) || in_array($method_type, $availability[$region]);
        }

        return in_array($method_type, $availability['other']);
    }

    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId() {
        return 'TXN' . date('Ymd') . strtoupper(uniqid());
    }

    /**
     * Log payment activity
     */
    private function logPaymentActivity($transaction_id, $event_type, $message, $data = []) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO payment_logs (transaction_id, event_type, message, data, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $transaction_id,
                $event_type,
                $message,
                json_encode($data),
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (PDOException $e) {
            error_log("Error logging payment activity: " . $e->getMessage());
        }
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus($transaction_id, $status, $gateway_response = null) {
        try {
            $sql = "UPDATE payment_transactions SET status = ?, updated_at = NOW()";
            $params = [$status];

            if ($status === 'paid') {
                $sql .= ", paid_at = NOW()";
            }

            if ($gateway_response) {
                $sql .= ", gateway_response = ?";
                $params[] = json_encode($gateway_response);
            }

            $sql .= " WHERE transaction_id = ?";
            $params[] = $transaction_id;

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);

            // Log status update
            $this->logPaymentActivity($transaction_id, 'status_updated', "Status updated to: $status", $gateway_response);

            return true;
        } catch (PDOException $e) {
            error_log("Error updating payment status: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment transaction details
     */
    public function getPaymentTransaction($transaction_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT pt.*, pm.name as payment_method_name, pm.type as payment_method_type, pm.code as payment_method_code
                FROM payment_transactions pt
                JOIN payment_methods pm ON pt.payment_method_id = pm.id
                WHERE pt.transaction_id = ?
            ");
            $stmt->execute([$transaction_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting payment transaction: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Process Midtrans E-wallet payment
     */
    private function processMidtransEwallet($transaction_id, $payment_method, $amount, $additional_data) {
        // This will be implemented with actual Midtrans SDK
        // For now, return mock response
        return [
            'type' => 'ewallet',
            'payment_method' => $payment_method['code'],
            'amount' => $amount,
            'transaction_id' => $transaction_id,
            'qr_code_url' => 'https://api.midtrans.com/qr/' . $transaction_id,
            'deeplink_url' => $this->generateDeeplink($payment_method['code'], $transaction_id),
            'instructions' => 'Scan QR code atau klik link untuk membayar dengan ' . $payment_method['name'],
            'additional_info' => $additional_data
        ];
    }

    /**
     * Process Midtrans Virtual Account payment
     */
    private function processMidtransVA($transaction_id, $payment_method, $amount, $additional_data) {
        // Generate VA number (mock)
        $va_number = $this->generateVANumber($payment_method['code']);

        // Update transaction with VA number
        $stmt = $this->conn->prepare("UPDATE payment_transactions SET va_number = ? WHERE transaction_id = ?");
        $stmt->execute([$va_number, $transaction_id]);

        return [
            'type' => 'va',
            'payment_method' => $payment_method['code'],
            'amount' => $amount,
            'transaction_id' => $transaction_id,
            'va_number' => $va_number,
            'bank_name' => $this->getBankName($payment_method['code']),
            'instructions' => "Transfer ke Virtual Account: $va_number",
            'additional_info' => $additional_data
        ];
    }

    /**
     * Process Midtrans Card payment
     */
    private function processMidtransCard($transaction_id, $payment_method, $amount, $additional_data) {
        return [
            'type' => 'card',
            'payment_method' => $payment_method['code'],
            'amount' => $amount,
            'transaction_id' => $transaction_id,
            'payment_url' => "https://app.midtrans.com/snap/v1/transactions/$transaction_id",
            'instructions' => 'Klik link untuk melanjutkan pembayaran dengan kartu',
            'additional_info' => $additional_data
        ];
    }

    /**
     * Process installment payment
     */
    private function processInstallmentPayment($transaction_id, $payment_method, $amount, $additional_data) {
        $installment_terms = $additional_data['installment_terms'] ?? 3;
        $installment_amount = $amount / $installment_terms;

        // Create installment records
        for ($i = 1; $i <= $installment_terms; $i++) {
            $due_date = date('Y-m-d', strtotime('+' . $i . ' months'));
            $stmt = $this->conn->prepare("
                INSERT INTO payment_installments (transaction_id, installment_number, amount, due_date)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$transaction_id, $i, $installment_amount, $due_date]);
        }

        return [
            'type' => 'installment',
            'payment_method' => $payment_method['code'],
            'amount' => $amount,
            'transaction_id' => $transaction_id,
            'installment_terms' => $installment_terms,
            'installment_amount' => $installment_amount,
            'payment_url' => 'https://app.midtrans.com/snap/v1/transactions/' . $transaction_id,
            'instructions' => 'Cicilan ' . $installment_terms . ' bulan dengan pembayaran Rp ' . number_format($installment_amount, 0, ',', '.') . ' per bulan'
        ];
    }

    /**
     * Process paylater payment
     */
    private function processPaylaterPayment($transaction_id, $payment_method, $amount, $additional_data) {
        return [
            'type' => 'paylater',
            'payment_method' => $payment_method['code'],
            'amount' => $amount,
            'transaction_id' => $transaction_id,
            'payment_url' => "https://app.midtrans.com/snap/v1/transactions/$transaction_id",
            'instructions' => "Bayar nanti dengan {$payment_method['name']}",
            'additional_info' => $additional_data
        ];
    }

    /**
     * Generate deeplink for e-wallet
     */
    private function generateDeeplink($payment_code, $transaction_id) {
        $deeplinks = [
            'gopay' => 'gojek://gopay/merchanttransfer?tref=' . $transaction_id,
            'ovo' => 'ovo://payment/' . $transaction_id,
            'dana' => 'dana://qr/' . $transaction_id,
            'shopeepay' => 'shopeepay://payment/' . $transaction_id,
            'linkaja' => 'linkaja://payment/' . $transaction_id
        ];

        return $deeplinks[$payment_code] ?? '#';
    }

    /**
     * Generate VA number
     */
    private function generateVANumber($payment_code) {
        $prefixes = [
            'bca_va' => '12345',
            'bni_va' => '98765',
            'bri_va' => '55555',
            'mandiri_va' => '77777',
            'permata_va' => '88888'
        ];

        $prefix = $prefixes[$payment_code] ?? '12345';
        return $prefix . rand(**********, **********);
    }

    /**
     * Get bank name from payment code
     */
    private function getBankName($payment_code) {
        $banks = [
            'bca_va' => 'Bank Central Asia (BCA)',
            'bni_va' => 'Bank Negara Indonesia (BNI)',
            'bri_va' => 'Bank Rakyat Indonesia (BRI)',
            'mandiri_va' => 'Bank Mandiri',
            'permata_va' => 'Bank Permata'
        ];

        return $banks[$payment_code] ?? 'Bank';
    }
}
