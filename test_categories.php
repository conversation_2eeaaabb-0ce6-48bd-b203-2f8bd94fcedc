<?php
// Test script to check categories in database
require_once 'config.php';
require_once 'includes/db_connect.php';

echo "<h2>Testing Categories Database</h2>";

try {
    // Test database connection
    if (!$conn) {
        echo "<p style='color: red;'>Database connection failed!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>Database connection successful!</p>";
    
    // Check if categories table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'categories'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>Categories table does not exist!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>Categories table exists!</p>";
    
    // Get all categories
    $stmt = $conn->query("SELECT category_id, name, slug FROM categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Categories in database:</h3>";
    if (empty($categories)) {
        echo "<p style='color: orange;'>No categories found in database!</p>";
        
        // Insert default categories
        echo "<p>Inserting default categories...</p>";
        $defaultCategories = [
            ['name' => 'Cosmetics', 'slug' => 'cosmetics'],
            ['name' => 'Medicine', 'slug' => 'medicine'],
            ['name' => 'Milk Products', 'slug' => 'milk-products'],
            ['name' => 'Sports', 'slug' => 'sports'],
            ['name' => 'Vegetables', 'slug' => 'vegetables']
        ];
        
        $insertStmt = $conn->prepare("INSERT INTO categories (name, slug) VALUES (?, ?)");
        foreach ($defaultCategories as $cat) {
            $insertStmt->execute([$cat['name'], $cat['slug']]);
            echo "<p>Inserted: " . $cat['name'] . "</p>";
        }
        
        // Get categories again
        $stmt = $conn->query("SELECT category_id, name, slug FROM categories ORDER BY name");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<ul>";
    foreach ($categories as $category) {
        echo "<li>ID: " . $category['category_id'] . " - Name: " . $category['name'] . " - Slug: " . $category['slug'] . "</li>";
    }
    echo "</ul>";
    
    // Test products count per category
    echo "<h3>Products per category:</h3>";
    $stmt = $conn->query("
        SELECT c.name, COUNT(p.product_id) as product_count 
        FROM categories c 
        LEFT JOIN products p ON c.category_id = p.category_id 
        GROUP BY c.category_id, c.name 
        ORDER BY c.name
    ");
    $productCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<ul>";
    foreach ($productCounts as $count) {
        echo "<li>" . $count['name'] . ": " . $count['product_count'] . " products</li>";
    }
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h2, h3 {
    color: #333;
}

p {
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

li {
    margin: 5px 0;
    padding: 5px;
    border-bottom: 1px solid #eee;
}
</style>
