<?php
session_start();
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';
require_once 'includes/PaymentManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Get order information from session or URL
$order_id = $_GET['order_id'] ?? $_SESSION['current_order_id'] ?? null;
$amount = $_GET['amount'] ?? $_SESSION['payment_amount'] ?? 0;
$region = $_SESSION['user_region'] ?? 'jakarta';

if (!$order_id || !$amount) {
    $_SESSION['alert_type'] = 'error';
    $_SESSION['alert_message'] = 'Invalid payment request';
    header('Location: cart.php');
    exit;
}

$paymentManager = new PaymentManager();
$payment_methods = $paymentManager->getPaymentMethodsByCategory($amount, $region);

$page = 'payment';
$page_title = 'Choose Payment Method';
require_once 'includes/header.php';
?>

<style>
.payment-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin-bottom: 40px;
}

.payment-category {
    margin-bottom: 2rem;
}

.payment-category-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-radius: 12px 12px 0 0;
    border-bottom: 2px solid #e9ecef;
}

.payment-method-card {
    border: 2px solid #e9ecef;
    border-radius: 0 0 12px 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
}

.payment-method-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.payment-method-card.selected {
    border-color: #667eea;
    background: #f8f9ff;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.payment-method-item {
    padding: 1.25rem;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.payment-method-item:last-child {
    border-bottom: none;
}

.payment-method-item:hover {
    background: #f8f9fa;
}

.payment-method-item.selected {
    background: #e3f2fd;
    border-left: 4px solid #667eea;
}

.payment-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.payment-fee {
    font-size: 0.9rem;
    color: #28a745;
    font-weight: 600;
}

.payment-fee.has-fee {
    color: #ffc107;
}

.payment-processing-time {
    font-size: 0.85rem;
    color: #6c757d;
}

.order-summary {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    position: sticky;
    top: 20px;
}

.btn-pay {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-pay:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-pay:disabled {
    background: #6c757d;
    transform: none;
    box-shadow: none;
}

@media (max-width: 768px) {
    .payment-hero {
        padding: 40px 0;
    }
    
    .payment-method-item {
        padding: 1rem;
    }
    
    .payment-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .order-summary {
        position: static;
        margin-top: 2rem;
    }
}
</style>

<!-- Hero Section -->
<div class="payment-hero">
    <div class="container text-center">
        <h1><i class="fas fa-credit-card me-3"></i>Choose Payment Method</h1>
        <p class="lead">Select your preferred payment method to complete your order</p>
        <div class="mt-3">
            <span class="badge bg-light text-dark fs-6 px-3 py-2">
                Order #<?php echo htmlspecialchars($order_id); ?>
            </span>
        </div>
    </div>
</div>

<div class="container mb-5">
    <div class="row">
        <!-- Payment Methods -->
        <div class="col-lg-8">
            <form id="paymentForm" action="process_payment.php" method="POST">
                <input type="hidden" name="order_id" value="<?php echo htmlspecialchars($order_id); ?>">
                <input type="hidden" name="amount" value="<?php echo htmlspecialchars($amount); ?>">
                <input type="hidden" name="selected_method" id="selectedMethod">
                
                <?php if (empty($payment_methods)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No payment methods available for your region and order amount.
                </div>
                <?php else: ?>
                
                <?php foreach ($payment_methods as $category_name => $category): ?>
                <div class="payment-category">
                    <!-- Category Header -->
                    <div class="payment-category-header">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="<?php echo $category['category_icon']; ?> me-2 text-primary"></i>
                            <?php echo htmlspecialchars($category['category_name']); ?>
                            <span class="badge bg-secondary ms-2"><?php echo count($category['methods']); ?></span>
                        </h5>
                    </div>
                    
                    <!-- Payment Methods in Category -->
                    <div class="payment-method-card">
                        <?php foreach ($category['methods'] as $method): ?>
                        <div class="payment-method-item" 
                             data-method="<?php echo htmlspecialchars($method['code']); ?>"
                             data-fee="<?php echo $method['calculated_fee']; ?>"
                             data-total="<?php echo $method['total_amount']; ?>">
                            
                            <div class="d-flex align-items-center">
                                <!-- Radio Button -->
                                <input type="radio" 
                                       name="payment_method" 
                                       value="<?php echo htmlspecialchars($method['code']); ?>"
                                       id="method_<?php echo htmlspecialchars($method['code']); ?>"
                                       class="form-check-input me-3">
                                
                                <!-- Payment Icon -->
                                <div class="payment-icon">
                                    <i class="<?php echo htmlspecialchars($method['icon']); ?>"></i>
                                </div>
                                
                                <!-- Payment Info -->
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($method['name']); ?></h6>
                                            <p class="text-muted mb-1 small">
                                                <?php echo htmlspecialchars($method['description']); ?>
                                            </p>
                                            <?php if (!empty($method['processing_time'])): ?>
                                            <div class="payment-processing-time">
                                                <i class="fas fa-clock me-1"></i>
                                                Processing: <?php echo htmlspecialchars($method['processing_time']); ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="text-end">
                                            <div class="payment-fee <?php echo $method['calculated_fee'] > 0 ? 'has-fee' : ''; ?>">
                                                <?php echo $method['fee_display']; ?>
                                            </div>
                                            <div class="text-muted small">
                                                Total: Rp <?php echo number_format($method['total_amount']); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <?php endif; ?>
            </form>
        </div>
        
        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="order-summary p-4">
                <h5 class="mb-3">
                    <i class="fas fa-receipt me-2"></i>Order Summary
                </h5>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>Subtotal:</span>
                    <span>Rp <?php echo number_format($amount); ?></span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>Payment Fee:</span>
                    <span id="paymentFee">Rp 0</span>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between mb-3">
                    <strong>Total:</strong>
                    <strong id="totalAmount">Rp <?php echo number_format($amount); ?></strong>
                </div>
                
                <div class="mb-3">
                    <div class="alert alert-info small">
                        <i class="fas fa-info-circle me-1"></i>
                        Your payment is secured with 256-bit SSL encryption
                    </div>
                </div>
                
                <button type="submit" 
                        form="paymentForm"
                        class="btn btn-pay btn-primary w-100"
                        id="payButton"
                        disabled>
                    <i class="fas fa-lock me-2"></i>
                    Proceed to Payment
                </button>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        100% Secure Payment
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const paymentItems = document.querySelectorAll('.payment-method-item');
    const payButton = document.getElementById('payButton');
    const selectedMethodInput = document.getElementById('selectedMethod');
    const paymentFeeElement = document.getElementById('paymentFee');
    const totalAmountElement = document.getElementById('totalAmount');
    
    // Handle payment method selection
    paymentMethods.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove selected class from all items
            paymentItems.forEach(item => {
                item.classList.remove('selected');
            });
            
            // Add selected class to current item
            const selectedItem = this.closest('.payment-method-item');
            selectedItem.classList.add('selected');
            
            // Update hidden input
            selectedMethodInput.value = this.value;
            
            // Update fee and total
            const fee = parseInt(selectedItem.dataset.fee) || 0;
            const total = parseInt(selectedItem.dataset.total) || 0;
            
            paymentFeeElement.textContent = 'Rp ' + fee.toLocaleString('id-ID');
            totalAmountElement.textContent = 'Rp ' + total.toLocaleString('id-ID');
            
            // Enable pay button
            payButton.disabled = false;
        });
    });
    
    // Handle clicking on payment method item
    paymentItems.forEach(item => {
        item.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
                radio.dispatchEvent(new Event('change'));
            }
        });
    });
    
    // Handle form submission
    document.getElementById('paymentForm').addEventListener('submit', function(e) {
        if (!selectedMethodInput.value) {
            e.preventDefault();
            alert('Please select a payment method');
            return;
        }
        
        // Show loading state
        payButton.disabled = true;
        payButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
