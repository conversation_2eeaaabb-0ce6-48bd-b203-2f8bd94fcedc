<?php
/**
 * Server-Sent Events endpoint for real-time notifications
 */

session_start();
require_once '../includes/db_connect.php';
require_once '../includes/NotificationManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    exit('Unauthorized');
}

$user_id = $_SESSION['user_id'];

// Set SSE headers
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// Disable output buffering
if (ob_get_level()) {
    ob_end_clean();
}

// Function to send SSE data
function sendSSEData($data, $event = 'message', $id = null) {
    if ($id !== null) {
        echo "id: $id\n";
    }
    echo "event: $event\n";
    echo "data: " . json_encode($data) . "\n\n";
    
    // Flush output immediately
    if (ob_get_level()) {
        ob_flush();
    }
    flush();
}

// Function to send heartbeat
function sendHeartbeat() {
    sendSSEData(['type' => 'heartbeat', 'timestamp' => time()], 'heartbeat');
}

// Initialize notification manager
$notificationManager = new NotificationManager();

// Send initial connection confirmation
sendSSEData([
    'type' => 'connected',
    'message' => 'Connected to notification stream',
    'user_id' => $user_id,
    'timestamp' => time()
], 'connected');

// Main SSE loop
$last_heartbeat = time();
$heartbeat_interval = 30; // Send heartbeat every 30 seconds
$check_interval = 2; // Check for new notifications every 2 seconds

while (true) {
    // Check if connection is still alive
    if (connection_aborted()) {
        break;
    }
    
    try {
        // Get pending notifications
        $pending_notifications = $notificationManager->getPendingNotifications($user_id);
        
        // Send each notification
        foreach ($pending_notifications as $notification) {
            sendSSEData([
                'type' => 'notification',
                'notification' => $notification
            ], 'notification', $notification['id'] ?? null);
        }
        
        // Send heartbeat if needed
        $current_time = time();
        if ($current_time - $last_heartbeat >= $heartbeat_interval) {
            sendHeartbeat();
            $last_heartbeat = $current_time;
        }
        
        // Also check for notification count updates
        $counts = $notificationManager->getNotificationCounts($user_id);
        if ($counts['unread'] > 0) {
            sendSSEData([
                'type' => 'count_update',
                'counts' => $counts
            ], 'count_update');
        }
        
    } catch (Exception $e) {
        error_log("SSE Error: " . $e->getMessage());
        sendSSEData([
            'type' => 'error',
            'message' => 'An error occurred while fetching notifications'
        ], 'error');
    }
    
    // Sleep for the check interval
    sleep($check_interval);
}

// Connection closed
error_log("SSE connection closed for user: $user_id");
?>
