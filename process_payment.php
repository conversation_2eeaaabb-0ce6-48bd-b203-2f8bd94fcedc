<?php
session_start();
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';
require_once 'includes/PaymentManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Validate POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['alert_type'] = 'error';
    $_SESSION['alert_message'] = 'Invalid request method';
    header('Location: cart.php');
    exit;
}

// Get form data
$order_id = $_POST['order_id'] ?? null;
$amount = (float)($_POST['amount'] ?? 0);
$selected_method = $_POST['selected_method'] ?? null;
$user_id = $_SESSION['user_id'];

// Validate required fields
if (!$order_id || !$amount || !$selected_method) {
    $_SESSION['alert_type'] = 'error';
    $_SESSION['alert_message'] = 'Missing required payment information';
    header('Location: payment_methods.php?order_id=' . urlencode($order_id) . '&amount=' . urlencode($amount));
    exit;
}

// Verify order exists and belongs to user
try {
    $stmt = $conn->prepare("
        SELECT order_id, user_id, total_amount, status 
        FROM orders 
        WHERE order_id = ? AND user_id = ?
    ");
    $stmt->execute([$order_id, $user_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        throw new Exception('Order not found or access denied');
    }
    
    if ($order['status'] !== 'pending') {
        throw new Exception('Order is not in pending status');
    }
    
    // Verify amount matches
    if (abs($order['total_amount'] - $amount) > 0.01) {
        throw new Exception('Amount mismatch');
    }
    
} catch (Exception $e) {
    error_log("Payment validation error: " . $e->getMessage());
    $_SESSION['alert_type'] = 'error';
    $_SESSION['alert_message'] = 'Order validation failed: ' . $e->getMessage();
    header('Location: cart.php');
    exit;
}

// Initialize payment manager
$paymentManager = new PaymentManager();

try {
    // Create payment transaction
    $payment_result = $paymentManager->createPaymentTransaction(
        $order_id, 
        $user_id, 
        $selected_method, 
        $amount,
        [
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'timestamp' => date('Y-m-d H:i:s')
        ]
    );
    
    if (!$payment_result['success']) {
        throw new Exception($payment_result['message']);
    }
    
    $transaction_id = $payment_result['transaction_id'];
    $payment_response = $payment_result['payment_response'];
    
    // Update order status to processing
    $stmt = $conn->prepare("
        UPDATE orders 
        SET status = 'processing', 
            payment_method = ?,
            updated_at = NOW()
        WHERE order_id = ?
    ");
    $stmt->execute([$selected_method, $order_id]);
    
    // Store payment info in session for the payment instruction page
    $_SESSION['payment_info'] = [
        'transaction_id' => $transaction_id,
        'order_id' => $order_id,
        'payment_method' => $selected_method,
        'amount' => $amount,
        'payment_response' => $payment_response
    ];
    
    // Redirect based on payment method type
    $payment_method_info = $paymentManager->getPaymentMethodByCode($selected_method);
    
    switch ($payment_method_info['type']) {
        case 'ewallet':
            header('Location: payment-ewallet.php');
            break;
            
        case 'va':
            header('Location: payment-va.php');
            break;
            
        case 'card':
            header('Location: payment-card.php');
            break;
            
        case 'qris':
            header('Location: payment-qris.php');
            break;
            
        case 'bank_transfer':
            header('Location: payment-transfer.php');
            break;
            
        case 'cod':
            header('Location: payment-cod.php');
            break;
            
        case 'installment':
        case 'paylater':
            header('Location: payment-installment.php');
            break;
            
        case 'crypto':
            header('Location: payment-crypto.php');
            break;
            
        default:
            header('Location: payment_process.php');
            break;
    }
    
} catch (Exception $e) {
    error_log("Payment processing error: " . $e->getMessage());
    $_SESSION['alert_type'] = 'error';
    $_SESSION['alert_message'] = 'Payment processing failed: ' . $e->getMessage();
    header('Location: payment_methods.php?order_id=' . urlencode($order_id) . '&amount=' . urlencode($amount));
    exit;
}
?>
