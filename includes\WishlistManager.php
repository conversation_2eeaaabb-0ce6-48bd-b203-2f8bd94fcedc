<?php
/**
 * Wishlist Manager Class
 * Handles all wishlist-related operations
 */

require_once __DIR__ . '/db_connect.php';

class WishlistManager {
    private $conn;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * Add product to wishlist
     */
    public function addToWishlist($user_id, $product_id, $notes = '', $priority = 'medium') {
        try {
            // Check if product exists and is active
            $stmt = $this->conn->prepare("SELECT product_id FROM products WHERE product_id = ? AND is_active = 1");
            $stmt->execute([$product_id]);
            if (!$stmt->fetch()) {
                throw new Exception('Product not found or inactive');
            }
            
            // Check if already in wishlist
            $stmt = $this->conn->prepare("SELECT wishlist_id FROM wishlist WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$user_id, $product_id]);
            if ($stmt->fetch()) {
                throw new Exception('Product already in wishlist');
            }
            
            // Add to wishlist
            $stmt = $this->conn->prepare("
                INSERT INTO wishlist (user_id, product_id, notes, priority) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$user_id, $product_id, $notes, $priority]);
            
            $wishlist_id = $this->conn->lastInsertId();
            
            // Add to default collection
            $this->addToDefaultCollection($user_id, $wishlist_id);
            
            return [
                'success' => true,
                'message' => 'Product added to wishlist',
                'wishlist_id' => $wishlist_id
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Remove product from wishlist
     */
    public function removeFromWishlist($user_id, $product_id) {
        try {
            $stmt = $this->conn->prepare("DELETE FROM wishlist WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$user_id, $product_id]);
            
            if ($stmt->rowCount() > 0) {
                return [
                    'success' => true,
                    'message' => 'Product removed from wishlist'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Product not found in wishlist'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if product is in user's wishlist
     */
    public function isInWishlist($user_id, $product_id) {
        try {
            $stmt = $this->conn->prepare("SELECT wishlist_id FROM wishlist WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$user_id, $product_id]);
            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get user's wishlist items
     */
    public function getUserWishlist($user_id, $collection_id = null, $limit = null, $offset = 0) {
        try {
            $sql = "
                SELECT w.*, p.NAME as product_name, p.price, p.image, p.stock,
                       c.NAME as category_name, c.slug as category_slug,
                       COALESCE(prs.average_rating, 0) as average_rating,
                       COALESCE(prs.total_reviews, 0) as total_reviews
                FROM wishlist w
                JOIN products p ON w.product_id = p.product_id
                LEFT JOIN categories c ON p.category_id = c.category_id
                LEFT JOIN product_rating_summary prs ON p.product_id = prs.product_id
                WHERE w.user_id = ? AND p.is_active = 1
            ";
            
            $params = [$user_id];
            
            if ($collection_id) {
                $sql .= " AND w.wishlist_id IN (
                    SELECT wci.wishlist_id FROM wishlist_collection_items wci 
                    WHERE wci.collection_id = ?
                )";
                $params[] = $collection_id;
            }
            
            $sql .= " ORDER BY w.added_at DESC";
            
            if ($limit) {
                $sql .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting wishlist: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get wishlist count for user
     */
    public function getWishlistCount($user_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) as count 
                FROM wishlist w 
                JOIN products p ON w.product_id = p.product_id 
                WHERE w.user_id = ? AND p.is_active = 1
            ");
            $stmt->execute([$user_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * Create new collection
     */
    public function createCollection($user_id, $name, $description = '', $is_public = false) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO wishlist_collections (user_id, collection_name, description, is_public) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$user_id, $name, $description, $is_public]);
            
            return [
                'success' => true,
                'collection_id' => $this->conn->lastInsertId()
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get user's collections
     */
    public function getUserCollections($user_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT wc.*, COUNT(wci.wishlist_id) as item_count
                FROM wishlist_collections wc
                LEFT JOIN wishlist_collection_items wci ON wc.collection_id = wci.collection_id
                WHERE wc.user_id = ?
                GROUP BY wc.collection_id
                ORDER BY wc.created_at ASC
            ");
            $stmt->execute([$user_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Add wishlist item to default collection
     */
    private function addToDefaultCollection($user_id, $wishlist_id) {
        try {
            // Get default collection
            $stmt = $this->conn->prepare("
                SELECT collection_id FROM wishlist_collections 
                WHERE user_id = ? AND collection_name = 'My Favorites' 
                LIMIT 1
            ");
            $stmt->execute([$user_id]);
            $collection = $stmt->fetch();
            
            if ($collection) {
                $stmt = $this->conn->prepare("
                    INSERT IGNORE INTO wishlist_collection_items (collection_id, wishlist_id) 
                    VALUES (?, ?)
                ");
                $stmt->execute([$collection['collection_id'], $wishlist_id]);
            }
        } catch (Exception $e) {
            // Ignore errors for default collection
        }
    }
    
    /**
     * Move all wishlist items to cart
     */
    public function moveAllToCart($user_id) {
        try {
            $wishlist_items = $this->getUserWishlist($user_id);
            $moved_count = 0;
            
            foreach ($wishlist_items as $item) {
                if ($item['stock'] > 0) {
                    // Add to cart (you'll need to implement this based on your cart system)
                    // For now, just remove from wishlist
                    $this->removeFromWishlist($user_id, $item['product_id']);
                    $moved_count++;
                }
            }
            
            return [
                'success' => true,
                'moved_count' => $moved_count
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
?>
