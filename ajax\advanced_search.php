<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/SearchManager.php';

header('Content-Type: application/json');

try {
    $searchManager = new SearchManager();
    
    // Get search parameters
    $params = [
        'search' => $_GET['search'] ?? '',
        'category' => $_GET['category'] ?? null,
        'min_price' => !empty($_GET['min_price']) ? (float)$_GET['min_price'] : null,
        'max_price' => !empty($_GET['max_price']) ? (float)$_GET['max_price'] : null,
        'min_rating' => !empty($_GET['min_rating']) ? (float)$_GET['min_rating'] : null,
        'in_stock' => !empty($_GET['in_stock']) ? 1 : null,
        'sort' => $_GET['sort'] ?? 'relevance',
        'page' => (int)($_GET['page'] ?? 1),
        'limit' => (int)($_GET['limit'] ?? 12)
    ];
    
    // Perform search
    $result = $searchManager->searchProducts($params);
    
    // Format response
    $response = [
        'success' => true,
        'data' => $result,
        'message' => $result['total_count'] > 0 ? 
            "Found {$result['total_count']} products" : 
            "No products found matching your criteria"
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Advanced search error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Search failed. Please try again.',
        'error' => $e->getMessage()
    ]);
}
?>
