<?php
/**
 * Advanced Filters Component
 * Reusable filter component for product search
 */

// Get current filter values
$current_search = $_GET['search'] ?? '';
$current_category = $_GET['category'] ?? '';
$current_min_price = $_GET['min_price'] ?? '';
$current_max_price = $_GET['max_price'] ?? '';
$current_min_rating = $_GET['min_rating'] ?? '';
$current_in_stock = isset($_GET['in_stock']) ? 1 : 0;
$current_sort = $_GET['sort'] ?? 'relevance';

// Get categories for filter
try {
    $stmt = $conn->prepare("SELECT category_id, name FROM categories WHERE is_active = 1 ORDER BY name ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $categories = [];
}

// Get price range
try {
    $stmt = $conn->prepare("SELECT MIN(price) as min_price, MAX(price) as max_price FROM products WHERE is_active = 1");
    $stmt->execute();
    $price_range = $stmt->fetch(PDO::FETCH_ASSOC);
    $min_price_range = floor($price_range['min_price'] ?? 0);
    $max_price_range = ceil($price_range['max_price'] ?? 1000000);
} catch (PDOException $e) {
    $min_price_range = 0;
    $max_price_range = 1000000;
}
?>

<style>
.advanced-filters {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.filter-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.filter-body {
    padding: 1.5rem;
}

.filter-section {
    margin-bottom: 1.5rem;
}

.filter-section:last-child {
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;
}

.price-range-slider {
    margin: 1rem 0;
}

.price-range-inputs {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.price-range-inputs input {
    flex: 1;
}

.rating-filter {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.rating-option {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.rating-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.rating-option.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.rating-option input {
    display: none;
}

.sort-options {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.sort-option {
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    background: white;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.sort-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
    color: #667eea;
    text-decoration: none;
}

.sort-option.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.quick-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.quick-filter {
    padding: 0.375rem 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    background: white;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.quick-filter:hover {
    border-color: #667eea;
    background: #f8f9ff;
    color: #667eea;
    text-decoration: none;
}

.quick-filter.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

@media (max-width: 768px) {
    .filter-body {
        padding: 1rem;
    }
    
    .price-range-inputs {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .filter-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .rating-filter,
    .sort-options,
    .quick-filters {
        justify-content: center;
    }
}
</style>

<!-- Advanced Filters -->
<div class="advanced-filters">
    <!-- Filter Header -->
    <div class="filter-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>Advanced Filters
        </h5>
        <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
            <i class="fas fa-chevron-down"></i>
        </button>
    </div>
    
    <!-- Filter Body -->
    <div class="collapse show" id="filterCollapse">
        <div class="filter-body">
            <form id="advancedFilterForm" method="GET" action="">
                <!-- Quick Filters -->
                <div class="filter-section">
                    <label class="filter-label">Quick Filters</label>
                    <div class="quick-filters">
                        <a href="?sort=popularity" class="quick-filter <?php echo $current_sort === 'popularity' ? 'active' : ''; ?>">
                            <i class="fas fa-fire me-1"></i>Terlaris
                        </a>
                        <a href="?sort=price_low" class="quick-filter <?php echo $current_sort === 'price_low' ? 'active' : ''; ?>">
                            <i class="fas fa-arrow-down me-1"></i>Termurah
                        </a>
                        <a href="?min_rating=4&sort=rating" class="quick-filter <?php echo $current_min_rating == '4' ? 'active' : ''; ?>">
                            <i class="fas fa-star me-1"></i>Rating Tinggi
                        </a>
                        <a href="?sort=newest" class="quick-filter <?php echo $current_sort === 'newest' ? 'active' : ''; ?>">
                            <i class="fas fa-clock me-1"></i>Terbaru
                        </a>
                        <a href="?in_stock=1" class="quick-filter <?php echo $current_in_stock ? 'active' : ''; ?>">
                            <i class="fas fa-check me-1"></i>Tersedia
                        </a>
                    </div>
                </div>
                
                <!-- Search -->
                <div class="filter-section">
                    <label class="filter-label" for="searchInput">Search Products</label>
                    <div class="input-group">
                        <input type="text" 
                               class="form-control" 
                               id="searchInput"
                               name="search" 
                               placeholder="Search products, categories..." 
                               value="<?php echo htmlspecialchars($current_search); ?>">
                        <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Category Filter -->
                <div class="filter-section">
                    <label class="filter-label" for="categorySelect">Category</label>
                    <select class="form-select" id="categorySelect" name="category">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['category_id']; ?>" 
                                <?php echo $current_category == $category['category_id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Price Range -->
                <div class="filter-section">
                    <label class="filter-label">Price Range</label>
                    <div class="price-range-inputs">
                        <div>
                            <label class="form-label small">Min Price</label>
                            <input type="number" 
                                   class="form-control" 
                                   name="min_price" 
                                   placeholder="0"
                                   min="<?php echo $min_price_range; ?>"
                                   max="<?php echo $max_price_range; ?>"
                                   value="<?php echo htmlspecialchars($current_min_price); ?>">
                        </div>
                        <div>
                            <label class="form-label small">Max Price</label>
                            <input type="number" 
                                   class="form-control" 
                                   name="max_price" 
                                   placeholder="<?php echo number_format($max_price_range); ?>"
                                   min="<?php echo $min_price_range; ?>"
                                   max="<?php echo $max_price_range; ?>"
                                   value="<?php echo htmlspecialchars($current_max_price); ?>">
                        </div>
                    </div>
                    <small class="text-muted">
                        Range: Rp <?php echo number_format($min_price_range); ?> - Rp <?php echo number_format($max_price_range); ?>
                    </small>
                </div>
                
                <!-- Rating Filter -->
                <div class="filter-section">
                    <label class="filter-label">Minimum Rating</label>
                    <div class="rating-filter">
                        <label class="rating-option <?php echo $current_min_rating == '1' ? 'active' : ''; ?>">
                            <input type="radio" name="min_rating" value="1" <?php echo $current_min_rating == '1' ? 'checked' : ''; ?>>
                            <span>1+ <i class="fas fa-star text-warning"></i></span>
                        </label>
                        <label class="rating-option <?php echo $current_min_rating == '2' ? 'active' : ''; ?>">
                            <input type="radio" name="min_rating" value="2" <?php echo $current_min_rating == '2' ? 'checked' : ''; ?>>
                            <span>2+ <i class="fas fa-star text-warning"></i></span>
                        </label>
                        <label class="rating-option <?php echo $current_min_rating == '3' ? 'active' : ''; ?>">
                            <input type="radio" name="min_rating" value="3" <?php echo $current_min_rating == '3' ? 'checked' : ''; ?>>
                            <span>3+ <i class="fas fa-star text-warning"></i></span>
                        </label>
                        <label class="rating-option <?php echo $current_min_rating == '4' ? 'active' : ''; ?>">
                            <input type="radio" name="min_rating" value="4" <?php echo $current_min_rating == '4' ? 'checked' : ''; ?>>
                            <span>4+ <i class="fas fa-star text-warning"></i></span>
                        </label>
                        <label class="rating-option <?php echo $current_min_rating == '5' ? 'active' : ''; ?>">
                            <input type="radio" name="min_rating" value="5" <?php echo $current_min_rating == '5' ? 'checked' : ''; ?>>
                            <span>5 <i class="fas fa-star text-warning"></i></span>
                        </label>
                    </div>
                </div>
                
                <!-- Stock Filter -->
                <div class="filter-section">
                    <div class="form-check">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="inStockCheck" 
                               name="in_stock" 
                               value="1"
                               <?php echo $current_in_stock ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="inStockCheck">
                            <i class="fas fa-check-circle text-success me-1"></i>
                            Show only products in stock
                        </label>
                    </div>
                </div>
                
                <!-- Sort Options -->
                <div class="filter-section">
                    <label class="filter-label">Sort By</label>
                    <div class="sort-options">
                        <input type="radio" class="btn-check" name="sort" value="relevance" id="sort_relevance" <?php echo $current_sort === 'relevance' ? 'checked' : ''; ?>>
                        <label class="sort-option" for="sort_relevance">Relevance</label>
                        
                        <input type="radio" class="btn-check" name="sort" value="popularity" id="sort_popularity" <?php echo $current_sort === 'popularity' ? 'checked' : ''; ?>>
                        <label class="sort-option" for="sort_popularity">Popularity</label>
                        
                        <input type="radio" class="btn-check" name="sort" value="price_low" id="sort_price_low" <?php echo $current_sort === 'price_low' ? 'checked' : ''; ?>>
                        <label class="sort-option" for="sort_price_low">Price: Low to High</label>
                        
                        <input type="radio" class="btn-check" name="sort" value="price_high" id="sort_price_high" <?php echo $current_sort === 'price_high' ? 'checked' : ''; ?>>
                        <label class="sort-option" for="sort_price_high">Price: High to Low</label>
                        
                        <input type="radio" class="btn-check" name="sort" value="rating" id="sort_rating" <?php echo $current_sort === 'rating' ? 'checked' : ''; ?>>
                        <label class="sort-option" for="sort_rating">Rating</label>
                        
                        <input type="radio" class="btn-check" name="sort" value="newest" id="sort_newest" <?php echo $current_sort === 'newest' ? 'checked' : ''; ?>>
                        <label class="sort-option" for="sort_newest">Newest</label>
                        
                        <input type="radio" class="btn-check" name="sort" value="name_asc" id="sort_name_asc" <?php echo $current_sort === 'name_asc' ? 'checked' : ''; ?>>
                        <label class="sort-option" for="sort_name_asc">Name A-Z</label>
                    </div>
                </div>
                
                <!-- Filter Actions -->
                <div class="filter-actions">
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Apply Filters
                        </button>
                        <button type="button" class="btn btn-outline-secondary ms-2" id="resetFilters">
                            <i class="fas fa-undo me-1"></i>Reset
                        </button>
                    </div>
                    <div>
                        <a href="Products.php" class="btn btn-outline-danger">
                            <i class="fas fa-times me-1"></i>Clear All
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle rating filter clicks
    const ratingOptions = document.querySelectorAll('.rating-option');
    ratingOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            ratingOptions.forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Check the radio button
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
            }
        });
    });
    
    // Handle sort option clicks
    const sortOptions = document.querySelectorAll('.sort-option');
    sortOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            sortOptions.forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked option
            this.classList.add('active');
        });
    });
    
    // Clear search button
    document.getElementById('clearSearch').addEventListener('click', function() {
        document.getElementById('searchInput').value = '';
    });
    
    // Reset filters button
    document.getElementById('resetFilters').addEventListener('click', function() {
        const form = document.getElementById('advancedFilterForm');
        form.reset();
        
        // Remove active classes
        document.querySelectorAll('.rating-option, .sort-option').forEach(opt => {
            opt.classList.remove('active');
        });
        
        // Set default sort to relevance
        document.getElementById('sort_relevance').checked = true;
        document.querySelector('label[for="sort_relevance"]').classList.add('active');
    });
    
    // Auto-submit on quick filter clicks (handled by links)
    // Auto-submit on select changes
    document.getElementById('categorySelect').addEventListener('change', function() {
        document.getElementById('advancedFilterForm').submit();
    });
});
</script>
