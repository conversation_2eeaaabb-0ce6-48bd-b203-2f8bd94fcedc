<?php
require_once 'includes/db_connect.php';

echo "<h2>Cart Table Structure Check</h2>";

try {
    // Check if cart table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'cart'");
    $stmt->execute();
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<p style='color: green;'>✅ Cart table exists</p>";
        
        // Show table structure
        $stmt = $conn->prepare("DESCRIBE cart");
        $stmt->execute();
        $columns = $stmt->fetchAll();
        
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check cart contents
        $stmt = $conn->prepare("SELECT COUNT(*) as total_items FROM cart");
        $stmt->execute();
        $total = $stmt->fetch();
        echo "<p>Total items in all carts: " . $total['total_items'] . "</p>";
        
        // Show recent cart items
        $stmt = $conn->prepare("SELECT c.*, p.name as product_name FROM cart c LEFT JOIN products p ON c.product_id = p.product_id ORDER BY c.created_at DESC LIMIT 10");
        $stmt->execute();
        $recent_items = $stmt->fetchAll();
        
        if ($recent_items) {
            echo "<h3>Recent Cart Items:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Cart ID</th><th>User ID</th><th>Product ID</th><th>Product Name</th><th>Quantity</th><th>Created</th></tr>";
            foreach ($recent_items as $item) {
                echo "<tr>";
                echo "<td>" . $item['cart_id'] . "</td>";
                echo "<td>" . $item['user_id'] . "</td>";
                echo "<td>" . $item['product_id'] . "</td>";
                echo "<td>" . ($item['product_name'] ?? 'Unknown') . "</td>";
                echo "<td>" . $item['quantity'] . "</td>";
                echo "<td>" . $item['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No cart items found</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Cart table does not exist!</p>";
        
        // Create cart table
        echo "<h3>Creating cart table...</h3>";
        $create_sql = "
        CREATE TABLE cart (
            cart_id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_product (user_id, product_id),
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE
        )";
        
        $conn->exec($create_sql);
        echo "<p style='color: green;'>✅ Cart table created successfully!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<p><a href="test_cart_debug.php">Go to Cart Debug</a></p>
<p><a href="product-detail.php?id=29">Go to Product Detail</a></p>
