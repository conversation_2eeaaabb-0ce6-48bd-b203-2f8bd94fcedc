<?php
/**
 * Simple Database Connection Test
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

try {
    // Include database connection
    require_once 'includes/db_connect.php';
    
    $tests = [];
    
    // Test 1: Basic connection
    $tests['connection'] = [
        'name' => 'Database Connection',
        'status' => 'PASS',
        'message' => 'Connected successfully'
    ];
    
    // Test 2: Check tables exist
    $tables = ['carts', 'cart_items', 'products'];
    foreach ($tables as $table) {
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM {$table}");
            $stmt->execute();
            $count = $stmt->fetchColumn();
            $stmt = null; // Close statement
            
            $tests["table_{$table}"] = [
                'name' => "Table {$table}",
                'status' => 'PASS',
                'message' => "Table exists with {$count} records"
            ];
        } catch (PDOException $e) {
            $tests["table_{$table}"] = [
                'name' => "Table {$table}",
                'status' => 'FAIL',
                'message' => "Error: " . $e->getMessage()
            ];
        }
    }
    
    // Test 3: Check table structures
    try {
        $stmt = $conn->prepare("DESCRIBE cart_items");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $stmt = null; // Close statement
        
        $required_columns = ['cart_id', 'product_id', 'quantity'];
        $missing_columns = array_diff($required_columns, $columns);
        
        if (empty($missing_columns)) {
            $tests['cart_items_structure'] = [
                'name' => 'Cart Items Structure',
                'status' => 'PASS',
                'message' => 'All required columns present: ' . implode(', ', $columns)
            ];
        } else {
            $tests['cart_items_structure'] = [
                'name' => 'Cart Items Structure',
                'status' => 'FAIL',
                'message' => 'Missing columns: ' . implode(', ', $missing_columns)
            ];
        }
    } catch (PDOException $e) {
        $tests['cart_items_structure'] = [
            'name' => 'Cart Items Structure',
            'status' => 'FAIL',
            'message' => 'Error checking structure: ' . $e->getMessage()
        ];
    }
    
    // Test 4: Test basic operations
    try {
        // Test insert
        $stmt = $conn->prepare("INSERT INTO carts (user_id) VALUES (999)");
        $stmt->execute();
        $test_cart_id = $conn->lastInsertId();
        $stmt = null; // Close statement
        
        // Test cart item insert
        $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity) VALUES (?, 1, 1)");
        $stmt->execute([$test_cart_id]);
        $stmt = null; // Close statement
        
        // Test select
        $stmt = $conn->prepare("SELECT quantity FROM cart_items WHERE cart_id = ?");
        $stmt->execute([$test_cart_id]);
        $quantity = $stmt->fetchColumn();
        $stmt = null; // Close statement
        
        // Cleanup
        $stmt = $conn->prepare("DELETE FROM cart_items WHERE cart_id = ?");
        $stmt->execute([$test_cart_id]);
        $stmt = null; // Close statement
        
        $stmt = $conn->prepare("DELETE FROM carts WHERE cart_id = ?");
        $stmt->execute([$test_cart_id]);
        $stmt = null; // Close statement
        
        $tests['basic_operations'] = [
            'name' => 'Basic Operations',
            'status' => 'PASS',
            'message' => "Insert, select, and delete operations successful. Retrieved quantity: {$quantity}"
        ];
        
    } catch (PDOException $e) {
        $tests['basic_operations'] = [
            'name' => 'Basic Operations',
            'status' => 'FAIL',
            'message' => 'Error in basic operations: ' . $e->getMessage()
        ];
    }
    
    // Count passed/failed tests
    $passed = 0;
    $failed = 0;
    foreach ($tests as $test) {
        if ($test['status'] === 'PASS') {
            $passed++;
        } else {
            $failed++;
        }
    }
    
    $overall_status = $failed === 0 ? 'PASS' : 'PARTIAL';
    
    echo json_encode([
        'success' => true,
        'overall_status' => $overall_status,
        'message' => "Database tests completed. {$passed} passed, {$failed} failed.",
        'tests' => $tests,
        'summary' => [
            'passed' => $passed,
            'failed' => $failed,
            'total' => count($tests)
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage(),
        'error' => $e->getMessage()
    ]);
}
?>
