-- Website Performance Optimization
-- Comprehensive database optimization for TeWuNeed

USE db_tewuneed;

-- 1. Add critical indexes for products page
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_products_category_active ON products(category_id, is_active);
CREATE INDEX IF NOT EXISTS idx_products_name_search ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_products_created ON products(created_at);
CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);

-- 2. Add indexes for reviews
CREATE INDEX IF NOT EXISTS idx_reviews_product ON product_reviews(product_id);
CREATE INDEX IF NOT EXISTS idx_reviews_rating ON product_reviews(rating);

-- 3. Add indexes for categories
CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- 4. Add indexes for cart operations
CREATE INDEX IF NOT EXISTS idx_carts_user ON carts(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_cart ON cart_items(cart_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_product ON cart_items(product_id);

-- 5. Create optimized view for products with ratings
CREATE OR REPLACE VIEW products_with_stats AS
SELECT 
    p.product_id,
    p.name,
    p.description,
    p.price,
    p.stock,
    p.image,
    p.category_id,
    p.is_active,
    p.created_at,
    c.name as category_name,
    COALESCE(r.avg_rating, 0) as rating,
    COALESCE(r.review_count, 0) as review_count
FROM products p
LEFT JOIN categories c ON p.category_id = c.category_id
LEFT JOIN (
    SELECT 
        product_id,
        AVG(rating) as avg_rating,
        COUNT(*) as review_count
    FROM product_reviews
    GROUP BY product_id
) r ON p.product_id = r.product_id
WHERE p.is_active = 1;

-- 6. Create stored procedure for fast product listing
DELIMITER //

CREATE OR REPLACE PROCEDURE GetProductsOptimized(
    IN p_category_id INT,
    IN p_search_term VARCHAR(255),
    IN p_sort_by VARCHAR(50),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    DECLARE v_sql TEXT;
    DECLARE v_where_clause TEXT DEFAULT 'WHERE p.is_active = 1';
    DECLARE v_order_clause TEXT DEFAULT 'ORDER BY p.name ASC';
    
    -- Build WHERE clause
    IF p_category_id IS NOT NULL AND p_category_id > 0 THEN
        SET v_where_clause = CONCAT(v_where_clause, ' AND p.category_id = ', p_category_id);
    END IF;
    
    IF p_search_term IS NOT NULL AND LENGTH(p_search_term) > 0 THEN
        SET v_where_clause = CONCAT(v_where_clause, ' AND (p.name LIKE ''%', p_search_term, '%'' OR p.description LIKE ''%', p_search_term, '%'')');
    END IF;
    
    -- Build ORDER clause
    CASE p_sort_by
        WHEN 'name_desc' THEN SET v_order_clause = 'ORDER BY p.name DESC';
        WHEN 'price_asc' THEN SET v_order_clause = 'ORDER BY p.price ASC';
        WHEN 'price_desc' THEN SET v_order_clause = 'ORDER BY p.price DESC';
        WHEN 'newest' THEN SET v_order_clause = 'ORDER BY p.created_at DESC';
        ELSE SET v_order_clause = 'ORDER BY p.name ASC';
    END CASE;
    
    -- Build final query using the optimized view
    SET v_sql = CONCAT(
        'SELECT * FROM products_with_stats p ',
        v_where_clause, ' ',
        v_order_clause
    );
    
    IF p_limit IS NOT NULL AND p_limit > 0 THEN
        SET v_sql = CONCAT(v_sql, ' LIMIT ', p_limit);
        IF p_offset IS NOT NULL AND p_offset > 0 THEN
            SET v_sql = CONCAT(v_sql, ' OFFSET ', p_offset);
        END IF;
    END IF;
    
    SET @sql = v_sql;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END//

DELIMITER ;

-- 7. Create function for fast cart count
DELIMITER //

CREATE OR REPLACE FUNCTION GetCartCountFast(p_user_id INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    SELECT COALESCE(SUM(ci.quantity), 0) INTO v_count
    FROM cart_items ci
    JOIN carts c ON ci.cart_id = c.cart_id
    WHERE c.user_id = p_user_id;
    
    RETURN v_count;
END//

DELIMITER ;

-- 8. Create materialized table for product search
CREATE TABLE IF NOT EXISTS product_search_cache (
    product_id INT PRIMARY KEY,
    search_text TEXT,
    category_id INT,
    price DECIMAL(15,2),
    stock INT,
    rating DECIMAL(3,2),
    review_count INT,
    is_active TINYINT(1),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FULLTEXT(search_text),
    INDEX idx_category (category_id),
    INDEX idx_price (price),
    INDEX idx_rating (rating),
    INDEX idx_active (is_active)
);

-- 9. Procedure to refresh search cache
DELIMITER //

CREATE OR REPLACE PROCEDURE RefreshProductSearchCache()
BEGIN
    TRUNCATE TABLE product_search_cache;
    
    INSERT INTO product_search_cache (
        product_id, search_text, category_id, price, stock, rating, review_count, is_active
    )
    SELECT 
        p.product_id,
        CONCAT(p.name, ' ', COALESCE(p.description, ''), ' ', COALESCE(c.name, '')) as search_text,
        p.category_id,
        p.price,
        p.stock,
        COALESCE(AVG(r.rating), 0) as rating,
        COUNT(r.review_id) as review_count,
        p.is_active
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.category_id
    LEFT JOIN product_reviews r ON p.product_id = r.product_id
    GROUP BY p.product_id, p.name, p.description, p.category_id, p.price, p.stock, p.is_active, c.name;
END//

DELIMITER ;

-- 10. Create event to auto-refresh cache
CREATE EVENT IF NOT EXISTS refresh_product_cache
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO
  CALL RefreshProductSearchCache();

-- 11. Optimize table storage
ALTER TABLE products ENGINE=InnoDB ROW_FORMAT=COMPRESSED;
ALTER TABLE product_reviews ENGINE=InnoDB ROW_FORMAT=COMPRESSED;
ALTER TABLE categories ENGINE=InnoDB;
ALTER TABLE carts ENGINE=InnoDB;
ALTER TABLE cart_items ENGINE=InnoDB;

-- 12. Create performance monitoring view
CREATE OR REPLACE VIEW performance_stats AS
SELECT 
    'total_products' as metric,
    COUNT(*) as value,
    'Active products in database' as description
FROM products WHERE is_active = 1
UNION ALL
SELECT 
    'total_categories' as metric,
    COUNT(*) as value,
    'Total categories' as description
FROM categories
UNION ALL
SELECT 
    'total_reviews' as metric,
    COUNT(*) as value,
    'Total product reviews' as description
FROM product_reviews
UNION ALL
SELECT 
    'avg_products_per_category' as metric,
    ROUND(AVG(product_count), 2) as value,
    'Average products per category' as description
FROM (
    SELECT category_id, COUNT(*) as product_count
    FROM products 
    WHERE is_active = 1
    GROUP BY category_id
) as cat_stats;

-- 13. Initial cache refresh
CALL RefreshProductSearchCache();

-- 14. Analyze tables for optimization
ANALYZE TABLE products;
ANALYZE TABLE categories;
ANALYZE TABLE product_reviews;
ANALYZE TABLE carts;
ANALYZE TABLE cart_items;

SELECT 'Database optimization completed successfully!' as status;
SELECT * FROM performance_stats;
