<?php
/**
 * TEWUNEED - Enhanced User Notifications Page
 * Display user notifications and order updates with advanced features
 */

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/auth_check.php';
require_once 'includes/enhanced_notification_functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$page_title = 'Notifications';

// Create welcome notification for first-time users
createWelcomeNotification($conn, $user_id);

// Handle AJAX requests
if (isset($_POST['ajax_action'])) {
    header('Content-Type: application/json');

    switch ($_POST['ajax_action']) {
        case 'mark_read':
            $notification_id = $_POST['notification_id'] ?? null;
            $table = $_POST['table'] ?? 'order_notifications';

            if ($notification_id) {
                $success = markSingleNotificationRead($conn, $notification_id, $user_id, $table);
                $unread_count = getTotalUnreadCount($conn, $user_id);

                echo json_encode([
                    'success' => $success,
                    'unread_count' => $unread_count,
                    'message' => $success ? 'Notification marked as read' : 'Failed to mark notification as read'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid notification ID']);
            }
            exit;

        case 'mark_all_read':
            $success = markAllNotificationsRead($conn, $user_id);
            $unread_count = getTotalUnreadCount($conn, $user_id);

            echo json_encode([
                'success' => $success,
                'unread_count' => $unread_count,
                'message' => $success ? 'All notifications marked as read' : 'Failed to mark notifications as read'
            ]);
            exit;

        case 'delete_notification':
            $notification_id = $_POST['notification_id'] ?? null;
            $table = $_POST['table'] ?? 'order_notifications';

            if ($notification_id) {
                $success = deleteNotification($conn, $notification_id, $user_id, $table);
                $unread_count = getTotalUnreadCount($conn, $user_id);

                echo json_encode([
                    'success' => $success,
                    'unread_count' => $unread_count,
                    'message' => $success ? 'Notification deleted' : 'Failed to delete notification'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid notification ID']);
            }
            exit;

        case 'clear_all':
            $success = clearAllNotifications($conn, $user_id);

            echo json_encode([
                'success' => $success,
                'unread_count' => 0,
                'message' => $success ? 'All notifications cleared' : 'Failed to clear notifications'
            ]);
            exit;

        case 'cleanup_old':
            $cleaned = cleanupOldNotifications($conn);
            $unread_count = getTotalUnreadCount($conn, $user_id);

            echo json_encode([
                'success' => true,
                'unread_count' => $unread_count,
                'cleaned' => $cleaned,
                'message' => "Cleaned up {$cleaned} old notifications"
            ]);
            exit;
    }
}

// Legacy POST handling for non-AJAX requests
if (isset($_POST['mark_read']) && isset($_POST['notification_id'])) {
    markSingleNotificationRead($conn, $_POST['notification_id'], $user_id);
    header('Location: user_notifications.php');
    exit;
}

if (isset($_POST['mark_all_read'])) {
    markAllNotificationsRead($conn, $user_id);
    header('Location: user_notifications.php');
    exit;
}

// Get all user notifications from both tables
$notifications = getAllUserNotifications($conn, $user_id, 50);

// Get notification statistics
$stats = getNotificationStats($conn, $user_id);
$unread_count = $stats['unread'];

include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-bell me-2"></i>Notifications
                        <span class="badge bg-primary ms-2" id="unread-count"><?php echo $unread_count; ?></span>
                    </h2>
                    <p class="text-muted mb-0">
                        <?php if ($unread_count > 0): ?>
                            You have <span id="unread-text"><?php echo $unread_count; ?></span> unread notification<?php echo $unread_count > 1 ? 's' : ''; ?>
                        <?php else: ?>
                            <span id="status-text">All notifications are read</span>
                        <?php endif; ?>
                    </p>
                </div>

                <div class="btn-group" role="group">
                    <?php if ($unread_count > 0): ?>
                    <button type="button" class="btn btn-outline-success" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-1"></i>Mark All Read
                    </button>
                    <?php endif; ?>

                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="cleanupOldNotifications()">
                                <i class="fas fa-broom me-2"></i>Cleanup Old
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="clearAllNotifications()">
                                <i class="fas fa-trash me-2"></i>Clear All
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary"><?php echo $stats['total']; ?></h5>
                            <p class="card-text small text-muted">Total</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning"><?php echo $stats['unread']; ?></h5>
                            <p class="card-text small text-muted">Unread</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info"><?php echo $stats['today']; ?></h5>
                            <p class="card-text small text-muted">Today</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success"><?php echo $stats['this_week']; ?></h5>
                            <p class="card-text small text-muted">This Week</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <?php if (empty($notifications)): ?>
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Notifications</h5>
                    <p class="text-muted">You don't have any notifications yet.</p>
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home me-1"></i>Go to Homepage
                    </a>
                </div>
            </div>
            <?php else: ?>
            <div class="notifications-list" id="notifications-container">
                <?php foreach ($notifications as $notification): ?>
                <div class="card mb-3 notification-card <?php echo $notification['is_read'] ? '' : 'border-primary unread'; ?>"
                     data-id="<?php echo $notification['id']; ?>"
                     data-table="<?php echo $notification['source_table']; ?>"
                     data-read="<?php echo $notification['is_read'] ? 'true' : 'false'; ?>">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="notification-icon me-3">
                                        <?php
                                        $icon_class = 'fas fa-info-circle text-info';
                                        $type = $notification['type'] ?? 'info';
                                        
                                        switch ($type) {
                                            case 'order':
                                                $icon_class = 'fas fa-shopping-bag text-success';
                                                break;
                                            case 'payment':
                                                $icon_class = 'fas fa-credit-card text-warning';
                                                break;
                                            case 'shipping':
                                                $icon_class = 'fas fa-truck text-primary';
                                                break;
                                            case 'system':
                                                $icon_class = 'fas fa-cog text-secondary';
                                                break;
                                            case 'promotion':
                                                $icon_class = 'fas fa-tag text-danger';
                                                break;
                                        }
                                        ?>
                                        <i class="<?php echo $icon_class; ?> fa-lg"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 <?php echo $notification['is_read'] ? 'text-muted' : 'text-dark fw-bold'; ?>">
                                            <?php echo htmlspecialchars($notification['title']); ?>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo $notification['formatted_date']; ?>
                                        </small>
                                    </div>
                                </div>
                                
                                <p class="mb-2 <?php echo $notification['is_read'] ? 'text-muted' : 'text-dark'; ?>">
                                    <?php echo nl2br(htmlspecialchars($notification['message'])); ?>
                                </p>
                                
                                <div class="notification-actions mt-2">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <?php if (!$notification['is_read']): ?>
                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                onclick="markAsRead(<?php echo $notification['id']; ?>, '<?php echo $notification['source_table']; ?>')">
                                            <i class="fas fa-check me-1"></i>Mark as Read
                                        </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="deleteNotification(<?php echo $notification['id']; ?>, '<?php echo $notification['source_table']; ?>')">
                                            <i class="fas fa-trash me-1"></i>Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (!$notification['is_read']): ?>
                            <div class="notification-badge">
                                <span class="badge bg-primary rounded-pill">New</span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Back Button -->
            <div class="text-center mt-4">
                <a href="profile.php" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-user me-1"></i>Back to Profile
                </a>
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home me-1"></i>Homepage
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.notifications-list .card {
    transition: all 0.3s ease;
}

.notifications-list .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.notification-icon {
    width: 40px;
    text-align: center;
}

.notification-badge {
    min-width: 60px;
    text-align: center;
}

.border-primary {
    border-left: 4px solid #0d6efd !important;
}
</style>

<script>
// Enhanced Notification Management
document.addEventListener('DOMContentLoaded', function() {
    initializeNotifications();
});

function initializeNotifications() {
    const notificationCards = document.querySelectorAll('.notification-card');

    notificationCards.forEach(card => {
        card.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                return;
            }

            const id = card.dataset.id;
            const table = card.dataset.table;
            const isRead = card.dataset.read === 'true';

            if (!isRead) {
                markAsRead(id, table);
            }
        });
    });
}

function markAsRead(notificationId, table) {
    const formData = new FormData();
    formData.append('ajax_action', 'mark_read');
    formData.append('notification_id', notificationId);
    formData.append('table', table);

    fetch('user_notifications.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const card = document.querySelector(`[data-id="${notificationId}"]`);
            if (card) {
                card.classList.remove('border-primary', 'unread');
                card.dataset.read = 'true';

                const badge = card.querySelector('.notification-badge');
                if (badge) badge.remove();

                const markReadBtn = card.querySelector('button[onclick*="markAsRead"]');
                if (markReadBtn) markReadBtn.remove();
            }

            updateUnreadCount(data.unread_count);
            showToast('Notification marked as read', 'success');
        } else {
            showToast('Failed to mark notification as read', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

function markAllAsRead() {
    if (!confirm('Mark all notifications as read?')) return;

    const formData = new FormData();
    formData.append('ajax_action', 'mark_all_read');

    fetch('user_notifications.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelectorAll('.notification-card.unread').forEach(card => {
                card.classList.remove('border-primary', 'unread');
                card.dataset.read = 'true';

                const badge = card.querySelector('.notification-badge');
                if (badge) badge.remove();

                const markReadBtn = card.querySelector('button[onclick*="markAsRead"]');
                if (markReadBtn) markReadBtn.remove();
            });

            updateUnreadCount(0);
            showToast('All notifications marked as read', 'success');

            const markAllBtn = document.querySelector('button[onclick="markAllAsRead()"]');
            if (markAllBtn) markAllBtn.style.display = 'none';
        } else {
            showToast('Failed to mark all notifications as read', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

function deleteNotification(notificationId, table) {
    if (!confirm('Delete this notification?')) return;

    const formData = new FormData();
    formData.append('ajax_action', 'delete_notification');
    formData.append('notification_id', notificationId);
    formData.append('table', table);

    fetch('user_notifications.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const card = document.querySelector(`[data-id="${notificationId}"]`);
            if (card) {
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '0';
                card.style.transform = 'translateX(100%)';

                setTimeout(() => {
                    card.remove();

                    const remainingCards = document.querySelectorAll('.notification-card');
                    if (remainingCards.length === 0) {
                        location.reload();
                    }
                }, 300);
            }

            updateUnreadCount(data.unread_count);
            showToast('Notification deleted', 'success');
        } else {
            showToast('Failed to delete notification', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

function clearAllNotifications() {
    if (!confirm('This will permanently delete ALL notifications. Are you sure?')) return;

    const formData = new FormData();
    formData.append('ajax_action', 'clear_all');

    fetch('user_notifications.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('All notifications cleared', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('Failed to clear notifications', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

function cleanupOldNotifications() {
    if (!confirm('This will delete old read notifications (older than 30 days). Continue?')) return;

    const formData = new FormData();
    formData.append('ajax_action', 'cleanup_old');

    fetch('user_notifications.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`Cleaned up ${data.cleaned} old notifications`, 'success');
            if (data.cleaned > 0) {
                setTimeout(() => location.reload(), 1000);
            }
        } else {
            showToast('Failed to cleanup notifications', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

function updateUnreadCount(count) {
    const unreadCountBadge = document.getElementById('unread-count');
    const unreadText = document.getElementById('unread-text');
    const statusText = document.getElementById('status-text');

    if (unreadCountBadge) {
        unreadCountBadge.textContent = count;
        if (count === 0) {
            unreadCountBadge.style.display = 'none';
        }
    }

    if (count === 0) {
        const statusContainer = document.querySelector('p.text-muted');
        if (statusContainer) {
            statusContainer.innerHTML = '<span id="status-text">All notifications are read</span>';
        }
    }

    const headerBadge = document.getElementById('headerNotificationBadge');
    if (headerBadge) {
        if (count > 0) {
            headerBadge.textContent = count;
            headerBadge.style.display = 'inline';
        } else {
            headerBadge.style.display = 'none';
        }
    }
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}
</script>

<?php include 'includes/footer.php'; ?>
