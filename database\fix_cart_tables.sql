-- Fix Cart Tables Structure
-- This script will fix missing columns and ensure proper cart functionality

USE db_tewuneed;

-- 1. Check and create carts table if not exists
CREATE TABLE IF NOT EXISTS carts (
    cart_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
);

-- 2. Check and create cart_items table if not exists
CREATE TABLE IF NOT EXISTS cart_items (
    cart_item_id INT AUTO_INCREMENT PRIMARY KEY,
    cart_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_cart_product (cart_id, product_id),
    INDEX idx_cart_id (cart_id),
    INDEX idx_product_id (product_id),
    FOREIGN KEY (cart_id) REFERENCES carts(cart_id) ON DELETE CASCADE
);

-- 3. Add missing columns if they don't exist
-- Add added_at column to cart_items if missing
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE cart_items ADD COLUMN added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'SELECT "added_at column already exists" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'db_tewuneed' 
    AND TABLE_NAME = 'cart_items' 
    AND COLUMN_NAME = 'added_at'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add updated_at column to cart_items if missing
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE cart_items ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
        'SELECT "updated_at column already exists" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'db_tewuneed' 
    AND TABLE_NAME = 'cart_items' 
    AND COLUMN_NAME = 'updated_at'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add created_at column to carts if missing
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE carts ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'SELECT "created_at column already exists" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'db_tewuneed' 
    AND TABLE_NAME = 'carts' 
    AND COLUMN_NAME = 'created_at'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add updated_at column to carts if missing
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE carts ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
        'SELECT "updated_at column already exists" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'db_tewuneed' 
    AND TABLE_NAME = 'carts' 
    AND COLUMN_NAME = 'updated_at'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. Ensure products table has required columns
-- Add is_active column if missing
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE products ADD COLUMN is_active TINYINT(1) DEFAULT 1',
        'SELECT "is_active column already exists" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'db_tewuneed' 
    AND TABLE_NAME = 'products' 
    AND COLUMN_NAME = 'is_active'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add created_at column to products if missing
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE products ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'SELECT "created_at column already exists" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'db_tewuneed' 
    AND TABLE_NAME = 'products' 
    AND COLUMN_NAME = 'created_at'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. Update existing data
-- Set is_active = 1 for all products if column was just added
UPDATE products SET is_active = 1 WHERE is_active IS NULL;

-- 6. Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_product ON cart_items(cart_id, product_id);

-- 7. Show table structures for verification
SHOW CREATE TABLE carts;
SHOW CREATE TABLE cart_items;
SHOW CREATE TABLE products;

-- 8. Test data insertion
INSERT IGNORE INTO carts (user_id) VALUES (1);
SET @test_cart_id = LAST_INSERT_ID();

-- Clean up test data
DELETE FROM cart_items WHERE cart_id = @test_cart_id;
DELETE FROM carts WHERE cart_id = @test_cart_id;

SELECT 'Database schema fix completed successfully!' as status;
